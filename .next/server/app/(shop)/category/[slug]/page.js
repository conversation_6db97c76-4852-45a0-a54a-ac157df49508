/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(shop)/category/[slug]/page";
exports.ids = ["app/(shop)/category/[slug]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&page=%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&appPaths=%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&page=%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&appPaths=%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(shop)',\n        {\n        children: [\n        'category',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(shop)/category/[slug]/page.tsx */ \"(rsc)/./src/app/(shop)/category/[slug]/page.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(shop)/category/[slug]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(shop)/category/[slug]/page\",\n        pathname: \"/category/[slug]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkYoc2hvcCklMkZjYXRlZ29yeSUyRiU1QnNsdWclNUQlMkZwYWdlJnBhZ2U9JTJGKHNob3ApJTJGY2F0ZWdvcnklMkYlNUJzbHVnJTVEJTJGcGFnZSZhcHBQYXRocz0lMkYoc2hvcCklMkZjYXRlZ29yeSUyRiU1QnNsdWclNUQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGKHNob3ApJTJGY2F0ZWdvcnklMkYlNUJzbHVnJTVEJTJGcGFnZS50c3gmYXBwRGlyPSUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1Qiw4TEFBNEc7QUFDbkk7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLDRCQUE0QiwwTkFBZ0Y7QUFDNUc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUF1RjtBQUNoSCxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8/ZGEwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcoc2hvcCknLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdjYXRlZ29yeScsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ1tzbHVnXScsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2FwcC8oc2hvcCkvY2F0ZWdvcnkvW3NsdWddL3BhZ2UudHN4XCIpLCBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2FwcC8oc2hvcCkvY2F0ZWdvcnkvW3NsdWddL3BhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvQXBwbGUvRGVza3RvcC9FbGVjdHJvSHViL3NyYy9hcHAvbGF5b3V0LnRzeFwiKSwgXCIvVXNlcnMvQXBwbGUvRGVza3RvcC9FbGVjdHJvSHViL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9zcmMvYXBwLyhzaG9wKS9jYXRlZ29yeS9bc2x1Z10vcGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi8oc2hvcCkvY2F0ZWdvcnkvW3NsdWddL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvKHNob3ApL2NhdGVnb3J5L1tzbHVnXS9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9jYXRlZ29yeS9bc2x1Z11cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&page=%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&appPaths=%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SkipLink.tsx */ \"(ssr)/./src/components/SkipLink.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRlNraXBMaW5rLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraXBMaW5rJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEc7QUFDMUc7QUFDQSxzS0FBOEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLz9mNWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lwTGlua1wiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FFilterSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FFilterSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/FilterSidebar.tsx */ \"(ssr)/./src/components/FilterSidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRkZpbHRlclNpZGViYXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQTBHO0FBQzFHO0FBQ0EsZ0xBQWtJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8/MDBkOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvRmlsdGVyU2lkZWJhci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FFilterSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/FilterSidebar.tsx":
/*!******************************************!*\
  !*** ./src/components/FilterSidebar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FilterSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction FilterSidebar({ brands }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const sp = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const [brand, setBrand] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(sp.get(\"brand\") || \"\");\n    const [min, setMin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(sp.get(\"min\") || \"\");\n    const [max, setMax] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(sp.get(\"max\") || \"\");\n    function apply() {\n        const q = new URLSearchParams(sp.toString());\n        if (brand) q.set(\"brand\", brand);\n        else q.delete(\"brand\");\n        if (min) q.set(\"min\", min);\n        else q.delete(\"min\");\n        if (max) q.set(\"max\", max);\n        else q.delete(\"max\");\n        router.push(`?${q.toString()}`);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        \"aria-label\": \"Filters\",\n        style: {\n            borderRight: \"1px solid #eee\",\n            paddingRight: \"1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                children: [\n                    \"Brand\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: brand,\n                        onChange: (e)=>setBrand(e.target.value),\n                        \"aria-label\": \"Filter by brand\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"All\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            brands.map((b)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: b,\n                                    children: b\n                                }, b, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 30\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        children: [\n                            \"Min $\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                value: min,\n                                onChange: (e)=>setMin(e.target.value),\n                                inputMode: \"numeric\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        children: [\n                            \"Max $\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                value: max,\n                                onChange: (e)=>setMax(e.target.value),\n                                inputMode: \"numeric\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: apply,\n                children: \"Apply\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FilterSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SkipLink auto */ \nfunction SkipLink() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main\",\n        style: {\n            position: \"absolute\",\n            left: -9999,\n            top: -9999\n        },\n        onFocus: (e)=>{\n            e.currentTarget.style.left = \"8px\";\n            e.currentTarget.style.top = \"8px\";\n            e.currentTarget.style.background = \"white\";\n            e.currentTarget.style.padding = \"8px\";\n        },\n        children: \"Skip to content\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ta2lwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUVDLE1BQUs7UUFBUUMsT0FBTztZQUFFQyxVQUFVO1lBQVlDLE1BQU0sQ0FBQztZQUFNQyxLQUFLLENBQUM7UUFBSztRQUFHQyxTQUFTLENBQUNDO1lBQ2pGQSxFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNFLElBQUksR0FBRztZQUNuREcsRUFBRUMsYUFBYSxDQUF1Qk4sS0FBSyxDQUFDRyxHQUFHLEdBQUc7WUFDbERFLEVBQUVDLGFBQWEsQ0FBdUJOLEtBQUssQ0FBQ08sVUFBVSxHQUFHO1lBQ3pERixFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNRLE9BQU8sR0FBRztRQUN6RDtrQkFBRzs7Ozs7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4Pzk5NTEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2tpcExpbmsoKSB7XG4gIHJldHVybiAoXG4gICAgPGEgaHJlZj1cIiNtYWluXCIgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGxlZnQ6IC05OTk5LCB0b3A6IC05OTk5IH19IG9uRm9jdXM9eyhlKSA9PiB7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS5sZWZ0ID0gJzhweCc7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS50b3AgPSAnOHB4JztcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnQpLnN0eWxlLmJhY2tncm91bmQgPSAnd2hpdGUnO1xuICAgICAgKGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCkuc3R5bGUucGFkZGluZyA9ICc4cHgnO1xuICAgIH19PlNraXAgdG8gY29udGVudDwvYT5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTa2lwTGluayIsImEiLCJocmVmIiwic3R5bGUiLCJwb3NpdGlvbiIsImxlZnQiLCJ0b3AiLCJvbkZvY3VzIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJiYWNrZ3JvdW5kIiwicGFkZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SkipLink.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1cc9da9c511c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYzlkYTljNTExY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(shop)/category/[slug]/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/(shop)/category/[slug]/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ProductCard */ \"(rsc)/./src/components/ProductCard.tsx\");\n/* harmony import */ var _components_FilterSidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/FilterSidebar */ \"(rsc)/./src/components/FilterSidebar.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n// Mock data - same as homepage but organized by category\nconst mockCategories = {\n    \"smartphones\": {\n        name: \"Smartphones\",\n        description: \"Latest smartphones from top brands including iPhone, Samsung Galaxy, Google Pixel, and more.\",\n        icon: \"\\uD83D\\uDCF1\"\n    },\n    \"laptops-computers\": {\n        name: \"Laptops & Computers\",\n        description: \"High-performance laptops, ultrabooks, and desktop computers for work and gaming.\",\n        icon: \"\\uD83D\\uDCBB\"\n    },\n    \"audio-headphones\": {\n        name: \"Audio & Headphones\",\n        description: \"Premium headphones, earbuds, and audio equipment from Sony, Bose, Apple, and more.\",\n        icon: \"\\uD83C\\uDFA7\"\n    },\n    \"gaming-consoles\": {\n        name: \"Gaming & Consoles\",\n        description: \"Gaming consoles, handheld devices, and gaming accessories.\",\n        icon: \"\\uD83C\\uDFAE\"\n    },\n    \"tablets-ereaders\": {\n        name: \"Tablets & E-readers\",\n        description: \"iPads, Android tablets, e-readers, and digital reading devices.\",\n        icon: \"\\uD83D\\uDCF1\"\n    },\n    \"wearables-fitness\": {\n        name: \"Wearables & Fitness\",\n        description: \"Smartwatches, fitness trackers, and health monitoring devices.\",\n        icon: \"⌚\"\n    },\n    \"smart-home-iot\": {\n        name: \"Smart Home & IoT\",\n        description: \"Smart speakers, home automation, security cameras, and IoT devices.\",\n        icon: \"\\uD83C\\uDFE0\"\n    },\n    \"cameras-photography\": {\n        name: \"Cameras & Photography\",\n        description: \"Professional cameras, lenses, action cameras, and photography equipment.\",\n        icon: \"\\uD83D\\uDCF7\"\n    },\n    \"tv-home-theater\": {\n        name: \"TV & Home Theater\",\n        description: \"4K TVs, OLED displays, soundbars, and home entertainment systems.\",\n        icon: \"\\uD83D\\uDCFA\"\n    },\n    \"computer-components\": {\n        name: \"Computer Components\",\n        description: \"Graphics cards, processors, motherboards, and PC building components.\",\n        icon: \"\\uD83D\\uDD27\"\n    },\n    \"monitors-displays\": {\n        name: \"Monitors & Displays\",\n        description: \"4K monitors, gaming displays, professional monitors, and external screens.\",\n        icon: \"\\uD83D\\uDDA5️\"\n    },\n    \"keyboards-mice\": {\n        name: \"Keyboards & Mice\",\n        description: \"Mechanical keyboards, gaming mice, and computer peripherals.\",\n        icon: \"⌨️\"\n    },\n    \"storage-memory\": {\n        name: \"Storage & Memory\",\n        description: \"SSDs, hard drives, RAM, and data storage solutions.\",\n        icon: \"\\uD83D\\uDCBE\"\n    },\n    \"networking-wifi\": {\n        name: \"Networking & WiFi\",\n        description: \"Routers, WiFi extenders, network switches, and connectivity solutions.\",\n        icon: \"\\uD83D\\uDCE1\"\n    },\n    \"mobile-accessories\": {\n        name: \"Mobile Accessories\",\n        description: \"Phone cases, screen protectors, chargers, and mobile accessories.\",\n        icon: \"\\uD83D\\uDCF1\"\n    },\n    \"power-charging\": {\n        name: \"Power & Charging\",\n        description: \"Power banks, wireless chargers, cables, and charging solutions.\",\n        icon: \"\\uD83D\\uDD0B\"\n    },\n    \"drones-rc\": {\n        name: \"Drones & RC\",\n        description: \"Consumer drones, professional UAVs, and remote control devices.\",\n        icon: \"\\uD83D\\uDE81\"\n    },\n    \"vr-ar\": {\n        name: \"VR & AR\",\n        description: \"Virtual reality headsets, augmented reality devices, and immersive technology.\",\n        icon: \"\\uD83E\\uDD7D\"\n    },\n    \"security-surveillance\": {\n        name: \"Security & Surveillance\",\n        description: \"Security cameras, alarm systems, and surveillance equipment.\",\n        icon: \"\\uD83D\\uDD12\"\n    },\n    \"office-electronics\": {\n        name: \"Office Electronics\",\n        description: \"Printers, scanners, projectors, and office technology.\",\n        icon: \"\\uD83D\\uDDA8️\"\n    }\n};\nconst mockProducts = [\n    // SMARTPHONES\n    {\n        id: \"1\",\n        title: \"iPhone 15 Pro Max\",\n        slug: \"iphone-15-pro-max\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"2\",\n        title: \"iPhone 15 Pro\",\n        slug: \"iphone-15-pro\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"3\",\n        title: \"Samsung Galaxy S24 Ultra\",\n        slug: \"galaxy-s24-ultra\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"4\",\n        title: \"Samsung Galaxy S24\",\n        slug: \"galaxy-s24\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"5\",\n        title: \"Google Pixel 8 Pro\",\n        slug: \"google-pixel-8-pro\",\n        brand: {\n            name: \"Google\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"6\",\n        title: \"OnePlus 12\",\n        slug: \"oneplus-12\",\n        brand: {\n            name: \"OnePlus\"\n        },\n        variants: [\n            {\n                price: 799.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"7\",\n        title: \"Xiaomi 14 Ultra\",\n        slug: \"xiaomi-14-ultra\",\n        brand: {\n            name: \"Xiaomi\"\n        },\n        variants: [\n            {\n                price: 699.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    // LAPTOPS\n    {\n        id: \"8\",\n        title: 'MacBook Pro 16\" M3 Max',\n        slug: \"macbook-pro-16-m3-max\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 3499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops-computers\"\n    },\n    {\n        id: \"9\",\n        title: \"MacBook Air M3\",\n        slug: \"macbook-air-m3\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops-computers\"\n    },\n    {\n        id: \"10\",\n        title: \"Dell XPS 13 Plus\",\n        slug: \"dell-xps-13-plus\",\n        brand: {\n            name: \"Dell\"\n        },\n        variants: [\n            {\n                price: 1399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops-computers\"\n    },\n    {\n        id: \"11\",\n        title: \"ThinkPad X1 Carbon\",\n        slug: \"thinkpad-x1-carbon\",\n        brand: {\n            name: \"Lenovo\"\n        },\n        variants: [\n            {\n                price: 1599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops-computers\"\n    },\n    {\n        id: \"12\",\n        title: \"Surface Laptop 5\",\n        slug: \"surface-laptop-5\",\n        brand: {\n            name: \"Microsoft\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops-computers\"\n    },\n    {\n        id: \"13\",\n        title: \"ASUS ROG Zephyrus G14\",\n        slug: \"asus-rog-zephyrus-g14\",\n        brand: {\n            name: \"ASUS\"\n        },\n        variants: [\n            {\n                price: 1899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops-computers\"\n    },\n    {\n        id: \"14\",\n        title: \"HP Spectre x360\",\n        slug: \"hp-spectre-x360\",\n        brand: {\n            name: \"HP\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops-computers\"\n    },\n    // AUDIO\n    {\n        id: \"15\",\n        title: \"AirPods Pro 2\",\n        slug: \"airpods-pro-2\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 249.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio-headphones\"\n    },\n    {\n        id: \"16\",\n        title: \"Sony WH-1000XM5\",\n        slug: \"sony-wh-1000xm5\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio-headphones\"\n    },\n    {\n        id: \"17\",\n        title: \"Bose QuietComfort Ultra\",\n        slug: \"bose-quietcomfort-ultra\",\n        brand: {\n            name: \"Bose\"\n        },\n        variants: [\n            {\n                price: 429.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio-headphones\"\n    },\n    {\n        id: \"18\",\n        title: \"Sennheiser HD 800S\",\n        slug: \"sennheiser-hd-800s\",\n        brand: {\n            name: \"Sennheiser\"\n        },\n        variants: [\n            {\n                price: 1699.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio-headphones\"\n    },\n    {\n        id: \"19\",\n        title: \"Audio-Technica ATH-M50x\",\n        slug: \"audio-technica-ath-m50x\",\n        brand: {\n            name: \"Audio-Technica\"\n        },\n        variants: [\n            {\n                price: 149.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio-headphones\"\n    },\n    {\n        id: \"20\",\n        title: \"Beats Studio Pro\",\n        slug: \"beats-studio-pro\",\n        brand: {\n            name: \"Beats\"\n        },\n        variants: [\n            {\n                price: 349.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio-headphones\"\n    },\n    // Add more products for other categories...\n    {\n        id: \"21\",\n        title: \"PlayStation 5\",\n        slug: \"playstation-5\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"gaming-consoles\"\n    },\n    {\n        id: \"22\",\n        title: \"Xbox Series X\",\n        slug: \"xbox-series-x\",\n        brand: {\n            name: \"Microsoft\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"gaming-consoles\"\n    },\n    {\n        id: \"23\",\n        title: \"Nintendo Switch OLED\",\n        slug: \"nintendo-switch-oled\",\n        brand: {\n            name: \"Nintendo\"\n        },\n        variants: [\n            {\n                price: 349.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"gaming-consoles\"\n    },\n    {\n        id: \"24\",\n        title: \"Steam Deck OLED\",\n        slug: \"steam-deck-oled\",\n        brand: {\n            name: \"Valve\"\n        },\n        variants: [\n            {\n                price: 549.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"gaming-consoles\"\n    }\n];\nfunction CategoryPage({ params, searchParams }) {\n    const categorySlug = params.slug;\n    const category = mockCategories[categorySlug];\n    if (!category) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: \"2rem\",\n                textAlign: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"Category Not Found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        'The category \"',\n                        categorySlug,\n                        \"\\\" doesn't exist.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    href: \"/\",\n                    style: {\n                        color: \"#007bff\",\n                        textDecoration: \"none\"\n                    },\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    // Filter products by category\n    const categoryProducts = mockProducts.filter((p)=>p.category === categorySlug);\n    // Get unique brands for this category\n    const brands = [\n        ...new Set(categoryProducts.map((p)=>p.brand.name))\n    ];\n    // Apply brand filter if specified\n    const filteredProducts = searchParams.brand ? categoryProducts.filter((p)=>p.brand.name.toLowerCase() === searchParams.brand.toLowerCase()) : categoryProducts;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: \"1400px\",\n            margin: \"0 auto\",\n            padding: \"1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n                    color: \"white\",\n                    padding: \"2rem\",\n                    borderRadius: \"12px\",\n                    marginBottom: \"2rem\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"3rem\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        children: category.icon\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"2.5rem\",\n                            margin: \"0 0 1rem 0\"\n                        },\n                        children: category.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: \"1.2rem\",\n                            opacity: 0.9,\n                            maxWidth: \"600px\",\n                            margin: \"0 auto\"\n                        },\n                        children: category.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"1rem\",\n                            fontSize: \"1.1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: filteredProducts.length\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            \" products available\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"280px 1fr\",\n                    gap: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterSidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                brands: brands\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"2rem\",\n                                    padding: \"1.5rem\",\n                                    backgroundColor: \"#f8f9fa\",\n                                    borderRadius: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            marginBottom: \"1rem\"\n                                        },\n                                        children: \"Browse Other Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"0.5rem\"\n                                        },\n                                        children: Object.entries(mockCategories).slice(0, 6).map(([slug, cat])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: `/category/${slug}`,\n                                                style: {\n                                                    textDecoration: \"none\",\n                                                    color: slug === categorySlug ? \"#007bff\" : \"#666\",\n                                                    padding: \"0.5rem\",\n                                                    borderRadius: \"6px\",\n                                                    backgroundColor: slug === categorySlug ? \"#e3f2fd\" : \"transparent\",\n                                                    fontWeight: slug === categorySlug ? \"bold\" : \"normal\"\n                                                },\n                                                children: [\n                                                    cat.icon,\n                                                    \" \",\n                                                    cat.name\n                                                ]\n                                            }, slug, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: filteredProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        justifyContent: \"space-between\",\n                                        alignItems: \"center\",\n                                        marginBottom: \"1.5rem\",\n                                        padding: \"1rem\",\n                                        backgroundColor: \"#f8f9fa\",\n                                        borderRadius: \"8px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: filteredProducts.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" products found\",\n                                                searchParams.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: \"#666\",\n                                                        marginLeft: \"0.5rem\"\n                                                    },\n                                                    children: [\n                                                        'filtered by \"',\n                                                        searchParams.brand,\n                                                        '\"'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"0.9rem\",\n                                                color: \"#666\"\n                                            },\n                                            children: \"Sorted by: Featured\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n                                        gap: \"1.5rem\"\n                                    },\n                                    children: filteredProducts.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            product: p\n                                        }, p.id, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: \"center\",\n                                padding: \"3rem\",\n                                backgroundColor: \"#f8f9fa\",\n                                borderRadius: \"12px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"No products found\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#666\",\n                                        marginBottom: \"1rem\"\n                                    },\n                                    children: \"No products match your current filters.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: `/category/${categorySlug}`,\n                                    style: {\n                                        color: \"#007bff\",\n                                        textDecoration: \"none\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"Clear filters and view all \",\n                                        category.name.toLowerCase()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/(shop)/category/[slug]/page.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhzaG9wKS9jYXRlZ29yeS9bc2x1Z10vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFtRDtBQUNJO0FBQzFCO0FBRTdCLHlEQUF5RDtBQUN6RCxNQUFNRyxpQkFBaUI7SUFDckIsZUFBZTtRQUNiQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EscUJBQXFCO1FBQ25CRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0Esb0JBQW9CO1FBQ2xCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EsbUJBQW1CO1FBQ2pCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0Esb0JBQW9CO1FBQ2xCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EscUJBQXFCO1FBQ25CRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0Esa0JBQWtCO1FBQ2hCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EsdUJBQXVCO1FBQ3JCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EsbUJBQW1CO1FBQ2pCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EsdUJBQXVCO1FBQ3JCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EscUJBQXFCO1FBQ25CRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0Esa0JBQWtCO1FBQ2hCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0Esa0JBQWtCO1FBQ2hCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EsbUJBQW1CO1FBQ2pCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0Esc0JBQXNCO1FBQ3BCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0Esa0JBQWtCO1FBQ2hCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EsYUFBYTtRQUNYRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EsU0FBUztRQUNQRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0EseUJBQXlCO1FBQ3ZCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0lBQ0Esc0JBQXNCO1FBQ3BCRixNQUFNO1FBQ05DLGFBQWE7UUFDYkMsTUFBTTtJQUNSO0FBQ0Y7QUFFQSxNQUFNQyxlQUFlO0lBQ25CLGNBQWM7SUFDZDtRQUFFQyxJQUFJO1FBQUtDLE9BQU87UUFBcUJDLE1BQU07UUFBcUJDLE9BQU87WUFBRVAsTUFBTTtRQUFRO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFjO0lBQzNQO1FBQUVSLElBQUk7UUFBS0MsT0FBTztRQUFpQkMsTUFBTTtRQUFpQkMsT0FBTztZQUFFUCxNQUFNO1FBQVE7UUFBR1EsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQWM7SUFDbFA7UUFBRVIsSUFBSTtRQUFLQyxPQUFPO1FBQTRCQyxNQUFNO1FBQW9CQyxPQUFPO1lBQUVQLE1BQU07UUFBVTtRQUFHUSxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBYztJQUNuUTtRQUFFUixJQUFJO1FBQUtDLE9BQU87UUFBc0JDLE1BQU07UUFBY0MsT0FBTztZQUFFUCxNQUFNO1FBQVU7UUFBR1EsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQWM7SUFDdFA7UUFBRVIsSUFBSTtRQUFLQyxPQUFPO1FBQXNCQyxNQUFNO1FBQXNCQyxPQUFPO1lBQUVQLE1BQU07UUFBUztRQUFHUSxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBYztJQUM3UDtRQUFFUixJQUFJO1FBQUtDLE9BQU87UUFBY0MsTUFBTTtRQUFjQyxPQUFPO1lBQUVQLE1BQU07UUFBVTtRQUFHUSxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBYztJQUM5TztRQUFFUixJQUFJO1FBQUtDLE9BQU87UUFBbUJDLE1BQU07UUFBbUJDLE9BQU87WUFBRVAsTUFBTTtRQUFTO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFjO0lBRXZQLFVBQVU7SUFDVjtRQUFFUixJQUFJO1FBQUtDLE9BQU87UUFBMEJDLE1BQU07UUFBeUJDLE9BQU87WUFBRVAsTUFBTTtRQUFRO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQWlGO1NBQUU7UUFBRUMsVUFBVTtJQUFvQjtJQUN2UTtRQUFFUixJQUFJO1FBQUtDLE9BQU87UUFBa0JDLE1BQU07UUFBa0JDLE9BQU87WUFBRVAsTUFBTTtRQUFRO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQWlGO1NBQUU7UUFBRUMsVUFBVTtJQUFvQjtJQUN4UDtRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBb0JDLE1BQU07UUFBb0JDLE9BQU87WUFBRVAsTUFBTTtRQUFPO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFvQjtJQUMvUDtRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBc0JDLE1BQU07UUFBc0JDLE9BQU87WUFBRVAsTUFBTTtRQUFTO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFvQjtJQUNyUTtRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBb0JDLE1BQU07UUFBb0JDLE9BQU87WUFBRVAsTUFBTTtRQUFZO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFvQjtJQUNwUTtRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBeUJDLE1BQU07UUFBeUJDLE9BQU87WUFBRVAsTUFBTTtRQUFPO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFvQjtJQUN6UTtRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBbUJDLE1BQU07UUFBbUJDLE9BQU87WUFBRVAsTUFBTTtRQUFLO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFvQjtJQUUzUCxRQUFRO0lBQ1I7UUFBRVIsSUFBSTtRQUFNQyxPQUFPO1FBQWlCQyxNQUFNO1FBQWlCQyxPQUFPO1lBQUVQLE1BQU07UUFBUTtRQUFHUSxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBbUI7SUFDeFA7UUFBRVIsSUFBSTtRQUFNQyxPQUFPO1FBQW1CQyxNQUFNO1FBQW1CQyxPQUFPO1lBQUVQLE1BQU07UUFBTztRQUFHUSxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBbUI7SUFDM1A7UUFBRVIsSUFBSTtRQUFNQyxPQUFPO1FBQTJCQyxNQUFNO1FBQTJCQyxPQUFPO1lBQUVQLE1BQU07UUFBTztRQUFHUSxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBbUI7SUFDM1E7UUFBRVIsSUFBSTtRQUFNQyxPQUFPO1FBQXNCQyxNQUFNO1FBQXNCQyxPQUFPO1lBQUVQLE1BQU07UUFBYTtRQUFHUSxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBbUI7SUFDeFE7UUFBRVIsSUFBSTtRQUFNQyxPQUFPO1FBQTJCQyxNQUFNO1FBQTJCQyxPQUFPO1lBQUVQLE1BQU07UUFBaUI7UUFBR1EsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQW1CO0lBQ3JSO1FBQUVSLElBQUk7UUFBTUMsT0FBTztRQUFvQkMsTUFBTTtRQUFvQkMsT0FBTztZQUFFUCxNQUFNO1FBQVE7UUFBR1EsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQW1CO0lBRTlQLDRDQUE0QztJQUM1QztRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBaUJDLE1BQU07UUFBaUJDLE9BQU87WUFBRVAsTUFBTTtRQUFPO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFrQjtJQUN0UDtRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBaUJDLE1BQU07UUFBaUJDLE9BQU87WUFBRVAsTUFBTTtRQUFZO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFrQjtJQUMzUDtRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBd0JDLE1BQU07UUFBd0JDLE9BQU87WUFBRVAsTUFBTTtRQUFXO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFrQjtJQUN4UTtRQUFFUixJQUFJO1FBQU1DLE9BQU87UUFBbUJDLE1BQU07UUFBbUJDLE9BQU87WUFBRVAsTUFBTTtRQUFRO1FBQUdRLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFrQjtDQUM1UDtBQUVjLFNBQVNDLGFBQWEsRUFBRUMsTUFBTSxFQUFFQyxZQUFZLEVBQU87SUFDaEUsTUFBTUMsZUFBZUYsT0FBT1IsSUFBSTtJQUNoQyxNQUFNTSxXQUFXYixjQUFjLENBQUNpQixhQUE0QztJQUU1RSxJQUFJLENBQUNKLFVBQVU7UUFDYixxQkFDRSw4REFBQ0s7WUFBSUMsT0FBTztnQkFBRUMsU0FBUztnQkFBUUMsV0FBVztZQUFTOzs4QkFDakQsOERBQUNDOzhCQUFHOzs7Ozs7OEJBQ0osOERBQUNDOzt3QkFBRTt3QkFBZU47d0JBQWE7Ozs7Ozs7OEJBQy9CLDhEQUFDbEIsaURBQUlBO29CQUFDeUIsTUFBSztvQkFBSUwsT0FBTzt3QkFBRU0sT0FBTzt3QkFBV0MsZ0JBQWdCO29CQUFPOzhCQUFHOzs7Ozs7Ozs7Ozs7SUFHMUU7SUFFQSw4QkFBOEI7SUFDOUIsTUFBTUMsbUJBQW1CdkIsYUFBYXdCLE1BQU0sQ0FBQ0wsQ0FBQUEsSUFBS0EsRUFBRVYsUUFBUSxLQUFLSTtJQUVqRSxzQ0FBc0M7SUFDdEMsTUFBTVksU0FBUztXQUFJLElBQUlDLElBQUlILGlCQUFpQkksR0FBRyxDQUFDUixDQUFBQSxJQUFLQSxFQUFFZixLQUFLLENBQUNQLElBQUk7S0FBRztJQUVwRSxrQ0FBa0M7SUFDbEMsTUFBTStCLG1CQUFtQmhCLGFBQWFSLEtBQUssR0FDdkNtQixpQkFBaUJDLE1BQU0sQ0FBQ0wsQ0FBQUEsSUFBS0EsRUFBRWYsS0FBSyxDQUFDUCxJQUFJLENBQUNnQyxXQUFXLE9BQU9qQixhQUFhUixLQUFLLENBQUN5QixXQUFXLE1BQzFGTjtJQUVKLHFCQUNFLDhEQUFDVDtRQUFJQyxPQUFPO1lBQUVlLFVBQVU7WUFBVUMsUUFBUTtZQUFVZixTQUFTO1FBQU87OzBCQUVsRSw4REFBQ0Y7Z0JBQUlDLE9BQU87b0JBQ1ZpQixZQUFZO29CQUNaWCxPQUFPO29CQUNQTCxTQUFTO29CQUNUaUIsY0FBYztvQkFDZEMsY0FBYztvQkFDZGpCLFdBQVc7Z0JBQ2I7O2tDQUNFLDhEQUFDSDt3QkFBSUMsT0FBTzs0QkFBRW9CLFVBQVU7NEJBQVFELGNBQWM7d0JBQVM7a0NBQUl6QixTQUFTVixJQUFJOzs7Ozs7a0NBQ3hFLDhEQUFDbUI7d0JBQUdILE9BQU87NEJBQUVvQixVQUFVOzRCQUFVSixRQUFRO3dCQUFhO2tDQUFJdEIsU0FBU1osSUFBSTs7Ozs7O2tDQUN2RSw4REFBQ3NCO3dCQUFFSixPQUFPOzRCQUFFb0IsVUFBVTs0QkFBVUMsU0FBUzs0QkFBS04sVUFBVTs0QkFBU0MsUUFBUTt3QkFBUztrQ0FDL0V0QixTQUFTWCxXQUFXOzs7Ozs7a0NBRXZCLDhEQUFDZ0I7d0JBQUlDLE9BQU87NEJBQUVzQixXQUFXOzRCQUFRRixVQUFVO3dCQUFTOzswQ0FDbEQsOERBQUNHOzBDQUFRVixpQkFBaUJXLE1BQU07Ozs7Ozs0QkFBVTs7Ozs7Ozs7Ozs7OzswQkFJOUMsOERBQUN6QjtnQkFBSUMsT0FBTztvQkFBRXlCLFNBQVM7b0JBQVFDLHFCQUFxQjtvQkFBYUMsS0FBSztnQkFBTzs7a0NBRTNFLDhEQUFDNUI7OzBDQUNDLDhEQUFDcEIsaUVBQWFBO2dDQUFDK0IsUUFBUUE7Ozs7OzswQ0FHdkIsOERBQUNYO2dDQUFJQyxPQUFPO29DQUNWc0IsV0FBVztvQ0FDWHJCLFNBQVM7b0NBQ1QyQixpQkFBaUI7b0NBQ2pCVixjQUFjO2dDQUNoQjs7a0RBQ0UsOERBQUNXO3dDQUFHN0IsT0FBTzs0Q0FBRW1CLGNBQWM7d0NBQU87a0RBQUc7Ozs7OztrREFDckMsOERBQUNwQjt3Q0FBSUMsT0FBTzs0Q0FBRXlCLFNBQVM7NENBQVFLLGVBQWU7NENBQVVILEtBQUs7d0NBQVM7a0RBQ25FSSxPQUFPQyxPQUFPLENBQUNuRCxnQkFBZ0JvRCxLQUFLLENBQUMsR0FBRyxHQUFHckIsR0FBRyxDQUFDLENBQUMsQ0FBQ3hCLE1BQU04QyxJQUFJLGlCQUMxRCw4REFBQ3RELGlEQUFJQTtnREFFSHlCLE1BQU0sQ0FBQyxVQUFVLEVBQUVqQixLQUFLLENBQUM7Z0RBQ3pCWSxPQUFPO29EQUNMTyxnQkFBZ0I7b0RBQ2hCRCxPQUFPbEIsU0FBU1UsZUFBZSxZQUFZO29EQUMzQ0csU0FBUztvREFDVGlCLGNBQWM7b0RBQ2RVLGlCQUFpQnhDLFNBQVNVLGVBQWUsWUFBWTtvREFDckRxQyxZQUFZL0MsU0FBU1UsZUFBZSxTQUFTO2dEQUMvQzs7b0RBRUNvQyxJQUFJbEQsSUFBSTtvREFBQztvREFBRWtELElBQUlwRCxJQUFJOzsrQ0FYZk07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBbUJmLDhEQUFDVztrQ0FDRWMsaUJBQWlCVyxNQUFNLEdBQUcsa0JBQ3pCOzs4Q0FDRSw4REFBQ3pCO29DQUFJQyxPQUFPO3dDQUNWeUIsU0FBUzt3Q0FDVFcsZ0JBQWdCO3dDQUNoQkMsWUFBWTt3Q0FDWmxCLGNBQWM7d0NBQ2RsQixTQUFTO3dDQUNUMkIsaUJBQWlCO3dDQUNqQlYsY0FBYztvQ0FDaEI7O3NEQUNFLDhEQUFDbkI7OzhEQUNDLDhEQUFDd0I7OERBQVFWLGlCQUFpQlcsTUFBTTs7Ozs7O2dEQUFVO2dEQUN6QzNCLGFBQWFSLEtBQUssa0JBQ2pCLDhEQUFDaUQ7b0RBQUt0QyxPQUFPO3dEQUFFTSxPQUFPO3dEQUFRaUMsWUFBWTtvREFBUzs7d0RBQUc7d0RBQ3RDMUMsYUFBYVIsS0FBSzt3REFBQzs7Ozs7Ozs7Ozs7OztzREFJdkMsOERBQUNVOzRDQUFJQyxPQUFPO2dEQUFFb0IsVUFBVTtnREFBVWQsT0FBTzs0Q0FBTztzREFBRzs7Ozs7Ozs7Ozs7OzhDQUtyRCw4REFBQ1A7b0NBQUlDLE9BQU87d0NBQ1Z5QixTQUFTO3dDQUNUQyxxQkFBcUI7d0NBQ3JCQyxLQUFLO29DQUNQOzhDQUNHZCxpQkFBaUJELEdBQUcsQ0FBQ1IsQ0FBQUEsa0JBQ3BCLDhEQUFDMUIsK0RBQVdBOzRDQUFZOEQsU0FBU3BDOzJDQUFmQSxFQUFFbEIsRUFBRTs7Ozs7Ozs7Ozs7eURBSzVCLDhEQUFDYTs0QkFBSUMsT0FBTztnQ0FDVkUsV0FBVztnQ0FDWEQsU0FBUztnQ0FDVDJCLGlCQUFpQjtnQ0FDakJWLGNBQWM7NEJBQ2hCOzs4Q0FDRSw4REFBQ1c7OENBQUc7Ozs7Ozs4Q0FDSiw4REFBQ3pCO29DQUFFSixPQUFPO3dDQUFFTSxPQUFPO3dDQUFRYSxjQUFjO29DQUFPOzhDQUFHOzs7Ozs7OENBR25ELDhEQUFDdkMsaURBQUlBO29DQUNIeUIsTUFBTSxDQUFDLFVBQVUsRUFBRVAsYUFBYSxDQUFDO29DQUNqQ0UsT0FBTzt3Q0FDTE0sT0FBTzt3Q0FDUEMsZ0JBQWdCO3dDQUNoQjRCLFlBQVk7b0NBQ2Q7O3dDQUNEO3dDQUM2QnpDLFNBQVNaLElBQUksQ0FBQ2dDLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFyRSIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZWN0cm9odWItbWFya2V0cGxhY2UvLi9zcmMvYXBwLyhzaG9wKS9jYXRlZ29yeS9bc2x1Z10vcGFnZS50c3g/OTVlNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUHJvZHVjdENhcmQgZnJvbSAnQC9jb21wb25lbnRzL1Byb2R1Y3RDYXJkJztcbmltcG9ydCBGaWx0ZXJTaWRlYmFyIGZyb20gJ0AvY29tcG9uZW50cy9GaWx0ZXJTaWRlYmFyJztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5cbi8vIE1vY2sgZGF0YSAtIHNhbWUgYXMgaG9tZXBhZ2UgYnV0IG9yZ2FuaXplZCBieSBjYXRlZ29yeVxuY29uc3QgbW9ja0NhdGVnb3JpZXMgPSB7XG4gICdzbWFydHBob25lcyc6IHtcbiAgICBuYW1lOiAnU21hcnRwaG9uZXMnLFxuICAgIGRlc2NyaXB0aW9uOiAnTGF0ZXN0IHNtYXJ0cGhvbmVzIGZyb20gdG9wIGJyYW5kcyBpbmNsdWRpbmcgaVBob25lLCBTYW1zdW5nIEdhbGF4eSwgR29vZ2xlIFBpeGVsLCBhbmQgbW9yZS4nLFxuICAgIGljb246ICfwn5OxJ1xuICB9LFxuICAnbGFwdG9wcy1jb21wdXRlcnMnOiB7XG4gICAgbmFtZTogJ0xhcHRvcHMgJiBDb21wdXRlcnMnLFxuICAgIGRlc2NyaXB0aW9uOiAnSGlnaC1wZXJmb3JtYW5jZSBsYXB0b3BzLCB1bHRyYWJvb2tzLCBhbmQgZGVza3RvcCBjb21wdXRlcnMgZm9yIHdvcmsgYW5kIGdhbWluZy4nLFxuICAgIGljb246ICfwn5K7J1xuICB9LFxuICAnYXVkaW8taGVhZHBob25lcyc6IHtcbiAgICBuYW1lOiAnQXVkaW8gJiBIZWFkcGhvbmVzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1ByZW1pdW0gaGVhZHBob25lcywgZWFyYnVkcywgYW5kIGF1ZGlvIGVxdWlwbWVudCBmcm9tIFNvbnksIEJvc2UsIEFwcGxlLCBhbmQgbW9yZS4nLFxuICAgIGljb246ICfwn46nJ1xuICB9LFxuICAnZ2FtaW5nLWNvbnNvbGVzJzoge1xuICAgIG5hbWU6ICdHYW1pbmcgJiBDb25zb2xlcycsXG4gICAgZGVzY3JpcHRpb246ICdHYW1pbmcgY29uc29sZXMsIGhhbmRoZWxkIGRldmljZXMsIGFuZCBnYW1pbmcgYWNjZXNzb3JpZXMuJyxcbiAgICBpY29uOiAn8J+OridcbiAgfSxcbiAgJ3RhYmxldHMtZXJlYWRlcnMnOiB7XG4gICAgbmFtZTogJ1RhYmxldHMgJiBFLXJlYWRlcnMnLFxuICAgIGRlc2NyaXB0aW9uOiAnaVBhZHMsIEFuZHJvaWQgdGFibGV0cywgZS1yZWFkZXJzLCBhbmQgZGlnaXRhbCByZWFkaW5nIGRldmljZXMuJyxcbiAgICBpY29uOiAn8J+TsSdcbiAgfSxcbiAgJ3dlYXJhYmxlcy1maXRuZXNzJzoge1xuICAgIG5hbWU6ICdXZWFyYWJsZXMgJiBGaXRuZXNzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1NtYXJ0d2F0Y2hlcywgZml0bmVzcyB0cmFja2VycywgYW5kIGhlYWx0aCBtb25pdG9yaW5nIGRldmljZXMuJyxcbiAgICBpY29uOiAn4oyaJ1xuICB9LFxuICAnc21hcnQtaG9tZS1pb3QnOiB7XG4gICAgbmFtZTogJ1NtYXJ0IEhvbWUgJiBJb1QnLFxuICAgIGRlc2NyaXB0aW9uOiAnU21hcnQgc3BlYWtlcnMsIGhvbWUgYXV0b21hdGlvbiwgc2VjdXJpdHkgY2FtZXJhcywgYW5kIElvVCBkZXZpY2VzLicsXG4gICAgaWNvbjogJ/Cfj6AnXG4gIH0sXG4gICdjYW1lcmFzLXBob3RvZ3JhcGh5Jzoge1xuICAgIG5hbWU6ICdDYW1lcmFzICYgUGhvdG9ncmFwaHknLFxuICAgIGRlc2NyaXB0aW9uOiAnUHJvZmVzc2lvbmFsIGNhbWVyYXMsIGxlbnNlcywgYWN0aW9uIGNhbWVyYXMsIGFuZCBwaG90b2dyYXBoeSBlcXVpcG1lbnQuJyxcbiAgICBpY29uOiAn8J+TtydcbiAgfSxcbiAgJ3R2LWhvbWUtdGhlYXRlcic6IHtcbiAgICBuYW1lOiAnVFYgJiBIb21lIFRoZWF0ZXInLFxuICAgIGRlc2NyaXB0aW9uOiAnNEsgVFZzLCBPTEVEIGRpc3BsYXlzLCBzb3VuZGJhcnMsIGFuZCBob21lIGVudGVydGFpbm1lbnQgc3lzdGVtcy4nLFxuICAgIGljb246ICfwn5O6J1xuICB9LFxuICAnY29tcHV0ZXItY29tcG9uZW50cyc6IHtcbiAgICBuYW1lOiAnQ29tcHV0ZXIgQ29tcG9uZW50cycsXG4gICAgZGVzY3JpcHRpb246ICdHcmFwaGljcyBjYXJkcywgcHJvY2Vzc29ycywgbW90aGVyYm9hcmRzLCBhbmQgUEMgYnVpbGRpbmcgY29tcG9uZW50cy4nLFxuICAgIGljb246ICfwn5SnJ1xuICB9LFxuICAnbW9uaXRvcnMtZGlzcGxheXMnOiB7XG4gICAgbmFtZTogJ01vbml0b3JzICYgRGlzcGxheXMnLFxuICAgIGRlc2NyaXB0aW9uOiAnNEsgbW9uaXRvcnMsIGdhbWluZyBkaXNwbGF5cywgcHJvZmVzc2lvbmFsIG1vbml0b3JzLCBhbmQgZXh0ZXJuYWwgc2NyZWVucy4nLFxuICAgIGljb246ICfwn5al77iPJ1xuICB9LFxuICAna2V5Ym9hcmRzLW1pY2UnOiB7XG4gICAgbmFtZTogJ0tleWJvYXJkcyAmIE1pY2UnLFxuICAgIGRlc2NyaXB0aW9uOiAnTWVjaGFuaWNhbCBrZXlib2FyZHMsIGdhbWluZyBtaWNlLCBhbmQgY29tcHV0ZXIgcGVyaXBoZXJhbHMuJyxcbiAgICBpY29uOiAn4oyo77iPJ1xuICB9LFxuICAnc3RvcmFnZS1tZW1vcnknOiB7XG4gICAgbmFtZTogJ1N0b3JhZ2UgJiBNZW1vcnknLFxuICAgIGRlc2NyaXB0aW9uOiAnU1NEcywgaGFyZCBkcml2ZXMsIFJBTSwgYW5kIGRhdGEgc3RvcmFnZSBzb2x1dGlvbnMuJyxcbiAgICBpY29uOiAn8J+SvidcbiAgfSxcbiAgJ25ldHdvcmtpbmctd2lmaSc6IHtcbiAgICBuYW1lOiAnTmV0d29ya2luZyAmIFdpRmknLFxuICAgIGRlc2NyaXB0aW9uOiAnUm91dGVycywgV2lGaSBleHRlbmRlcnMsIG5ldHdvcmsgc3dpdGNoZXMsIGFuZCBjb25uZWN0aXZpdHkgc29sdXRpb25zLicsXG4gICAgaWNvbjogJ/Cfk6EnXG4gIH0sXG4gICdtb2JpbGUtYWNjZXNzb3JpZXMnOiB7XG4gICAgbmFtZTogJ01vYmlsZSBBY2Nlc3NvcmllcycsXG4gICAgZGVzY3JpcHRpb246ICdQaG9uZSBjYXNlcywgc2NyZWVuIHByb3RlY3RvcnMsIGNoYXJnZXJzLCBhbmQgbW9iaWxlIGFjY2Vzc29yaWVzLicsXG4gICAgaWNvbjogJ/Cfk7EnXG4gIH0sXG4gICdwb3dlci1jaGFyZ2luZyc6IHtcbiAgICBuYW1lOiAnUG93ZXIgJiBDaGFyZ2luZycsXG4gICAgZGVzY3JpcHRpb246ICdQb3dlciBiYW5rcywgd2lyZWxlc3MgY2hhcmdlcnMsIGNhYmxlcywgYW5kIGNoYXJnaW5nIHNvbHV0aW9ucy4nLFxuICAgIGljb246ICfwn5SLJ1xuICB9LFxuICAnZHJvbmVzLXJjJzoge1xuICAgIG5hbWU6ICdEcm9uZXMgJiBSQycsXG4gICAgZGVzY3JpcHRpb246ICdDb25zdW1lciBkcm9uZXMsIHByb2Zlc3Npb25hbCBVQVZzLCBhbmQgcmVtb3RlIGNvbnRyb2wgZGV2aWNlcy4nLFxuICAgIGljb246ICfwn5qBJ1xuICB9LFxuICAndnItYXInOiB7XG4gICAgbmFtZTogJ1ZSICYgQVInLFxuICAgIGRlc2NyaXB0aW9uOiAnVmlydHVhbCByZWFsaXR5IGhlYWRzZXRzLCBhdWdtZW50ZWQgcmVhbGl0eSBkZXZpY2VzLCBhbmQgaW1tZXJzaXZlIHRlY2hub2xvZ3kuJyxcbiAgICBpY29uOiAn8J+lvSdcbiAgfSxcbiAgJ3NlY3VyaXR5LXN1cnZlaWxsYW5jZSc6IHtcbiAgICBuYW1lOiAnU2VjdXJpdHkgJiBTdXJ2ZWlsbGFuY2UnLFxuICAgIGRlc2NyaXB0aW9uOiAnU2VjdXJpdHkgY2FtZXJhcywgYWxhcm0gc3lzdGVtcywgYW5kIHN1cnZlaWxsYW5jZSBlcXVpcG1lbnQuJyxcbiAgICBpY29uOiAn8J+UkidcbiAgfSxcbiAgJ29mZmljZS1lbGVjdHJvbmljcyc6IHtcbiAgICBuYW1lOiAnT2ZmaWNlIEVsZWN0cm9uaWNzJyxcbiAgICBkZXNjcmlwdGlvbjogJ1ByaW50ZXJzLCBzY2FubmVycywgcHJvamVjdG9ycywgYW5kIG9mZmljZSB0ZWNobm9sb2d5LicsXG4gICAgaWNvbjogJ/CflqjvuI8nXG4gIH1cbn07XG5cbmNvbnN0IG1vY2tQcm9kdWN0cyA9IFtcbiAgLy8gU01BUlRQSE9ORVNcbiAgeyBpZDogJzEnLCB0aXRsZTogJ2lQaG9uZSAxNSBQcm8gTWF4Jywgc2x1ZzogJ2lwaG9uZS0xNS1wcm8tbWF4JywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDExOTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU5Mjc1MDQ3NTMzOC03NGI3YjIxMDg1YWI/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3NtYXJ0cGhvbmVzJyB9LFxuICB7IGlkOiAnMicsIHRpdGxlOiAnaVBob25lIDE1IFBybycsIHNsdWc6ICdpcGhvbmUtMTUtcHJvJywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDk5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTkyNzUwNDc1MzM4LTc0YjdiMjEwODVhYj93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnc21hcnRwaG9uZXMnIH0sXG4gIHsgaWQ6ICczJywgdGl0bGU6ICdTYW1zdW5nIEdhbGF4eSBTMjQgVWx0cmEnLCBzbHVnOiAnZ2FsYXh5LXMyNC11bHRyYScsIGJyYW5kOiB7IG5hbWU6ICdTYW1zdW5nJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDEyOTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUxMTcwNzE3MTYzNC01Zjg5N2ZmMDJhYTk/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3NtYXJ0cGhvbmVzJyB9LFxuICB7IGlkOiAnNCcsIHRpdGxlOiAnU2Ftc3VuZyBHYWxheHkgUzI0Jywgc2x1ZzogJ2dhbGF4eS1zMjQnLCBicmFuZDogeyBuYW1lOiAnU2Ftc3VuZycgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA4OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUxMTcwNzE3MTYzNC01Zjg5N2ZmMDJhYTk/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3NtYXJ0cGhvbmVzJyB9LFxuICB7IGlkOiAnNScsIHRpdGxlOiAnR29vZ2xlIFBpeGVsIDggUHJvJywgc2x1ZzogJ2dvb2dsZS1waXhlbC04LXBybycsIGJyYW5kOiB7IG5hbWU6ICdHb29nbGUnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogOTk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MTE3MDcxNzE2MzQtNWY4OTdmZjAyYWE5P3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdzbWFydHBob25lcycgfSxcbiAgeyBpZDogJzYnLCB0aXRsZTogJ09uZVBsdXMgMTInLCBzbHVnOiAnb25lcGx1cy0xMicsIGJyYW5kOiB7IG5hbWU6ICdPbmVQbHVzJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDc5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTExNzA3MTcxNjM0LTVmODk3ZmYwMmFhOT93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnc21hcnRwaG9uZXMnIH0sXG4gIHsgaWQ6ICc3JywgdGl0bGU6ICdYaWFvbWkgMTQgVWx0cmEnLCBzbHVnOiAneGlhb21pLTE0LXVsdHJhJywgYnJhbmQ6IHsgbmFtZTogJ1hpYW9taScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA2OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUxMTcwNzE3MTYzNC01Zjg5N2ZmMDJhYTk/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3NtYXJ0cGhvbmVzJyB9LFxuXG4gIC8vIExBUFRPUFNcbiAgeyBpZDogJzgnLCB0aXRsZTogJ01hY0Jvb2sgUHJvIDE2XCIgTTMgTWF4Jywgc2x1ZzogJ21hY2Jvb2stcHJvLTE2LW0zLW1heCcsIGJyYW5kOiB7IG5hbWU6ICdBcHBsZScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAzNDk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NDE4MDcwODQtNWM1MmI2YjNhZGVmP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdsYXB0b3BzLWNvbXB1dGVycycgfSxcbiAgeyBpZDogJzknLCB0aXRsZTogJ01hY0Jvb2sgQWlyIE0zJywgc2x1ZzogJ21hY2Jvb2stYWlyLW0zJywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDEyOTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU0MTgwNzA4NC01YzUyYjZiM2FkZWY/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2xhcHRvcHMtY29tcHV0ZXJzJyB9LFxuICB7IGlkOiAnMTAnLCB0aXRsZTogJ0RlbGwgWFBTIDEzIFBsdXMnLCBzbHVnOiAnZGVsbC14cHMtMTMtcGx1cycsIGJyYW5kOiB7IG5hbWU6ICdEZWxsJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDEzOTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ5NjE4MTEzMzIwNi04MGNlOWI4OGE4NTM/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2xhcHRvcHMtY29tcHV0ZXJzJyB9LFxuICB7IGlkOiAnMTEnLCB0aXRsZTogJ1RoaW5rUGFkIFgxIENhcmJvbicsIHNsdWc6ICd0aGlua3BhZC14MS1jYXJib24nLCBicmFuZDogeyBuYW1lOiAnTGVub3ZvJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDE1OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ5NjE4MTEzMzIwNi04MGNlOWI4OGE4NTM/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2xhcHRvcHMtY29tcHV0ZXJzJyB9LFxuICB7IGlkOiAnMTInLCB0aXRsZTogJ1N1cmZhY2UgTGFwdG9wIDUnLCBzbHVnOiAnc3VyZmFjZS1sYXB0b3AtNScsIGJyYW5kOiB7IG5hbWU6ICdNaWNyb3NvZnQnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTI5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDk2MTgxMTMzMjA2LTgwY2U5Yjg4YTg1Mz93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnbGFwdG9wcy1jb21wdXRlcnMnIH0sXG4gIHsgaWQ6ICcxMycsIHRpdGxlOiAnQVNVUyBST0cgWmVwaHlydXMgRzE0Jywgc2x1ZzogJ2FzdXMtcm9nLXplcGh5cnVzLWcxNCcsIGJyYW5kOiB7IG5hbWU6ICdBU1VTJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDE4OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ5NjE4MTEzMzIwNi04MGNlOWI4OGE4NTM/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2xhcHRvcHMtY29tcHV0ZXJzJyB9LFxuICB7IGlkOiAnMTQnLCB0aXRsZTogJ0hQIFNwZWN0cmUgeDM2MCcsIHNsdWc6ICdocC1zcGVjdHJlLXgzNjAnLCBicmFuZDogeyBuYW1lOiAnSFAnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTE5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDk2MTgxMTMzMjA2LTgwY2U5Yjg4YTg1Mz93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnbGFwdG9wcy1jb21wdXRlcnMnIH0sXG5cbiAgLy8gQVVESU9cbiAgeyBpZDogJzE1JywgdGl0bGU6ICdBaXJQb2RzIFBybyAyJywgc2x1ZzogJ2FpcnBvZHMtcHJvLTInLCBicmFuZDogeyBuYW1lOiAnQXBwbGUnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMjQ5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MDYyMjA5NDU3NzAtYjViNmMyYzU1YmYxP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdhdWRpby1oZWFkcGhvbmVzJyB9LFxuICB7IGlkOiAnMTYnLCB0aXRsZTogJ1NvbnkgV0gtMTAwMFhNNScsIHNsdWc6ICdzb255LXdoLTEwMDB4bTUnLCBicmFuZDogeyBuYW1lOiAnU29ueScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAzOTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNTc0MDQyMDkyOC01ZTU2MGMwNmQzMGU/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2F1ZGlvLWhlYWRwaG9uZXMnIH0sXG4gIHsgaWQ6ICcxNycsIHRpdGxlOiAnQm9zZSBRdWlldENvbWZvcnQgVWx0cmEnLCBzbHVnOiAnYm9zZS1xdWlldGNvbWZvcnQtdWx0cmEnLCBicmFuZDogeyBuYW1lOiAnQm9zZScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA0MjkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNTc0MDQyMDkyOC01ZTU2MGMwNmQzMGU/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2F1ZGlvLWhlYWRwaG9uZXMnIH0sXG4gIHsgaWQ6ICcxOCcsIHRpdGxlOiAnU2VubmhlaXNlciBIRCA4MDBTJywgc2x1ZzogJ3Nlbm5oZWlzZXItaGQtODAwcycsIGJyYW5kOiB7IG5hbWU6ICdTZW5uaGVpc2VyJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDE2OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNTc0MDQyMDkyOC01ZTU2MGMwNmQzMGU/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2F1ZGlvLWhlYWRwaG9uZXMnIH0sXG4gIHsgaWQ6ICcxOScsIHRpdGxlOiAnQXVkaW8tVGVjaG5pY2EgQVRILU01MHgnLCBzbHVnOiAnYXVkaW8tdGVjaG5pY2EtYXRoLW01MHgnLCBicmFuZDogeyBuYW1lOiAnQXVkaW8tVGVjaG5pY2EnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTQ5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MDU3NDA0MjA5MjgtNWU1NjBjMDZkMzBlP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdhdWRpby1oZWFkcGhvbmVzJyB9LFxuICB7IGlkOiAnMjAnLCB0aXRsZTogJ0JlYXRzIFN0dWRpbyBQcm8nLCBzbHVnOiAnYmVhdHMtc3R1ZGlvLXBybycsIGJyYW5kOiB7IG5hbWU6ICdCZWF0cycgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAzNDkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNTc0MDQyMDkyOC01ZTU2MGMwNmQzMGU/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2F1ZGlvLWhlYWRwaG9uZXMnIH0sXG5cbiAgLy8gQWRkIG1vcmUgcHJvZHVjdHMgZm9yIG90aGVyIGNhdGVnb3JpZXMuLi5cbiAgeyBpZDogJzIxJywgdGl0bGU6ICdQbGF5U3RhdGlvbiA1Jywgc2x1ZzogJ3BsYXlzdGF0aW9uLTUnLCBicmFuZDogeyBuYW1lOiAnU29ueScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA0OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTYwNjE0NDA0MjYxNC1iMjQxN2U5OWM0ZTM/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2dhbWluZy1jb25zb2xlcycgfSxcbiAgeyBpZDogJzIyJywgdGl0bGU6ICdYYm94IFNlcmllcyBYJywgc2x1ZzogJ3hib3gtc2VyaWVzLXgnLCBicmFuZDogeyBuYW1lOiAnTWljcm9zb2Z0JyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDQ5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNjA2MTQ0MDQyNjE0LWIyNDE3ZTk5YzRlMz93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnZ2FtaW5nLWNvbnNvbGVzJyB9LFxuICB7IGlkOiAnMjMnLCB0aXRsZTogJ05pbnRlbmRvIFN3aXRjaCBPTEVEJywgc2x1ZzogJ25pbnRlbmRvLXN3aXRjaC1vbGVkJywgYnJhbmQ6IHsgbmFtZTogJ05pbnRlbmRvJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDM0OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNjA2MTQ0MDQyNjE0LWIyNDE3ZTk5YzRlMz93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnZ2FtaW5nLWNvbnNvbGVzJyB9LFxuICB7IGlkOiAnMjQnLCB0aXRsZTogJ1N0ZWFtIERlY2sgT0xFRCcsIHNsdWc6ICdzdGVhbS1kZWNrLW9sZWQnLCBicmFuZDogeyBuYW1lOiAnVmFsdmUnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogNTQ5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MDYxNDQwNDI2MTQtYjI0MTdlOTljNGUzP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdnYW1pbmctY29uc29sZXMnIH0sXG5dO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDYXRlZ29yeVBhZ2UoeyBwYXJhbXMsIHNlYXJjaFBhcmFtcyB9OiBhbnkpIHtcbiAgY29uc3QgY2F0ZWdvcnlTbHVnID0gcGFyYW1zLnNsdWc7XG4gIGNvbnN0IGNhdGVnb3J5ID0gbW9ja0NhdGVnb3JpZXNbY2F0ZWdvcnlTbHVnIGFzIGtleW9mIHR5cGVvZiBtb2NrQ2F0ZWdvcmllc107XG5cbiAgaWYgKCFjYXRlZ29yeSkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7IHBhZGRpbmc6ICcycmVtJywgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgPGgxPkNhdGVnb3J5IE5vdCBGb3VuZDwvaDE+XG4gICAgICAgIDxwPlRoZSBjYXRlZ29yeSBcIntjYXRlZ29yeVNsdWd9XCIgZG9lc24ndCBleGlzdC48L3A+XG4gICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgc3R5bGU9e3sgY29sb3I6ICcjMDA3YmZmJywgdGV4dERlY29yYXRpb246ICdub25lJyB9fT7ihpAgQmFjayB0byBIb21lPC9MaW5rPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIEZpbHRlciBwcm9kdWN0cyBieSBjYXRlZ29yeVxuICBjb25zdCBjYXRlZ29yeVByb2R1Y3RzID0gbW9ja1Byb2R1Y3RzLmZpbHRlcihwID0+IHAuY2F0ZWdvcnkgPT09IGNhdGVnb3J5U2x1Zyk7XG5cbiAgLy8gR2V0IHVuaXF1ZSBicmFuZHMgZm9yIHRoaXMgY2F0ZWdvcnlcbiAgY29uc3QgYnJhbmRzID0gWy4uLm5ldyBTZXQoY2F0ZWdvcnlQcm9kdWN0cy5tYXAocCA9PiBwLmJyYW5kLm5hbWUpKV07XG5cbiAgLy8gQXBwbHkgYnJhbmQgZmlsdGVyIGlmIHNwZWNpZmllZFxuICBjb25zdCBmaWx0ZXJlZFByb2R1Y3RzID0gc2VhcmNoUGFyYW1zLmJyYW5kXG4gICAgPyBjYXRlZ29yeVByb2R1Y3RzLmZpbHRlcihwID0+IHAuYnJhbmQubmFtZS50b0xvd2VyQ2FzZSgpID09PSBzZWFyY2hQYXJhbXMuYnJhbmQudG9Mb3dlckNhc2UoKSlcbiAgICA6IGNhdGVnb3J5UHJvZHVjdHM7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7IG1heFdpZHRoOiAnMTQwMHB4JywgbWFyZ2luOiAnMCBhdXRvJywgcGFkZGluZzogJzFyZW0nIH19PlxuICAgICAgey8qIENhdGVnb3J5IEhlYWRlciAqL31cbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSknLFxuICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgcGFkZGluZzogJzJyZW0nLFxuICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgbWFyZ2luQm90dG9tOiAnMnJlbScsXG4gICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcidcbiAgICAgIH19PlxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnM3JlbScsIG1hcmdpbkJvdHRvbTogJzAuNXJlbScgfX0+e2NhdGVnb3J5Lmljb259PC9kaXY+XG4gICAgICAgIDxoMSBzdHlsZT17eyBmb250U2l6ZTogJzIuNXJlbScsIG1hcmdpbjogJzAgMCAxcmVtIDAnIH19PntjYXRlZ29yeS5uYW1lfTwvaDE+XG4gICAgICAgIDxwIHN0eWxlPXt7IGZvbnRTaXplOiAnMS4ycmVtJywgb3BhY2l0eTogMC45LCBtYXhXaWR0aDogJzYwMHB4JywgbWFyZ2luOiAnMCBhdXRvJyB9fT5cbiAgICAgICAgICB7Y2F0ZWdvcnkuZGVzY3JpcHRpb259XG4gICAgICAgIDwvcD5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6ICcxcmVtJywgZm9udFNpemU6ICcxLjFyZW0nIH19PlxuICAgICAgICAgIDxzdHJvbmc+e2ZpbHRlcmVkUHJvZHVjdHMubGVuZ3RofTwvc3Ryb25nPiBwcm9kdWN0cyBhdmFpbGFibGVcbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZ3JpZCcsIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcyODBweCAxZnInLCBnYXA6ICcycmVtJyB9fT5cbiAgICAgICAgey8qIFNpZGViYXIgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPEZpbHRlclNpZGViYXIgYnJhbmRzPXticmFuZHN9IC8+XG5cbiAgICAgICAgICB7LyogQ2F0ZWdvcnkgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBtYXJnaW5Ub3A6ICcycmVtJyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICcxLjVyZW0nLFxuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2Y4ZjlmYScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4J1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPGgzIHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PkJyb3dzZSBPdGhlciBDYXRlZ29yaWVzPC9oMz5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJywgZ2FwOiAnMC41cmVtJyB9fT5cbiAgICAgICAgICAgICAge09iamVjdC5lbnRyaWVzKG1vY2tDYXRlZ29yaWVzKS5zbGljZSgwLCA2KS5tYXAoKFtzbHVnLCBjYXRdKSA9PiAoXG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGtleT17c2x1Z31cbiAgICAgICAgICAgICAgICAgIGhyZWY9e2AvY2F0ZWdvcnkvJHtzbHVnfWB9XG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogc2x1ZyA9PT0gY2F0ZWdvcnlTbHVnID8gJyMwMDdiZmYnIDogJyM2NjYnLFxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBzbHVnID09PSBjYXRlZ29yeVNsdWcgPyAnI2UzZjJmZCcgOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiBzbHVnID09PSBjYXRlZ29yeVNsdWcgPyAnYm9sZCcgOiAnbm9ybWFsJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7Y2F0Lmljb259IHtjYXQubmFtZX1cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcm9kdWN0cyBHcmlkICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIHtmaWx0ZXJlZFByb2R1Y3RzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMS41cmVtJyxcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2Y4ZjlmYScsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4J1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHN0cm9uZz57ZmlsdGVyZWRQcm9kdWN0cy5sZW5ndGh9PC9zdHJvbmc+IHByb2R1Y3RzIGZvdW5kXG4gICAgICAgICAgICAgICAgICB7c2VhcmNoUGFyYW1zLmJyYW5kICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgY29sb3I6ICcjNjY2JywgbWFyZ2luTGVmdDogJzAuNXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgZmlsdGVyZWQgYnkgXCJ7c2VhcmNoUGFyYW1zLmJyYW5kfVwiXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzAuOXJlbScsIGNvbG9yOiAnIzY2NicgfX0+XG4gICAgICAgICAgICAgICAgICBTb3J0ZWQgYnk6IEZlYXR1cmVkXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZ3JpZCcsXG4gICAgICAgICAgICAgICAgZ3JpZFRlbXBsYXRlQ29sdW1uczogJ3JlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI4MHB4LCAxZnIpKScsXG4gICAgICAgICAgICAgICAgZ2FwOiAnMS41cmVtJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7ZmlsdGVyZWRQcm9kdWN0cy5tYXAocCA9PiAoXG4gICAgICAgICAgICAgICAgICA8UHJvZHVjdENhcmQga2V5PXtwLmlkfSBwcm9kdWN0PXtwfSAvPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvPlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICczcmVtJyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2Y4ZjlmYScsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzEycHgnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPGgzPk5vIHByb2R1Y3RzIGZvdW5kPC9oMz5cbiAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgY29sb3I6ICcjNjY2JywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgTm8gcHJvZHVjdHMgbWF0Y2ggeW91ciBjdXJyZW50IGZpbHRlcnMuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBocmVmPXtgL2NhdGVnb3J5LyR7Y2F0ZWdvcnlTbHVnfWB9XG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzAwN2JmZicsXG4gICAgICAgICAgICAgICAgICB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENsZWFyIGZpbHRlcnMgYW5kIHZpZXcgYWxsIHtjYXRlZ29yeS5uYW1lLnRvTG93ZXJDYXNlKCl9XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUHJvZHVjdENhcmQiLCJGaWx0ZXJTaWRlYmFyIiwiTGluayIsIm1vY2tDYXRlZ29yaWVzIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsIm1vY2tQcm9kdWN0cyIsImlkIiwidGl0bGUiLCJzbHVnIiwiYnJhbmQiLCJ2YXJpYW50cyIsInByaWNlIiwibWVkaWEiLCJ1cmwiLCJjYXRlZ29yeSIsIkNhdGVnb3J5UGFnZSIsInBhcmFtcyIsInNlYXJjaFBhcmFtcyIsImNhdGVnb3J5U2x1ZyIsImRpdiIsInN0eWxlIiwicGFkZGluZyIsInRleHRBbGlnbiIsImgxIiwicCIsImhyZWYiLCJjb2xvciIsInRleHREZWNvcmF0aW9uIiwiY2F0ZWdvcnlQcm9kdWN0cyIsImZpbHRlciIsImJyYW5kcyIsIlNldCIsIm1hcCIsImZpbHRlcmVkUHJvZHVjdHMiLCJ0b0xvd2VyQ2FzZSIsIm1heFdpZHRoIiwibWFyZ2luIiwiYmFja2dyb3VuZCIsImJvcmRlclJhZGl1cyIsIm1hcmdpbkJvdHRvbSIsImZvbnRTaXplIiwib3BhY2l0eSIsIm1hcmdpblRvcCIsInN0cm9uZyIsImxlbmd0aCIsImRpc3BsYXkiLCJncmlkVGVtcGxhdGVDb2x1bW5zIiwiZ2FwIiwiYmFja2dyb3VuZENvbG9yIiwiaDMiLCJmbGV4RGlyZWN0aW9uIiwiT2JqZWN0IiwiZW50cmllcyIsInNsaWNlIiwiY2F0IiwiZm9udFdlaWdodCIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsInNwYW4iLCJtYXJnaW5MZWZ0IiwicHJvZHVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(shop)/category/[slug]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SkipLink */ \"(rsc)/./src/components/SkipLink.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ElectroHub — Electronics Marketplace\",\n    description: \"Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3000\"),\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SkipLink__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    \"aria-label\": \"Site header\",\n                    style: {\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        \"aria-label\": \"Main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"ElectroHub\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            \" | \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 48\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    id: \"main\",\n                    style: {\n                        minHeight: \"60vh\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    style: {\n                        padding: \"2rem\",\n                        borderTop: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" ElectroHub \\xb7 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 60\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/FilterSidebar.tsx":
/*!******************************************!*\
  !*** ./src/components/FilterSidebar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/currency */ \"(rsc)/./src/lib/currency.ts\");\n\n\n\nfunction ProductCard({ product }) {\n    const img = product.media?.[0]?.url || \"https://via.placeholder.com/400x300?text=ElectroHub\";\n    const price = Number(product.variants?.[0]?.price ?? 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        style: {\n            border: \"1px solid #ddd\",\n            borderRadius: 8,\n            overflow: \"hidden\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: `/product/${product.slug}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: img,\n                    alt: product.title,\n                    width: 400,\n                    height: 300,\n                    style: {\n                        width: \"100%\",\n                        height: \"auto\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"0.75rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: 0\n                            },\n                            children: product.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: 0\n                            },\n                            children: product.brand?.name\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatMoney)(price)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ProductCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx#SkipLink`);


/***/ }),

/***/ "(rsc)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatMoney: () => (/* binding */ formatMoney)\n/* harmony export */ });\nfunction formatMoney(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2N1cnJlbmN5LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxTQUFTQSxZQUFZQyxNQUFjLEVBQUVDLFdBQVcsS0FBSyxFQUFFQyxTQUFTLE9BQU87SUFDNUUsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUNGLFFBQVE7UUFBRUcsT0FBTztRQUFZSjtJQUFTLEdBQUdLLE1BQU0sQ0FBQ047QUFDL0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2xpYi9jdXJyZW5jeS50cz81OWM3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBmb3JtYXRNb25leShhbW91bnQ6IG51bWJlciwgY3VycmVuY3kgPSAnVVNEJywgbG9jYWxlID0gJ2VuLVVTJykge1xuICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KGxvY2FsZSwgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3kgfSkuZm9ybWF0KGFtb3VudCk7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0TW9uZXkiLCJhbW91bnQiLCJjdXJyZW5jeSIsImxvY2FsZSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImZvcm1hdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/currency.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&page=%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&appPaths=%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F(shop)%2Fcategory%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();