/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/cart/page";
exports.ids = ["app/cart/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcart%2Fpage&page=%2Fcart%2Fpage&appPaths=%2Fcart%2Fpage&pagePath=private-next-app-dir%2Fcart%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcart%2Fpage&page=%2Fcart%2Fpage&appPaths=%2Fcart%2Fpage&pagePath=private-next-app-dir%2Fcart%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'cart',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/cart/page.tsx */ \"(rsc)/./src/app/cart/page.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/cart/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/cart/page\",\n        pathname: \"/cart\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcart%2Fpage&page=%2Fcart%2Fpage&appPaths=%2Fcart%2Fpage&pagePath=private-next-app-dir%2Fcart%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SkipLink.tsx */ \"(ssr)/./src/components/SkipLink.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRlNraXBMaW5rLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraXBMaW5rJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEc7QUFDMUc7QUFDQSxzS0FBOEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLz9mNWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lwTGlua1wiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FCheckoutButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FCheckoutPriceConfidence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FCheckoutButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FCheckoutPriceConfidence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CheckoutButton.tsx */ \"(ssr)/./src/components/CheckoutButton.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CheckoutPriceConfidence.tsx */ \"(ssr)/./src/components/CheckoutPriceConfidence.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRkNoZWNrb3V0QnV0dG9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRkNoZWNrb3V0UHJpY2VDb25maWRlbmNlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUEwRztBQUMxRztBQUNBLGtMQUFtSTtBQUNuSTtBQUNBLG9NQUE0SSIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZWN0cm9odWItbWFya2V0cGxhY2UvPzdlZGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvQXBwbGUvRGVza3RvcC9FbGVjdHJvSHViL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvQXBwbGUvRGVza3RvcC9FbGVjdHJvSHViL3NyYy9jb21wb25lbnRzL0NoZWNrb3V0QnV0dG9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvQ2hlY2tvdXRQcmljZUNvbmZpZGVuY2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FCheckoutButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FCheckoutPriceConfidence.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/CheckoutButton.tsx":
/*!*******************************************!*\
  !*** ./src/components/CheckoutButton.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction CheckoutButton() {\n    async function checkout() {\n        const res = await fetch(\"/api/checkout\", {\n            method: \"POST\"\n        });\n        if (!res.ok) return alert(\"Checkout failed\");\n        const { url } = await res.json();\n        window.location.href = url;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: checkout,\n        \"aria-label\": \"Proceed to checkout\",\n        children: \"Proceed to Checkout\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutButton.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaGVja291dEJ1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVlLFNBQVNBO0lBQ3RCLGVBQWVDO1FBQ2IsTUFBTUMsTUFBTSxNQUFNQyxNQUFNLGlCQUFpQjtZQUFFQyxRQUFRO1FBQU87UUFDMUQsSUFBSSxDQUFDRixJQUFJRyxFQUFFLEVBQUUsT0FBT0MsTUFBTTtRQUMxQixNQUFNLEVBQUVDLEdBQUcsRUFBRSxHQUFHLE1BQU1MLElBQUlNLElBQUk7UUFDOUJDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHSjtJQUN6QjtJQUNBLHFCQUFPLDhEQUFDSztRQUFPQyxTQUFTWjtRQUFVYSxjQUFXO2tCQUFzQjs7Ozs7O0FBQ3JFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9jb21wb25lbnRzL0NoZWNrb3V0QnV0dG9uLnRzeD9mOTNlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2hlY2tvdXRCdXR0b24oKSB7XG4gIGFzeW5jIGZ1bmN0aW9uIGNoZWNrb3V0KCkge1xuICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKCcvYXBpL2NoZWNrb3V0JywgeyBtZXRob2Q6ICdQT1NUJyB9KTtcbiAgICBpZiAoIXJlcy5vaykgcmV0dXJuIGFsZXJ0KCdDaGVja291dCBmYWlsZWQnKTtcbiAgICBjb25zdCB7IHVybCB9ID0gYXdhaXQgcmVzLmpzb24oKTtcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVybDtcbiAgfVxuICByZXR1cm4gPGJ1dHRvbiBvbkNsaWNrPXtjaGVja291dH0gYXJpYS1sYWJlbD1cIlByb2NlZWQgdG8gY2hlY2tvdXRcIj5Qcm9jZWVkIHRvIENoZWNrb3V0PC9idXR0b24+O1xufVxuIl0sIm5hbWVzIjpbIkNoZWNrb3V0QnV0dG9uIiwiY2hlY2tvdXQiLCJyZXMiLCJmZXRjaCIsIm1ldGhvZCIsIm9rIiwiYWxlcnQiLCJ1cmwiLCJqc29uIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiYnV0dG9uIiwib25DbGljayIsImFyaWEtbGFiZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CheckoutButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CheckoutPriceConfidence.tsx":
/*!****************************************************!*\
  !*** ./src/components/CheckoutPriceConfidence.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPriceConfidence)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CheckoutPriceConfidence({ cartItems }) {\n    const [priceSummary, setPriceSummary] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const analyzePrices = async ()=>{\n            setIsLoading(true);\n            // Simulate price analysis\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Mock analysis results\n            const totalSavings = cartItems.reduce((sum, item)=>{\n                // Simulate savings calculation\n                const avgSavings = Math.random() * 50 + 10;\n                return sum + avgSavings * item.quantity;\n            }, 0);\n            const bestPriceItems = Math.floor(cartItems.length * 0.7); // 70% best price\n            const competitiveItems = cartItems.length - bestPriceItems;\n            const confidenceScore = Math.min(95, 75 + bestPriceItems / cartItems.length * 20);\n            setPriceSummary({\n                totalSavings,\n                bestPriceItems,\n                competitiveItems,\n                confidenceScore,\n                lastChecked: new Date()\n            });\n            setIsLoading(false);\n        };\n        if (cartItems.length > 0) {\n            analyzePrices();\n        }\n    }, [\n        cartItems\n    ]);\n    if (cartItems.length === 0) return null;\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"#f8f9fa\",\n                border: \"1px solid #e1e5e9\",\n                borderRadius: \"12px\",\n                padding: \"1.5rem\",\n                margin: \"1rem 0\"\n            },\n            className: \"jsx-d525c98fb358669b\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"1rem\",\n                        marginBottom: \"1rem\"\n                    },\n                    className: \"jsx-d525c98fb358669b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"30px\",\n                                height: \"30px\",\n                                border: \"3px solid #007bff\",\n                                borderTop: \"3px solid transparent\",\n                                borderRadius: \"50%\",\n                                animation: \"spin 1s linear infinite\"\n                            },\n                            className: \"jsx-d525c98fb358669b\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d525c98fb358669b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontWeight: \"bold\",\n                                        marginBottom: \"0.25rem\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: \"\\uD83D\\uDD0D Verifying Best Prices...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"0.9rem\",\n                                        color: \"#666\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: [\n                                        \"Checking \",\n                                        cartItems.length,\n                                        \" items against competitor prices\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        gap: \"0.5rem\",\n                        fontSize: \"0.9rem\",\n                        color: \"#666\"\n                    },\n                    className: \"jsx-d525c98fb358669b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d525c98fb358669b\",\n                            children: \"✅ Scanning major retailers...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d525c98fb358669b\",\n                            children: \"\\uD83D\\uDD0D Comparing current market prices...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d525c98fb358669b\",\n                            children: \"\\uD83D\\uDCB0 Calculating your savings...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"d525c98fb358669b\",\n                    children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n                }, void 0, false, void 0, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    if (!priceSummary) return null;\n    const getConfidenceColor = (score)=>{\n        if (score >= 90) return \"#28a745\";\n        if (score >= 75) return \"#ffc107\";\n        return \"#dc3545\";\n    };\n    const getConfidenceText = (score)=>{\n        if (score >= 90) return \"Excellent Deal\";\n        if (score >= 75) return \"Good Value\";\n        return \"Fair Price\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: \"#ffffff\",\n            border: \"2px solid #28a745\",\n            borderRadius: \"12px\",\n            padding: \"1.5rem\",\n            margin: \"1rem 0\"\n        },\n        className: \"jsx-8ab98315ba029cdc\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    marginBottom: \"1rem\"\n                },\n                className: \"jsx-8ab98315ba029cdc\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"0.75rem\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"2rem\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: \"\\uD83D\\uDEE1️\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontWeight: \"bold\",\n                                            fontSize: \"1.2rem\",\n                                            color: \"#28a745\"\n                                        },\n                                        className: \"jsx-8ab98315ba029cdc\",\n                                        children: \"Price Confidence Verified\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            color: \"#666\"\n                                        },\n                                        className: \"jsx-8ab98315ba029cdc\",\n                                        children: [\n                                            \"Last checked: \",\n                                            priceSummary.lastChecked.toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowDetails(!showDetails),\n                        style: {\n                            padding: \"0.5rem 1rem\",\n                            backgroundColor: \"#f8f9fa\",\n                            border: \"1px solid #e1e5e9\",\n                            borderRadius: \"6px\",\n                            cursor: \"pointer\",\n                            fontSize: \"0.9rem\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: showDetails ? \"Hide Details\" : \"Show Details\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(150px, 1fr))\",\n                    gap: \"1rem\",\n                    marginBottom: \"1rem\"\n                },\n                className: \"jsx-8ab98315ba029cdc\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"1rem\",\n                            backgroundColor: \"#d4edda\",\n                            borderRadius: \"8px\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1.5rem\",\n                                    fontWeight: \"bold\",\n                                    color: \"#155724\",\n                                    marginBottom: \"0.25rem\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: [\n                                    \"$\",\n                                    priceSummary.totalSavings.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: \"#155724\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: \"Total Savings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"1rem\",\n                            backgroundColor: \"#d4edda\",\n                            borderRadius: \"8px\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1.5rem\",\n                                    fontWeight: \"bold\",\n                                    color: \"#155724\",\n                                    marginBottom: \"0.25rem\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: priceSummary.bestPriceItems\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: \"#155724\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: \"Best Price Items\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"1rem\",\n                            backgroundColor: \"#fff3cd\",\n                            borderRadius: \"8px\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1.5rem\",\n                                    fontWeight: \"bold\",\n                                    color: \"#856404\",\n                                    marginBottom: \"0.25rem\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: priceSummary.competitiveItems\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: \"#856404\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: \"Competitive Items\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"1rem\",\n                            backgroundColor: getConfidenceColor(priceSummary.confidenceScore) + \"20\",\n                            borderRadius: \"8px\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1.5rem\",\n                                    fontWeight: \"bold\",\n                                    color: getConfidenceColor(priceSummary.confidenceScore),\n                                    marginBottom: \"0.25rem\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: [\n                                    priceSummary.confidenceScore.toFixed(0),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: getConfidenceColor(priceSummary.confidenceScore)\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: getConfidenceText(priceSummary.confidenceScore)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    borderTop: \"1px solid #e1e5e9\",\n                    paddingTop: \"1rem\",\n                    animation: \"slideDown 0.3s ease\"\n                },\n                className: \"jsx-8ab98315ba029cdc\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        style: {\n                            marginBottom: \"1rem\",\n                            color: \"#333\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: \"\\uD83D\\uDCCA Item-by-Item Analysis\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"0.75rem\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: cartItems.map((item, index)=>{\n                            const isBestPrice = index < priceSummary.bestPriceItems;\n                            const itemSavings = Math.random() * 30 + 5; // Mock savings per item\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\",\n                                    padding: \"0.75rem\",\n                                    backgroundColor: \"#f8f9fa\",\n                                    borderRadius: \"6px\"\n                                },\n                                className: \"jsx-8ab98315ba029cdc\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        className: \"jsx-8ab98315ba029cdc\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: \"bold\",\n                                                    marginBottom: \"0.25rem\"\n                                                },\n                                                className: \"jsx-8ab98315ba029cdc\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"0.8rem\",\n                                                    color: \"#666\"\n                                                },\n                                                className: \"jsx-8ab98315ba029cdc\",\n                                                children: [\n                                                    \"Qty: \",\n                                                    item.quantity,\n                                                    \" \\xd7 $\",\n                                                    item.price.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"1rem\"\n                                        },\n                                        className: \"jsx-8ab98315ba029cdc\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"0.25rem 0.75rem\",\n                                                    backgroundColor: isBestPrice ? \"#d4edda\" : \"#fff3cd\",\n                                                    color: isBestPrice ? \"#155724\" : \"#856404\",\n                                                    borderRadius: \"12px\",\n                                                    fontSize: \"0.8rem\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                className: \"jsx-8ab98315ba029cdc\",\n                                                children: isBestPrice ? \"\\uD83C\\uDFC6 Best Price\" : \"\\uD83D\\uDCB0 Competitive\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"0.9rem\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#28a745\"\n                                                },\n                                                className: \"jsx-8ab98315ba029cdc\",\n                                                children: [\n                                                    \"Save $\",\n                                                    itemSavings.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.id, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"1rem\",\n                    padding: \"1rem\",\n                    backgroundColor: \"#e3f2fd\",\n                    borderRadius: \"8px\",\n                    textAlign: \"center\"\n                },\n                className: \"jsx-8ab98315ba029cdc\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            color: \"#1976d2\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: \"\\uD83D\\uDEE1️ 100% Price Match Guarantee\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"0.9rem\",\n                            color: \"#666\"\n                        },\n                        className: \"jsx-8ab98315ba029cdc\",\n                        children: \"If you find a lower price within 30 days, we'll refund the difference plus 10% of the difference as store credit.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"8ab98315ba029cdc\",\n                children: \"@-webkit-keyframes slideDown{from{opacity:0;-webkit-transform:translatey(-10px);transform:translatey(-10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes slideDown{from{opacity:0;-moz-transform:translatey(-10px);transform:translatey(-10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes slideDown{from{opacity:0;-o-transform:translatey(-10px);transform:translatey(-10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes slideDown{from{opacity:0;-webkit-transform:translatey(-10px);-moz-transform:translatey(-10px);-o-transform:translatey(-10px);transform:translatey(-10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CheckoutPriceConfidence.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SkipLink auto */ \nfunction SkipLink() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main\",\n        style: {\n            position: \"absolute\",\n            left: -9999,\n            top: -9999\n        },\n        onFocus: (e)=>{\n            e.currentTarget.style.left = \"8px\";\n            e.currentTarget.style.top = \"8px\";\n            e.currentTarget.style.background = \"white\";\n            e.currentTarget.style.padding = \"8px\";\n        },\n        children: \"Skip to content\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ta2lwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUVDLE1BQUs7UUFBUUMsT0FBTztZQUFFQyxVQUFVO1lBQVlDLE1BQU0sQ0FBQztZQUFNQyxLQUFLLENBQUM7UUFBSztRQUFHQyxTQUFTLENBQUNDO1lBQ2pGQSxFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNFLElBQUksR0FBRztZQUNuREcsRUFBRUMsYUFBYSxDQUF1Qk4sS0FBSyxDQUFDRyxHQUFHLEdBQUc7WUFDbERFLEVBQUVDLGFBQWEsQ0FBdUJOLEtBQUssQ0FBQ08sVUFBVSxHQUFHO1lBQ3pERixFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNRLE9BQU8sR0FBRztRQUN6RDtrQkFBRzs7Ozs7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4Pzk5NTEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2tpcExpbmsoKSB7XG4gIHJldHVybiAoXG4gICAgPGEgaHJlZj1cIiNtYWluXCIgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGxlZnQ6IC05OTk5LCB0b3A6IC05OTk5IH19IG9uRm9jdXM9eyhlKSA9PiB7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS5sZWZ0ID0gJzhweCc7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS50b3AgPSAnOHB4JztcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnQpLnN0eWxlLmJhY2tncm91bmQgPSAnd2hpdGUnO1xuICAgICAgKGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCkuc3R5bGUucGFkZGluZyA9ICc4cHgnO1xuICAgIH19PlNraXAgdG8gY29udGVudDwvYT5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTa2lwTGluayIsImEiLCJocmVmIiwic3R5bGUiLCJwb3NpdGlvbiIsImxlZnQiLCJ0b3AiLCJvbkZvY3VzIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJiYWNrZ3JvdW5kIiwicGFkZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SkipLink.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1cc9da9c511c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYzlkYTljNTExY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/cart/page.tsx":
/*!*******************************!*\
  !*** ./src/app/cart/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CartPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_CheckoutButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/CheckoutButton */ \"(rsc)/./src/components/CheckoutButton.tsx\");\n/* harmony import */ var _components_CheckoutPriceConfidence__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/CheckoutPriceConfidence */ \"(rsc)/./src/components/CheckoutPriceConfidence.tsx\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/currency */ \"(rsc)/./src/lib/currency.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\n// Mock cart data for demo\nconst mockCartItems = [\n    {\n        id: \"1\",\n        product: {\n            title: \"iPhone 15 Pro\",\n            slug: \"iphone-15-pro\",\n            brand: {\n                name: \"Apple\"\n            },\n            media: [\n                {\n                    url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=150&h=150&fit=crop\"\n                }\n            ]\n        },\n        variant: {\n            id: \"v1\",\n            price: 999.99,\n            sku: \"IPHONE15P-256-TIT\"\n        },\n        quantity: 1,\n        unitPrice: 999.99\n    },\n    {\n        id: \"2\",\n        product: {\n            title: \"AirPods Pro 2\",\n            slug: \"airpods-pro-2\",\n            brand: {\n                name: \"Apple\"\n            },\n            media: [\n                {\n                    url: \"https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=150&h=150&fit=crop\"\n                }\n            ]\n        },\n        variant: {\n            id: \"v15\",\n            price: 249.99,\n            sku: \"AIRPODS-PRO2-WHT\"\n        },\n        quantity: 1,\n        unitPrice: 249.99\n    }\n];\nfunction CartPage() {\n    const items = mockCartItems;\n    const subtotal = items.reduce((sum, i)=>sum + i.unitPrice * i.quantity, 0);\n    const shipping = subtotal > 50 ? 0 : 9.99;\n    const tax = subtotal * 0.08; // 8% tax\n    const total = subtotal + shipping + tax;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: \"1200px\",\n            margin: \"0 auto\",\n            padding: \"1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"2.5rem\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        children: \"\\uD83D\\uDED2 Shopping Cart\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            fontSize: \"0.9rem\",\n                            color: \"#666\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/\",\n                                style: {\n                                    color: \"#007bff\",\n                                    textDecoration: \"none\"\n                                },\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            \" > \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            items.length === 0 ? // Empty Cart\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    padding: \"4rem 2rem\",\n                    backgroundColor: \"#f8f9fa\",\n                    borderRadius: \"12px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"4rem\",\n                            marginBottom: \"1rem\"\n                        },\n                        children: \"\\uD83D\\uDED2\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: \"1rem\"\n                        },\n                        children: \"Your cart is empty\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: \"#666\",\n                            marginBottom: \"2rem\",\n                            fontSize: \"1.1rem\"\n                        },\n                        children: \"Looks like you haven't added any items to your cart yet.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/products\",\n                        style: {\n                            display: \"inline-block\",\n                            padding: \"12px 24px\",\n                            backgroundColor: \"#007bff\",\n                            color: \"white\",\n                            textDecoration: \"none\",\n                            borderRadius: \"8px\",\n                            fontWeight: \"bold\",\n                            fontSize: \"1.1rem\"\n                        },\n                        children: \"Continue Shopping\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this) : // Cart with Items\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CheckoutPriceConfidence__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        cartItems: items.map((item)=>({\n                                id: item.id,\n                                name: item.product.title,\n                                price: item.unitPrice,\n                                quantity: item.quantity,\n                                slug: item.product.slug\n                            }))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"2fr 1fr\",\n                            gap: \"2rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            backgroundColor: \"#ffffff\",\n                                            border: \"1px solid #e1e5e9\",\n                                            borderRadius: \"12px\",\n                                            overflow: \"hidden\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    padding: \"1.5rem\",\n                                                    backgroundColor: \"#f8f9fa\",\n                                                    borderBottom: \"1px solid #e1e5e9\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: [\n                                                    \"Cart Items (\",\n                                                    items.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                padding: \"1.5rem\",\n                                                                display: \"grid\",\n                                                                gridTemplateColumns: \"100px 1fr auto auto\",\n                                                                gap: \"1rem\",\n                                                                alignItems: \"center\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    href: `/product/${item.product.slug}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: item.product.media[0]?.url || \"https://via.placeholder.com/100x100?text=Product\",\n                                                                        alt: item.product.title,\n                                                                        style: {\n                                                                            width: \"100px\",\n                                                                            height: \"100px\",\n                                                                            objectFit: \"cover\",\n                                                                            borderRadius: \"8px\",\n                                                                            cursor: \"pointer\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            href: `/product/${item.product.slug}`,\n                                                                            style: {\n                                                                                textDecoration: \"none\",\n                                                                                color: \"#333\",\n                                                                                fontWeight: \"bold\",\n                                                                                fontSize: \"1.1rem\"\n                                                                            },\n                                                                            children: item.product.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                            lineNumber: 148,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                color: \"#666\",\n                                                                                marginTop: \"0.25rem\"\n                                                                            },\n                                                                            children: [\n                                                                                \"by \",\n                                                                                item.product.brand.name\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                            lineNumber: 159,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                color: \"#666\",\n                                                                                fontSize: \"0.9rem\",\n                                                                                marginTop: \"0.25rem\"\n                                                                            },\n                                                                            children: [\n                                                                                \"SKU: \",\n                                                                                item.variant.sku\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                            lineNumber: 162,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                marginTop: \"0.5rem\"\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                style: {\n                                                                                    background: \"none\",\n                                                                                    border: \"none\",\n                                                                                    color: \"#dc3545\",\n                                                                                    cursor: \"pointer\",\n                                                                                    fontSize: \"0.9rem\",\n                                                                                    textDecoration: \"underline\"\n                                                                                },\n                                                                                children: \"Remove\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                                lineNumber: 166,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                            lineNumber: 165,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                    lineNumber: 147,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        textAlign: \"center\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                marginBottom: \"0.5rem\",\n                                                                                fontSize: \"0.9rem\",\n                                                                                color: \"#666\"\n                                                                            },\n                                                                            children: \"Quantity\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                display: \"flex\",\n                                                                                alignItems: \"center\",\n                                                                                border: \"1px solid #ddd\",\n                                                                                borderRadius: \"6px\",\n                                                                                overflow: \"hidden\"\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    style: {\n                                                                                        padding: \"0.5rem\",\n                                                                                        border: \"none\",\n                                                                                        backgroundColor: \"#f8f9fa\",\n                                                                                        cursor: \"pointer\"\n                                                                                    },\n                                                                                    children: \"-\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                                    lineNumber: 191,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        padding: \"0.5rem 1rem\",\n                                                                                        minWidth: \"50px\",\n                                                                                        textAlign: \"center\"\n                                                                                    },\n                                                                                    children: item.quantity\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                                    lineNumber: 199,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    style: {\n                                                                                        padding: \"0.5rem\",\n                                                                                        border: \"none\",\n                                                                                        backgroundColor: \"#f8f9fa\",\n                                                                                        cursor: \"pointer\"\n                                                                                    },\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                                    lineNumber: 206,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        textAlign: \"right\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                fontWeight: \"bold\",\n                                                                                fontSize: \"1.2rem\",\n                                                                                color: \"#007bff\"\n                                                                            },\n                                                                            children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_3__.formatMoney)(item.unitPrice * item.quantity)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                fontSize: \"0.9rem\",\n                                                                                color: \"#666\"\n                                                                            },\n                                                                            children: [\n                                                                                (0,_lib_currency__WEBPACK_IMPORTED_MODULE_3__.formatMoney)(item.unitPrice),\n                                                                                \" each\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                            lineNumber: 226,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        index < items.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                borderBottom: \"1px solid #e1e5e9\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: \"1rem\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/products\",\n                                            style: {\n                                                color: \"#007bff\",\n                                                textDecoration: \"none\",\n                                                fontWeight: \"bold\"\n                                            },\n                                            children: \"← Continue Shopping\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: \"#ffffff\",\n                                        border: \"1px solid #e1e5e9\",\n                                        borderRadius: \"12px\",\n                                        padding: \"1.5rem\",\n                                        position: \"sticky\",\n                                        top: \"1rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                marginBottom: \"1.5rem\"\n                                            },\n                                            children: \"Order Summary\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: \"1rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        marginBottom: \"0.5rem\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Subtotal (\",\n                                                                items.length,\n                                                                \" items)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_3__.formatMoney)(subtotal)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        marginBottom: \"0.5rem\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Shipping\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: shipping === 0 ? \"#28a745\" : \"#333\"\n                                                            },\n                                                            children: shipping === 0 ? \"FREE\" : (0,_lib_currency__WEBPACK_IMPORTED_MODULE_3__.formatMoney)(shipping)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        marginBottom: \"0.5rem\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Tax\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_3__.formatMoney)(tax)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                shipping > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        color: \"#666\",\n                                                        marginTop: \"0.5rem\",\n                                                        padding: \"0.5rem\",\n                                                        backgroundColor: \"#fff3cd\",\n                                                        borderRadius: \"6px\"\n                                                    },\n                                                    children: [\n                                                        \"\\uD83D\\uDCA1 Add \",\n                                                        (0,_lib_currency__WEBPACK_IMPORTED_MODULE_3__.formatMoney)(50 - subtotal),\n                                                        \" more for free shipping!\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                borderTop: \"1px solid #e1e5e9\",\n                                                paddingTop: \"1rem\",\n                                                marginBottom: \"1.5rem\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    fontSize: \"1.2rem\",\n                                                    fontWeight: \"bold\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Total\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: \"#007bff\"\n                                                        },\n                                                        children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_3__.formatMoney)(total)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CheckoutButton__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginTop: \"1rem\",\n                                                fontSize: \"0.9rem\",\n                                                color: \"#666\",\n                                                textAlign: \"center\"\n                                            },\n                                            children: \"\\uD83D\\uDD12 Secure checkout with SSL encryption\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/cart/page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/cart/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SkipLink */ \"(rsc)/./src/components/SkipLink.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ElectroHub — Electronics Marketplace\",\n    description: \"Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3000\"),\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SkipLink__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    \"aria-label\": \"Site header\",\n                    style: {\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        \"aria-label\": \"Main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"ElectroHub\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            \" | \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 48\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    id: \"main\",\n                    style: {\n                        minHeight: \"60vh\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    style: {\n                        padding: \"2rem\",\n                        borderTop: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" ElectroHub \\xb7 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 60\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/CheckoutButton.tsx":
/*!*******************************************!*\
  !*** ./src/components/CheckoutButton.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutButton.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/CheckoutPriceConfidence.tsx":
/*!****************************************************!*\
  !*** ./src/components/CheckoutPriceConfidence.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutPriceConfidence.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx#SkipLink`);


/***/ }),

/***/ "(rsc)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatMoney: () => (/* binding */ formatMoney)\n/* harmony export */ });\nfunction formatMoney(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2N1cnJlbmN5LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxTQUFTQSxZQUFZQyxNQUFjLEVBQUVDLFdBQVcsS0FBSyxFQUFFQyxTQUFTLE9BQU87SUFDNUUsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUNGLFFBQVE7UUFBRUcsT0FBTztRQUFZSjtJQUFTLEdBQUdLLE1BQU0sQ0FBQ047QUFDL0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2xpYi9jdXJyZW5jeS50cz81OWM3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBmb3JtYXRNb25leShhbW91bnQ6IG51bWJlciwgY3VycmVuY3kgPSAnVVNEJywgbG9jYWxlID0gJ2VuLVVTJykge1xuICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KGxvY2FsZSwgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3kgfSkuZm9ybWF0KGFtb3VudCk7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0TW9uZXkiLCJhbW91bnQiLCJjdXJyZW5jeSIsImxvY2FsZSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImZvcm1hdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/currency.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcart%2Fpage&page=%2Fcart%2Fpage&appPaths=%2Fcart%2Fpage&pagePath=private-next-app-dir%2Fcart%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();