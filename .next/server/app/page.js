/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SkipLink.tsx */ \"(ssr)/./src/components/SkipLink.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRlNraXBMaW5rLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraXBMaW5rJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEc7QUFDMUc7QUFDQSxzS0FBOEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLz9mNWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lwTGlua1wiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUEwRyIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZWN0cm9odWItbWFya2V0cGxhY2UvPzk2NmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvQXBwbGUvRGVza3RvcC9FbGVjdHJvSHViL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SkipLink auto */ \nfunction SkipLink() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main\",\n        style: {\n            position: \"absolute\",\n            left: -9999,\n            top: -9999\n        },\n        onFocus: (e)=>{\n            e.currentTarget.style.left = \"8px\";\n            e.currentTarget.style.top = \"8px\";\n            e.currentTarget.style.background = \"white\";\n            e.currentTarget.style.padding = \"8px\";\n        },\n        children: \"Skip to content\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ta2lwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUVDLE1BQUs7UUFBUUMsT0FBTztZQUFFQyxVQUFVO1lBQVlDLE1BQU0sQ0FBQztZQUFNQyxLQUFLLENBQUM7UUFBSztRQUFHQyxTQUFTLENBQUNDO1lBQ2pGQSxFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNFLElBQUksR0FBRztZQUNuREcsRUFBRUMsYUFBYSxDQUF1Qk4sS0FBSyxDQUFDRyxHQUFHLEdBQUc7WUFDbERFLEVBQUVDLGFBQWEsQ0FBdUJOLEtBQUssQ0FBQ08sVUFBVSxHQUFHO1lBQ3pERixFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNRLE9BQU8sR0FBRztRQUN6RDtrQkFBRzs7Ozs7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4Pzk5NTEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2tpcExpbmsoKSB7XG4gIHJldHVybiAoXG4gICAgPGEgaHJlZj1cIiNtYWluXCIgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGxlZnQ6IC05OTk5LCB0b3A6IC05OTk5IH19IG9uRm9jdXM9eyhlKSA9PiB7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS5sZWZ0ID0gJzhweCc7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS50b3AgPSAnOHB4JztcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnQpLnN0eWxlLmJhY2tncm91bmQgPSAnd2hpdGUnO1xuICAgICAgKGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCkuc3R5bGUucGFkZGluZyA9ICc4cHgnO1xuICAgIH19PlNraXAgdG8gY29udGVudDwvYT5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTa2lwTGluayIsImEiLCJocmVmIiwic3R5bGUiLCJwb3NpdGlvbiIsImxlZnQiLCJ0b3AiLCJvbkZvY3VzIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJiYWNrZ3JvdW5kIiwicGFkZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SkipLink.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1cc9da9c511c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYzlkYTljNTExY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SkipLink */ \"(rsc)/./src/components/SkipLink.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ElectroHub — Electronics Marketplace\",\n    description: \"Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3000\"),\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SkipLink__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    \"aria-label\": \"Site header\",\n                    style: {\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        \"aria-label\": \"Main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"ElectroHub\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            \" | \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 48\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    id: \"main\",\n                    style: {\n                        minHeight: \"60vh\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    style: {\n                        padding: \"2rem\",\n                        borderTop: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" ElectroHub \\xb7 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 60\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProductCard */ \"(rsc)/./src/components/ProductCard.tsx\");\n\n\n\n// Comprehensive Electronics Marketplace - Mock data for preview\nconst mockCategories = [\n    {\n        id: \"1\",\n        name: \"Smartphones\",\n        slug: \"smartphones\"\n    },\n    {\n        id: \"2\",\n        name: \"Laptops & Computers\",\n        slug: \"laptops-computers\"\n    },\n    {\n        id: \"3\",\n        name: \"Audio & Headphones\",\n        slug: \"audio-headphones\"\n    },\n    {\n        id: \"4\",\n        name: \"Gaming & Consoles\",\n        slug: \"gaming-consoles\"\n    },\n    {\n        id: \"5\",\n        name: \"Tablets & E-readers\",\n        slug: \"tablets-ereaders\"\n    },\n    {\n        id: \"6\",\n        name: \"Wearables & Fitness\",\n        slug: \"wearables-fitness\"\n    },\n    {\n        id: \"7\",\n        name: \"Smart Home & IoT\",\n        slug: \"smart-home-iot\"\n    },\n    {\n        id: \"8\",\n        name: \"Cameras & Photography\",\n        slug: \"cameras-photography\"\n    },\n    {\n        id: \"9\",\n        name: \"TV & Home Theater\",\n        slug: \"tv-home-theater\"\n    },\n    {\n        id: \"10\",\n        name: \"Computer Components\",\n        slug: \"computer-components\"\n    },\n    {\n        id: \"11\",\n        name: \"Networking & WiFi\",\n        slug: \"networking-wifi\"\n    },\n    {\n        id: \"12\",\n        name: \"Storage & Memory\",\n        slug: \"storage-memory\"\n    },\n    {\n        id: \"13\",\n        name: \"Monitors & Displays\",\n        slug: \"monitors-displays\"\n    },\n    {\n        id: \"14\",\n        name: \"Keyboards & Mice\",\n        slug: \"keyboards-mice\"\n    },\n    {\n        id: \"15\",\n        name: \"Mobile Accessories\",\n        slug: \"mobile-accessories\"\n    },\n    {\n        id: \"16\",\n        name: \"Power & Charging\",\n        slug: \"power-charging\"\n    },\n    {\n        id: \"17\",\n        name: \"Drones & RC\",\n        slug: \"drones-rc\"\n    },\n    {\n        id: \"18\",\n        name: \"VR & AR\",\n        slug: \"vr-ar\"\n    },\n    {\n        id: \"19\",\n        name: \"Security & Surveillance\",\n        slug: \"security-surveillance\"\n    },\n    {\n        id: \"20\",\n        name: \"Office Electronics\",\n        slug: \"office-electronics\"\n    }\n];\nconst mockProducts = [\n    // SMARTPHONES & MOBILE\n    {\n        id: \"1\",\n        title: \"iPhone 15 Pro Max\",\n        slug: \"iphone-15-pro-max\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        title: \"iPhone 15 Pro\",\n        slug: \"iphone-15-pro\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"3\",\n        title: \"Samsung Galaxy S24 Ultra\",\n        slug: \"galaxy-s24-ultra\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"4\",\n        title: \"Samsung Galaxy S24\",\n        slug: \"galaxy-s24\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"5\",\n        title: \"Google Pixel 8 Pro\",\n        slug: \"google-pixel-8-pro\",\n        brand: {\n            name: \"Google\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"6\",\n        title: \"OnePlus 12\",\n        slug: \"oneplus-12\",\n        brand: {\n            name: \"OnePlus\"\n        },\n        variants: [\n            {\n                price: 799.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"7\",\n        title: \"Xiaomi 14 Ultra\",\n        slug: \"xiaomi-14-ultra\",\n        brand: {\n            name: \"Xiaomi\"\n        },\n        variants: [\n            {\n                price: 699.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // LAPTOPS & COMPUTERS\n    {\n        id: \"8\",\n        title: 'MacBook Pro 16\" M3 Max',\n        slug: \"macbook-pro-16-m3-max\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 3499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"9\",\n        title: \"MacBook Air M3\",\n        slug: \"macbook-air-m3\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"10\",\n        title: \"Dell XPS 13 Plus\",\n        slug: \"dell-xps-13-plus\",\n        brand: {\n            name: \"Dell\"\n        },\n        variants: [\n            {\n                price: 1399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"11\",\n        title: \"ThinkPad X1 Carbon\",\n        slug: \"thinkpad-x1-carbon\",\n        brand: {\n            name: \"Lenovo\"\n        },\n        variants: [\n            {\n                price: 1599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"12\",\n        title: \"Surface Laptop 5\",\n        slug: \"surface-laptop-5\",\n        brand: {\n            name: \"Microsoft\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"13\",\n        title: \"ASUS ROG Zephyrus G14\",\n        slug: \"asus-rog-zephyrus-g14\",\n        brand: {\n            name: \"ASUS\"\n        },\n        variants: [\n            {\n                price: 1899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"14\",\n        title: \"HP Spectre x360\",\n        slug: \"hp-spectre-x360\",\n        brand: {\n            name: \"HP\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // AUDIO & HEADPHONES\n    {\n        id: \"15\",\n        title: \"AirPods Pro 2\",\n        slug: \"airpods-pro-2\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 249.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"16\",\n        title: \"Sony WH-1000XM5\",\n        slug: \"sony-wh-1000xm5\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"17\",\n        title: \"Bose QuietComfort Ultra\",\n        slug: \"bose-quietcomfort-ultra\",\n        brand: {\n            name: \"Bose\"\n        },\n        variants: [\n            {\n                price: 429.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"18\",\n        title: \"Sennheiser HD 800S\",\n        slug: \"sennheiser-hd-800s\",\n        brand: {\n            name: \"Sennheiser\"\n        },\n        variants: [\n            {\n                price: 1699.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"19\",\n        title: \"Audio-Technica ATH-M50x\",\n        slug: \"audio-technica-ath-m50x\",\n        brand: {\n            name: \"Audio-Technica\"\n        },\n        variants: [\n            {\n                price: 149.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"20\",\n        title: \"Beats Studio Pro\",\n        slug: \"beats-studio-pro\",\n        brand: {\n            name: \"Beats\"\n        },\n        variants: [\n            {\n                price: 349.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // GAMING & CONSOLES\n    {\n        id: \"21\",\n        title: \"PlayStation 5\",\n        slug: \"playstation-5\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"22\",\n        title: \"Xbox Series X\",\n        slug: \"xbox-series-x\",\n        brand: {\n            name: \"Microsoft\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"23\",\n        title: \"Nintendo Switch OLED\",\n        slug: \"nintendo-switch-oled\",\n        brand: {\n            name: \"Nintendo\"\n        },\n        variants: [\n            {\n                price: 349.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"24\",\n        title: \"Steam Deck OLED\",\n        slug: \"steam-deck-oled\",\n        brand: {\n            name: \"Valve\"\n        },\n        variants: [\n            {\n                price: 549.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // TABLETS & E-READERS\n    {\n        id: \"25\",\n        title: 'iPad Pro 12.9\" M2',\n        slug: \"ipad-pro-12-9-m2\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1099.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"26\",\n        title: \"iPad Air 5\",\n        slug: \"ipad-air-5\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"27\",\n        title: \"Samsung Galaxy Tab S9 Ultra\",\n        slug: \"galaxy-tab-s9-ultra\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"28\",\n        title: \"Microsoft Surface Pro 9\",\n        slug: \"surface-pro-9\",\n        brand: {\n            name: \"Microsoft\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"29\",\n        title: \"Kindle Oasis\",\n        slug: \"kindle-oasis\",\n        brand: {\n            name: \"Amazon\"\n        },\n        variants: [\n            {\n                price: 249.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // WEARABLES & FITNESS\n    {\n        id: \"30\",\n        title: \"Apple Watch Ultra 2\",\n        slug: \"apple-watch-ultra-2\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 799.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"31\",\n        title: \"Apple Watch Series 9\",\n        slug: \"apple-watch-series-9\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"32\",\n        title: \"Samsung Galaxy Watch 6\",\n        slug: \"galaxy-watch-6\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 329.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"33\",\n        title: \"Garmin Fenix 7X\",\n        slug: \"garmin-fenix-7x\",\n        brand: {\n            name: \"Garmin\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"34\",\n        title: \"Fitbit Sense 2\",\n        slug: \"fitbit-sense-2\",\n        brand: {\n            name: \"Fitbit\"\n        },\n        variants: [\n            {\n                price: 299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // SMART HOME & IOT\n    {\n        id: \"35\",\n        title: \"Amazon Echo Studio\",\n        slug: \"amazon-echo-studio\",\n        brand: {\n            name: \"Amazon\"\n        },\n        variants: [\n            {\n                price: 199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"36\",\n        title: \"Google Nest Hub Max\",\n        slug: \"google-nest-hub-max\",\n        brand: {\n            name: \"Google\"\n        },\n        variants: [\n            {\n                price: 229.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"37\",\n        title: \"Philips Hue Starter Kit\",\n        slug: \"philips-hue-starter-kit\",\n        brand: {\n            name: \"Philips\"\n        },\n        variants: [\n            {\n                price: 199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"38\",\n        title: \"Ring Video Doorbell Pro 2\",\n        slug: \"ring-video-doorbell-pro-2\",\n        brand: {\n            name: \"Ring\"\n        },\n        variants: [\n            {\n                price: 249.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"39\",\n        title: \"Nest Thermostat\",\n        slug: \"nest-thermostat\",\n        brand: {\n            name: \"Google\"\n        },\n        variants: [\n            {\n                price: 129.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // CAMERAS & PHOTOGRAPHY\n    {\n        id: \"40\",\n        title: \"Canon EOS R5\",\n        slug: \"canon-eos-r5\",\n        brand: {\n            name: \"Canon\"\n        },\n        variants: [\n            {\n                price: 3899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"41\",\n        title: \"Sony A7R V\",\n        slug: \"sony-a7r-v\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 3899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"42\",\n        title: \"Nikon Z9\",\n        slug: \"nikon-z9\",\n        brand: {\n            name: \"Nikon\"\n        },\n        variants: [\n            {\n                price: 5499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"43\",\n        title: \"Fujifilm X-T5\",\n        slug: \"fujifilm-x-t5\",\n        brand: {\n            name: \"Fujifilm\"\n        },\n        variants: [\n            {\n                price: 1699.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"44\",\n        title: \"GoPro Hero 12\",\n        slug: \"gopro-hero-12\",\n        brand: {\n            name: \"GoPro\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // TV & HOME THEATER\n    {\n        id: \"45\",\n        title: 'Samsung 65\" Neo QLED 8K',\n        slug: \"samsung-65-neo-qled-8k\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 2999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"46\",\n        title: 'LG 77\" OLED C3',\n        slug: \"lg-77-oled-c3\",\n        brand: {\n            name: \"LG\"\n        },\n        variants: [\n            {\n                price: 3499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"47\",\n        title: 'Sony 85\" X95L',\n        slug: \"sony-85-x95l\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 2799.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"48\",\n        title: \"Sonos Arc Soundbar\",\n        slug: \"sonos-arc-soundbar\",\n        brand: {\n            name: \"Sonos\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // COMPUTER COMPONENTS\n    {\n        id: \"49\",\n        title: \"NVIDIA RTX 4090\",\n        slug: \"nvidia-rtx-4090\",\n        brand: {\n            name: \"NVIDIA\"\n        },\n        variants: [\n            {\n                price: 1599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"50\",\n        title: \"AMD Ryzen 9 7950X\",\n        slug: \"amd-ryzen-9-7950x\",\n        brand: {\n            name: \"AMD\"\n        },\n        variants: [\n            {\n                price: 699.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"51\",\n        title: \"Intel Core i9-14900K\",\n        slug: \"intel-core-i9-14900k\",\n        brand: {\n            name: \"Intel\"\n        },\n        variants: [\n            {\n                price: 589.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"52\",\n        title: \"ASUS ROG Strix Z790-E\",\n        slug: \"asus-rog-strix-z790-e\",\n        brand: {\n            name: \"ASUS\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // MONITORS & DISPLAYS\n    {\n        id: \"53\",\n        title: \"Apple Studio Display\",\n        slug: \"apple-studio-display\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"54\",\n        title: 'Dell UltraSharp 32\" 4K',\n        slug: \"dell-ultrasharp-32-4k\",\n        brand: {\n            name: \"Dell\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"55\",\n        title: \"ASUS ProArt PA32UCG\",\n        slug: \"asus-proart-pa32ucg\",\n        brand: {\n            name: \"ASUS\"\n        },\n        variants: [\n            {\n                price: 2999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"56\",\n        title: \"Samsung Odyssey G9\",\n        slug: \"samsung-odyssey-g9\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // KEYBOARDS & MICE\n    {\n        id: \"57\",\n        title: \"Logitech MX Master 3S\",\n        slug: \"logitech-mx-master-3s\",\n        brand: {\n            name: \"Logitech\"\n        },\n        variants: [\n            {\n                price: 99.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"58\",\n        title: \"Apple Magic Keyboard\",\n        slug: \"apple-magic-keyboard\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"59\",\n        title: \"Keychron K8 Pro\",\n        slug: \"keychron-k8-pro\",\n        brand: {\n            name: \"Keychron\"\n        },\n        variants: [\n            {\n                price: 109.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"60\",\n        title: \"Razer DeathAdder V3\",\n        slug: \"razer-deathadder-v3\",\n        brand: {\n            name: \"Razer\"\n        },\n        variants: [\n            {\n                price: 89.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // STORAGE & MEMORY\n    {\n        id: \"61\",\n        title: \"Samsung 990 PRO 2TB SSD\",\n        slug: \"samsung-990-pro-2tb-ssd\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"62\",\n        title: \"WD Black SN850X 4TB\",\n        slug: \"wd-black-sn850x-4tb\",\n        brand: {\n            name: \"Western Digital\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"63\",\n        title: \"Corsair Vengeance 32GB DDR5\",\n        slug: \"corsair-vengeance-32gb-ddr5\",\n        brand: {\n            name: \"Corsair\"\n        },\n        variants: [\n            {\n                price: 149.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // VR & AR\n    {\n        id: \"64\",\n        title: \"Meta Quest 3\",\n        slug: \"meta-quest-3\",\n        brand: {\n            name: \"Meta\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"65\",\n        title: \"Apple Vision Pro\",\n        slug: \"apple-vision-pro\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 3499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"66\",\n        title: \"PICO 4 Enterprise\",\n        slug: \"pico-4-enterprise\",\n        brand: {\n            name: \"ByteDance\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // DRONES & RC\n    {\n        id: \"67\",\n        title: \"DJI Mavic 3 Pro\",\n        slug: \"dji-mavic-3-pro\",\n        brand: {\n            name: \"DJI\"\n        },\n        variants: [\n            {\n                price: 2199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"68\",\n        title: \"DJI Mini 4 Pro\",\n        slug: \"dji-mini-4-pro\",\n        brand: {\n            name: \"DJI\"\n        },\n        variants: [\n            {\n                price: 759.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    // POWER & CHARGING\n    {\n        id: \"69\",\n        title: \"Anker PowerCore 26800\",\n        slug: \"anker-powercore-26800\",\n        brand: {\n            name: \"Anker\"\n        },\n        variants: [\n            {\n                price: 65.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1609592806596-4d1b5e5e5e5e?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"70\",\n        title: \"Apple MagSafe Charger\",\n        slug: \"apple-magsafe-charger\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 39.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1609592806596-4d1b5e5e5e5e?w=400&h=300&fit=crop\"\n            }\n        ]\n    }\n];\nfunction Home() {\n    // Show first 24 products on homepage for better performance\n    const featuredProducts = mockProducts.slice(0, 24);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"1rem\",\n            maxWidth: \"1400px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                style: {\n                    textAlign: \"center\",\n                    marginBottom: \"3rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"2.5rem\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        children: \"ElectroHub - Premium Electronics Marketplace\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: \"1.2rem\",\n                            color: \"#666\"\n                        },\n                        children: \"\\uD83D\\uDD25 70+ Premium Products | \\uD83D\\uDCF1 Smartphones | \\uD83D\\uDCBB Laptops | \\uD83C\\uDFA7 Audio | \\uD83C\\uDFAE Gaming | \\uD83D\\uDCFA Smart Home | \\uD83D\\uDCF7 Cameras | ⌚ Wearables\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n                            color: \"white\",\n                            padding: \"1rem\",\n                            borderRadius: \"12px\",\n                            margin: \"1rem 0\",\n                            fontSize: \"1.1rem\"\n                        },\n                        children: [\n                            \"✨ \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Comprehensive Electronics Library\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 13\n                            }, this),\n                            \" - From $39.99 to $5,499.99 | Free Shipping on Orders $50+\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                lineNumber: 628,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                \"aria-label\": \"Categories\",\n                style: {\n                    marginBottom: \"3rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: \"1.8rem\",\n                            marginBottom: \"1.5rem\",\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            \"\\uD83D\\uDECD️ Shop by Category (\",\n                            mockCategories.length,\n                            \" Categories)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(180px, 1fr))\",\n                            gap: \"1rem\"\n                        },\n                        children: mockCategories.map((c)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: `/category/${c.slug}`,\n                                \"aria-label\": `Browse ${c.name}`,\n                                style: {\n                                    padding: \"1.2rem\",\n                                    border: \"2px solid #e1e5e9\",\n                                    borderRadius: \"12px\",\n                                    textAlign: \"center\",\n                                    textDecoration: \"none\",\n                                    color: \"#333\",\n                                    backgroundColor: \"#ffffff\",\n                                    boxShadow: \"0 2px 8px rgba(0,0,0,0.1)\",\n                                    transition: \"all 0.3s ease\",\n                                    fontWeight: \"500\"\n                                },\n                                children: c.name\n                            }, c.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                lineNumber: 645,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                \"aria-label\": \"Products\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            fontSize: \"1.8rem\",\n                            marginBottom: \"1.5rem\",\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            \"\\uD83C\\uDF1F Featured Products (\",\n                            mockProducts.length,\n                            \" Total Products Available)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            textAlign: \"center\",\n                            color: \"#666\",\n                            marginBottom: \"2rem\"\n                        },\n                        children: [\n                            \"Showing \",\n                            featuredProducts.length,\n                            \" of \",\n                            mockProducts.length,\n                            \" premium electronics from top brands\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 682,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n                            gap: \"1.5rem\"\n                        },\n                        children: featuredProducts.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                product: p\n                            }, p.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            marginTop: \"3rem\",\n                            padding: \"2rem\",\n                            backgroundColor: \"#f8f9fa\",\n                            borderRadius: \"12px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: \"1rem\"\n                                },\n                                children: [\n                                    \"Want to see all \",\n                                    mockProducts.length,\n                                    \" products?\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: \"#666\",\n                                    marginBottom: \"1.5rem\"\n                                },\n                                children: \"Browse our complete catalog with advanced filtering by category, brand, and price range\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                                lineNumber: 703,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/products\",\n                                style: {\n                                    display: \"inline-block\",\n                                    padding: \"12px 24px\",\n                                    backgroundColor: \"#007bff\",\n                                    color: \"white\",\n                                    textDecoration: \"none\",\n                                    borderRadius: \"8px\",\n                                    fontWeight: \"bold\"\n                                },\n                                children: \"View All Products →\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                lineNumber: 678,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n        lineNumber: 627,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/currency */ \"(rsc)/./src/lib/currency.ts\");\n\n\n\nfunction ProductCard({ product }) {\n    const img = product.media?.[0]?.url || \"https://via.placeholder.com/400x300?text=ElectroHub\";\n    const price = Number(product.variants?.[0]?.price ?? 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        style: {\n            border: \"1px solid #ddd\",\n            borderRadius: 8,\n            overflow: \"hidden\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: `/product/${product.slug}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: img,\n                    alt: product.title,\n                    width: 400,\n                    height: 300,\n                    style: {\n                        width: \"100%\",\n                        height: \"auto\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"0.75rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: 0\n                            },\n                            children: product.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: 0\n                            },\n                            children: product.brand?.name\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatMoney)(price)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ProductCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx#SkipLink`);


/***/ }),

/***/ "(rsc)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatMoney: () => (/* binding */ formatMoney)\n/* harmony export */ });\nfunction formatMoney(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2N1cnJlbmN5LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxTQUFTQSxZQUFZQyxNQUFjLEVBQUVDLFdBQVcsS0FBSyxFQUFFQyxTQUFTLE9BQU87SUFDNUUsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUNGLFFBQVE7UUFBRUcsT0FBTztRQUFZSjtJQUFTLEdBQUdLLE1BQU0sQ0FBQ047QUFDL0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2xpYi9jdXJyZW5jeS50cz81OWM3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBmb3JtYXRNb25leShhbW91bnQ6IG51bWJlciwgY3VycmVuY3kgPSAnVVNEJywgbG9jYWxlID0gJ2VuLVVTJykge1xuICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KGxvY2FsZSwgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3kgfSkuZm9ybWF0KGFtb3VudCk7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0TW9uZXkiLCJhbW91bnQiLCJjdXJyZW5jeSIsImxvY2FsZSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImZvcm1hdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/currency.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();