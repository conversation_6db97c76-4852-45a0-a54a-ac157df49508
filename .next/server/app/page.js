/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SkipLink.tsx */ \"(ssr)/./src/components/SkipLink.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRlNraXBMaW5rLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraXBMaW5rJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEc7QUFDMUc7QUFDQSxzS0FBOEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLz9mNWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lwTGlua1wiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUEwRyIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZWN0cm9odWItbWFya2V0cGxhY2UvPzk2NmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvQXBwbGUvRGVza3RvcC9FbGVjdHJvSHViL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2xpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SkipLink auto */ \nfunction SkipLink() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main\",\n        style: {\n            position: \"absolute\",\n            left: -9999,\n            top: -9999\n        },\n        onFocus: (e)=>{\n            e.currentTarget.style.left = \"8px\";\n            e.currentTarget.style.top = \"8px\";\n            e.currentTarget.style.background = \"white\";\n            e.currentTarget.style.padding = \"8px\";\n        },\n        children: \"Skip to content\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ta2lwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUVDLE1BQUs7UUFBUUMsT0FBTztZQUFFQyxVQUFVO1lBQVlDLE1BQU0sQ0FBQztZQUFNQyxLQUFLLENBQUM7UUFBSztRQUFHQyxTQUFTLENBQUNDO1lBQ2pGQSxFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNFLElBQUksR0FBRztZQUNuREcsRUFBRUMsYUFBYSxDQUF1Qk4sS0FBSyxDQUFDRyxHQUFHLEdBQUc7WUFDbERFLEVBQUVDLGFBQWEsQ0FBdUJOLEtBQUssQ0FBQ08sVUFBVSxHQUFHO1lBQ3pERixFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNRLE9BQU8sR0FBRztRQUN6RDtrQkFBRzs7Ozs7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4Pzk5NTEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2tpcExpbmsoKSB7XG4gIHJldHVybiAoXG4gICAgPGEgaHJlZj1cIiNtYWluXCIgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGxlZnQ6IC05OTk5LCB0b3A6IC05OTk5IH19IG9uRm9jdXM9eyhlKSA9PiB7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS5sZWZ0ID0gJzhweCc7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS50b3AgPSAnOHB4JztcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnQpLnN0eWxlLmJhY2tncm91bmQgPSAnd2hpdGUnO1xuICAgICAgKGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCkuc3R5bGUucGFkZGluZyA9ICc4cHgnO1xuICAgIH19PlNraXAgdG8gY29udGVudDwvYT5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTa2lwTGluayIsImEiLCJocmVmIiwic3R5bGUiLCJwb3NpdGlvbiIsImxlZnQiLCJ0b3AiLCJvbkZvY3VzIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJiYWNrZ3JvdW5kIiwicGFkZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SkipLink.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1cc9da9c511c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYzlkYTljNTExY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SkipLink */ \"(rsc)/./src/components/SkipLink.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ElectroHub — Electronics Marketplace\",\n    description: \"Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3000\"),\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SkipLink__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    \"aria-label\": \"Site header\",\n                    style: {\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        \"aria-label\": \"Main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"ElectroHub\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            \" | \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 48\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    id: \"main\",\n                    style: {\n                        minHeight: \"60vh\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    style: {\n                        padding: \"2rem\",\n                        borderTop: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" ElectroHub \\xb7 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 60\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProductCard */ \"(rsc)/./src/components/ProductCard.tsx\");\n\n\n\n// Mock data for preview\nconst mockCategories = [\n    {\n        id: \"1\",\n        name: \"Smartphones\",\n        slug: \"smartphones\"\n    },\n    {\n        id: \"2\",\n        name: \"Laptops\",\n        slug: \"laptops\"\n    },\n    {\n        id: \"3\",\n        name: \"Audio\",\n        slug: \"audio\"\n    },\n    {\n        id: \"4\",\n        name: \"Gaming\",\n        slug: \"gaming\"\n    },\n    {\n        id: \"5\",\n        name: \"Accessories\",\n        slug: \"accessories\"\n    },\n    {\n        id: \"6\",\n        name: \"Tablets\",\n        slug: \"tablets\"\n    },\n    {\n        id: \"7\",\n        name: \"Wearables\",\n        slug: \"wearables\"\n    },\n    {\n        id: \"8\",\n        name: \"Smart Home\",\n        slug: \"smart-home\"\n    }\n];\nconst mockProducts = [\n    {\n        id: \"1\",\n        title: \"iPhone 15 Pro\",\n        slug: \"iphone-15-pro\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        title: \"MacBook Air M3\",\n        slug: \"macbook-air-m3\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"3\",\n        title: \"AirPods Pro\",\n        slug: \"airpods-pro\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 249.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"4\",\n        title: \"Samsung Galaxy S24\",\n        slug: \"galaxy-s24\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"5\",\n        title: \"Sony WH-1000XM5\",\n        slug: \"sony-wh-1000xm5\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"6\",\n        title: \"Dell XPS 13\",\n        slug: \"dell-xps-13\",\n        brand: {\n            name: \"Dell\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"7\",\n        title: 'iPad Pro 12.9\"',\n        slug: \"ipad-pro-12-9\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1099.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"8\",\n        title: \"Nintendo Switch OLED\",\n        slug: \"nintendo-switch-oled\",\n        brand: {\n            name: \"Nintendo\"\n        },\n        variants: [\n            {\n                price: 349.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"9\",\n        title: \"Apple Watch Series 9\",\n        slug: \"apple-watch-series-9\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"10\",\n        title: \"Samsung 4K Monitor\",\n        slug: \"samsung-4k-monitor\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"11\",\n        title: \"Logitech MX Master 3\",\n        slug: \"logitech-mx-master-3\",\n        brand: {\n            name: \"Logitech\"\n        },\n        variants: [\n            {\n                price: 99.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop\"\n            }\n        ]\n    },\n    {\n        id: \"12\",\n        title: \"Google Pixel 8 Pro\",\n        slug: \"google-pixel-8-pro\",\n        brand: {\n            name: \"Google\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ]\n    }\n];\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"ElectroHub - Electronics Marketplace\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Phones \\xb7 Laptops \\xb7 Audio \\xb7 Gaming \\xb7 Accessories\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                \"aria-label\": \"Categories\",\n                style: {\n                    marginBottom: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Shop by Category\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(150px, 1fr))\",\n                            gap: \"1rem\"\n                        },\n                        children: mockCategories.map((c)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: `/category/${c.slug}`,\n                                \"aria-label\": `Browse ${c.name}`,\n                                style: {\n                                    padding: \"1rem\",\n                                    border: \"1px solid #ddd\",\n                                    borderRadius: \"8px\",\n                                    textAlign: \"center\",\n                                    textDecoration: \"none\",\n                                    color: \"inherit\",\n                                    backgroundColor: \"#f9f9f9\"\n                                },\n                                children: c.name\n                            }, c.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                \"aria-label\": \"Products\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Featured Products\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                            gap: \"1rem\"\n                        },\n                        children: mockProducts.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                product: p\n                            }, p.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/page.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/currency */ \"(rsc)/./src/lib/currency.ts\");\n\n\n\nfunction ProductCard({ product }) {\n    const img = product.media?.[0]?.url || \"https://via.placeholder.com/400x300?text=ElectroHub\";\n    const price = Number(product.variants?.[0]?.price ?? 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        style: {\n            border: \"1px solid #ddd\",\n            borderRadius: 8,\n            overflow: \"hidden\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: `/product/${product.slug}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: img,\n                    alt: product.title,\n                    width: 400,\n                    height: 300,\n                    style: {\n                        width: \"100%\",\n                        height: \"auto\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"0.75rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: 0\n                            },\n                            children: product.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: 0\n                            },\n                            children: product.brand?.name\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatMoney)(price)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ProductCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx#SkipLink`);


/***/ }),

/***/ "(rsc)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatMoney: () => (/* binding */ formatMoney)\n/* harmony export */ });\nfunction formatMoney(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2N1cnJlbmN5LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxTQUFTQSxZQUFZQyxNQUFjLEVBQUVDLFdBQVcsS0FBSyxFQUFFQyxTQUFTLE9BQU87SUFDNUUsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUNGLFFBQVE7UUFBRUcsT0FBTztRQUFZSjtJQUFTLEdBQUdLLE1BQU0sQ0FBQ047QUFDL0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2xpYi9jdXJyZW5jeS50cz81OWM3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBmb3JtYXRNb25leShhbW91bnQ6IG51bWJlciwgY3VycmVuY3kgPSAnVVNEJywgbG9jYWxlID0gJ2VuLVVTJykge1xuICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KGxvY2FsZSwgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3kgfSkuZm9ybWF0KGFtb3VudCk7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0TW9uZXkiLCJhbW91bnQiLCJjdXJyZW5jeSIsImxvY2FsZSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImZvcm1hdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/currency.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();