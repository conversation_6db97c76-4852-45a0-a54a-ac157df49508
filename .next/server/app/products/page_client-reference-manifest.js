globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/products/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SkipLink.tsx":{"*":{"id":"(ssr)/./src/components/SkipLink.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/FilterSidebar.tsx":{"*":{"id":"(ssr)/./src/components/FilterSidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AddToCartButton.tsx":{"*":{"id":"(ssr)/./src/components/AddToCartButton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CheckoutButton.tsx":{"*":{"id":"(ssr)/./src/components/CheckoutButton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/price-match/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/price-match/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/products/add/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/products/add/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/bulk-import/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/bulk-import/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx":{"id":"(app-pages-browser)/./src/components/SkipLink.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx":{"id":"(app-pages-browser)/./src/components/FilterSidebar.tsx","name":"*","chunks":["app/products/page","static/chunks/app/products/page.js"],"async":false},"/Users/<USER>/Desktop/ElectroHub/src/components/AddToCartButton.tsx":{"id":"(app-pages-browser)/./src/components/AddToCartButton.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/ElectroHub/src/components/CheckoutButton.tsx":{"id":"(app-pages-browser)/./src/components/CheckoutButton.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx":{"id":"(app-pages-browser)/./src/app/admin/price-match/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx":{"id":"(app-pages-browser)/./src/app/admin/products/add/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/ElectroHub/src/app/admin/bulk-import/page.tsx":{"id":"(app-pages-browser)/./src/app/admin/bulk-import/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Desktop/ElectroHub/src/":[],"/Users/<USER>/Desktop/ElectroHub/src/app/page":[],"/Users/<USER>/Desktop/ElectroHub/src/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Desktop/ElectroHub/src/app/products/page":[]}}