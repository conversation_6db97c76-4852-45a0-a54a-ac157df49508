/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/products/page";
exports.ids = ["app/products/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'products',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/products/page.tsx */ \"(rsc)/./src/app/products/page.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/products/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/products/page\",\n        pathname: \"/products\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmFwcC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRkFwcGxlJTJGRGVza3RvcCUyRkVsZWN0cm9IdWIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQTJIO0FBQzNIO0FBQ0Esb09BQTRIO0FBQzVIO0FBQ0EsME9BQStIO0FBQy9IO0FBQ0Esd09BQThIO0FBQzlIO0FBQ0Esa1BBQW1JO0FBQ25JO0FBQ0Esc1FBQTZJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8/NTQyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9hcHAtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvQXBwbGUvRGVza3RvcC9FbGVjdHJvSHViL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9yZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SkipLink.tsx */ \"(ssr)/./src/components/SkipLink.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRlNraXBMaW5rLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraXBMaW5rJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEc7QUFDMUc7QUFDQSxzS0FBOEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLz9mNWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lwTGlua1wiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FFilterSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FFilterSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/FilterSidebar.tsx */ \"(ssr)/./src/components/FilterSidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRkZpbHRlclNpZGViYXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQTBHO0FBQzFHO0FBQ0EsZ0xBQWtJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8/MDBkOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvRmlsdGVyU2lkZWJhci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FFilterSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/FilterSidebar.tsx":
/*!******************************************!*\
  !*** ./src/components/FilterSidebar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FilterSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction FilterSidebar({ brands }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const sp = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const [brand, setBrand] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(sp.get(\"brand\") || \"\");\n    const [min, setMin] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(sp.get(\"min\") || \"\");\n    const [max, setMax] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(sp.get(\"max\") || \"\");\n    function apply() {\n        const q = new URLSearchParams(sp.toString());\n        if (brand) q.set(\"brand\", brand);\n        else q.delete(\"brand\");\n        if (min) q.set(\"min\", min);\n        else q.delete(\"min\");\n        if (max) q.set(\"max\", max);\n        else q.delete(\"max\");\n        router.push(`?${q.toString()}`);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        \"aria-label\": \"Filters\",\n        style: {\n            borderRight: \"1px solid #eee\",\n            paddingRight: \"1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                children: [\n                    \"Brand\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: brand,\n                        onChange: (e)=>setBrand(e.target.value),\n                        \"aria-label\": \"Filter by brand\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"All\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            brands.map((b)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: b,\n                                    children: b\n                                }, b, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 30\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        children: [\n                            \"Min $\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                value: min,\n                                onChange: (e)=>setMin(e.target.value),\n                                inputMode: \"numeric\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        children: [\n                            \"Max $\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                value: max,\n                                onChange: (e)=>setMax(e.target.value),\n                                inputMode: \"numeric\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: apply,\n                children: \"Apply\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FilterSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SkipLink auto */ \nfunction SkipLink() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main\",\n        style: {\n            position: \"absolute\",\n            left: -9999,\n            top: -9999\n        },\n        onFocus: (e)=>{\n            e.currentTarget.style.left = \"8px\";\n            e.currentTarget.style.top = \"8px\";\n            e.currentTarget.style.background = \"white\";\n            e.currentTarget.style.padding = \"8px\";\n        },\n        children: \"Skip to content\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ta2lwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUVDLE1BQUs7UUFBUUMsT0FBTztZQUFFQyxVQUFVO1lBQVlDLE1BQU0sQ0FBQztZQUFNQyxLQUFLLENBQUM7UUFBSztRQUFHQyxTQUFTLENBQUNDO1lBQ2pGQSxFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNFLElBQUksR0FBRztZQUNuREcsRUFBRUMsYUFBYSxDQUF1Qk4sS0FBSyxDQUFDRyxHQUFHLEdBQUc7WUFDbERFLEVBQUVDLGFBQWEsQ0FBdUJOLEtBQUssQ0FBQ08sVUFBVSxHQUFHO1lBQ3pERixFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNRLE9BQU8sR0FBRztRQUN6RDtrQkFBRzs7Ozs7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4Pzk5NTEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2tpcExpbmsoKSB7XG4gIHJldHVybiAoXG4gICAgPGEgaHJlZj1cIiNtYWluXCIgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGxlZnQ6IC05OTk5LCB0b3A6IC05OTk5IH19IG9uRm9jdXM9eyhlKSA9PiB7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS5sZWZ0ID0gJzhweCc7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS50b3AgPSAnOHB4JztcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnQpLnN0eWxlLmJhY2tncm91bmQgPSAnd2hpdGUnO1xuICAgICAgKGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCkuc3R5bGUucGFkZGluZyA9ICc4cHgnO1xuICAgIH19PlNraXAgdG8gY29udGVudDwvYT5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTa2lwTGluayIsImEiLCJocmVmIiwic3R5bGUiLCJwb3NpdGlvbiIsImxlZnQiLCJ0b3AiLCJvbkZvY3VzIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJiYWNrZ3JvdW5kIiwicGFkZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SkipLink.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1cc9da9c511c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYzlkYTljNTExY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SkipLink */ \"(rsc)/./src/components/SkipLink.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ElectroHub — Electronics Marketplace\",\n    description: \"Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3000\"),\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SkipLink__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    \"aria-label\": \"Site header\",\n                    style: {\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        \"aria-label\": \"Main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"ElectroHub\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            \" | \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 48\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    id: \"main\",\n                    style: {\n                        minHeight: \"60vh\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    style: {\n                        padding: \"2rem\",\n                        borderTop: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" ElectroHub \\xb7 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 60\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AllProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ProductCard */ \"(rsc)/./src/components/ProductCard.tsx\");\n/* harmony import */ var _components_FilterSidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/FilterSidebar */ \"(rsc)/./src/components/FilterSidebar.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n// Complete product catalog - all 70+ products\nconst allProducts = [\n    // SMARTPHONES\n    {\n        id: \"1\",\n        title: \"iPhone 15 Pro Max\",\n        slug: \"iphone-15-pro-max\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"2\",\n        title: \"iPhone 15 Pro\",\n        slug: \"iphone-15-pro\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"3\",\n        title: \"Samsung Galaxy S24 Ultra\",\n        slug: \"galaxy-s24-ultra\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"4\",\n        title: \"Samsung Galaxy S24\",\n        slug: \"galaxy-s24\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"5\",\n        title: \"Google Pixel 8 Pro\",\n        slug: \"google-pixel-8-pro\",\n        brand: {\n            name: \"Google\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"6\",\n        title: \"OnePlus 12\",\n        slug: \"oneplus-12\",\n        brand: {\n            name: \"OnePlus\"\n        },\n        variants: [\n            {\n                price: 799.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    {\n        id: \"7\",\n        title: \"Xiaomi 14 Ultra\",\n        slug: \"xiaomi-14-ultra\",\n        brand: {\n            name: \"Xiaomi\"\n        },\n        variants: [\n            {\n                price: 699.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"smartphones\"\n    },\n    // LAPTOPS\n    {\n        id: \"8\",\n        title: 'MacBook Pro 16\" M3 Max',\n        slug: \"macbook-pro-16-m3-max\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 3499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops\"\n    },\n    {\n        id: \"9\",\n        title: \"MacBook Air M3\",\n        slug: \"macbook-air-m3\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops\"\n    },\n    {\n        id: \"10\",\n        title: \"Dell XPS 13 Plus\",\n        slug: \"dell-xps-13-plus\",\n        brand: {\n            name: \"Dell\"\n        },\n        variants: [\n            {\n                price: 1399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops\"\n    },\n    {\n        id: \"11\",\n        title: \"ThinkPad X1 Carbon\",\n        slug: \"thinkpad-x1-carbon\",\n        brand: {\n            name: \"Lenovo\"\n        },\n        variants: [\n            {\n                price: 1599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops\"\n    },\n    {\n        id: \"12\",\n        title: \"Surface Laptop 5\",\n        slug: \"surface-laptop-5\",\n        brand: {\n            name: \"Microsoft\"\n        },\n        variants: [\n            {\n                price: 1299.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops\"\n    },\n    {\n        id: \"13\",\n        title: \"ASUS ROG Zephyrus G14\",\n        slug: \"asus-rog-zephyrus-g14\",\n        brand: {\n            name: \"ASUS\"\n        },\n        variants: [\n            {\n                price: 1899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops\"\n    },\n    {\n        id: \"14\",\n        title: \"HP Spectre x360\",\n        slug: \"hp-spectre-x360\",\n        brand: {\n            name: \"HP\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"laptops\"\n    },\n    // AUDIO\n    {\n        id: \"15\",\n        title: \"AirPods Pro 2\",\n        slug: \"airpods-pro-2\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 249.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio\"\n    },\n    {\n        id: \"16\",\n        title: \"Sony WH-1000XM5\",\n        slug: \"sony-wh-1000xm5\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio\"\n    },\n    {\n        id: \"17\",\n        title: \"Bose QuietComfort Ultra\",\n        slug: \"bose-quietcomfort-ultra\",\n        brand: {\n            name: \"Bose\"\n        },\n        variants: [\n            {\n                price: 429.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio\"\n    },\n    {\n        id: \"18\",\n        title: \"Sennheiser HD 800S\",\n        slug: \"sennheiser-hd-800s\",\n        brand: {\n            name: \"Sennheiser\"\n        },\n        variants: [\n            {\n                price: 1699.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio\"\n    },\n    {\n        id: \"19\",\n        title: \"Audio-Technica ATH-M50x\",\n        slug: \"audio-technica-ath-m50x\",\n        brand: {\n            name: \"Audio-Technica\"\n        },\n        variants: [\n            {\n                price: 149.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio\"\n    },\n    {\n        id: \"20\",\n        title: \"Beats Studio Pro\",\n        slug: \"beats-studio-pro\",\n        brand: {\n            name: \"Beats\"\n        },\n        variants: [\n            {\n                price: 349.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"audio\"\n    },\n    // GAMING\n    {\n        id: \"21\",\n        title: \"PlayStation 5\",\n        slug: \"playstation-5\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"gaming\"\n    },\n    {\n        id: \"22\",\n        title: \"Xbox Series X\",\n        slug: \"xbox-series-x\",\n        brand: {\n            name: \"Microsoft\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"gaming\"\n    },\n    {\n        id: \"23\",\n        title: \"Nintendo Switch OLED\",\n        slug: \"nintendo-switch-oled\",\n        brand: {\n            name: \"Nintendo\"\n        },\n        variants: [\n            {\n                price: 349.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"gaming\"\n    },\n    {\n        id: \"24\",\n        title: \"Steam Deck OLED\",\n        slug: \"steam-deck-oled\",\n        brand: {\n            name: \"Valve\"\n        },\n        variants: [\n            {\n                price: 549.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"gaming\"\n    },\n    // TABLETS\n    {\n        id: \"25\",\n        title: 'iPad Pro 12.9\" M2',\n        slug: \"ipad-pro-12-9-m2\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1099.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"tablets\"\n    },\n    {\n        id: \"26\",\n        title: \"iPad Air 5\",\n        slug: \"ipad-air-5\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"tablets\"\n    },\n    {\n        id: \"27\",\n        title: \"Samsung Galaxy Tab S9 Ultra\",\n        slug: \"galaxy-tab-s9-ultra\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 1199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"tablets\"\n    },\n    {\n        id: \"28\",\n        title: \"Microsoft Surface Pro 9\",\n        slug: \"surface-pro-9\",\n        brand: {\n            name: \"Microsoft\"\n        },\n        variants: [\n            {\n                price: 999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"tablets\"\n    },\n    // WEARABLES\n    {\n        id: \"30\",\n        title: \"Apple Watch Ultra 2\",\n        slug: \"apple-watch-ultra-2\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 799.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"wearables\"\n    },\n    {\n        id: \"31\",\n        title: \"Apple Watch Series 9\",\n        slug: \"apple-watch-series-9\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 399.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"wearables\"\n    },\n    {\n        id: \"32\",\n        title: \"Samsung Galaxy Watch 6\",\n        slug: \"galaxy-watch-6\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 329.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"wearables\"\n    },\n    // CAMERAS\n    {\n        id: \"40\",\n        title: \"Canon EOS R5\",\n        slug: \"canon-eos-r5\",\n        brand: {\n            name: \"Canon\"\n        },\n        variants: [\n            {\n                price: 3899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"cameras\"\n    },\n    {\n        id: \"41\",\n        title: \"Sony A7R V\",\n        slug: \"sony-a7r-v\",\n        brand: {\n            name: \"Sony\"\n        },\n        variants: [\n            {\n                price: 3899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"cameras\"\n    },\n    {\n        id: \"42\",\n        title: \"Nikon Z9\",\n        slug: \"nikon-z9\",\n        brand: {\n            name: \"Nikon\"\n        },\n        variants: [\n            {\n                price: 5499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"cameras\"\n    },\n    // TVS\n    {\n        id: \"45\",\n        title: 'Samsung 65\" Neo QLED 8K',\n        slug: \"samsung-65-neo-qled-8k\",\n        brand: {\n            name: \"Samsung\"\n        },\n        variants: [\n            {\n                price: 2999.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"tvs\"\n    },\n    {\n        id: \"46\",\n        title: 'LG 77\" OLED C3',\n        slug: \"lg-77-oled-c3\",\n        brand: {\n            name: \"LG\"\n        },\n        variants: [\n            {\n                price: 3499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"tvs\"\n    },\n    // MONITORS\n    {\n        id: \"53\",\n        title: \"Apple Studio Display\",\n        slug: \"apple-studio-display\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 1599.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"monitors\"\n    },\n    {\n        id: \"54\",\n        title: 'Dell UltraSharp 32\" 4K',\n        slug: \"dell-ultrasharp-32-4k\",\n        brand: {\n            name: \"Dell\"\n        },\n        variants: [\n            {\n                price: 899.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"monitors\"\n    },\n    // VR\n    {\n        id: \"64\",\n        title: \"Meta Quest 3\",\n        slug: \"meta-quest-3\",\n        brand: {\n            name: \"Meta\"\n        },\n        variants: [\n            {\n                price: 499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"vr\"\n    },\n    {\n        id: \"65\",\n        title: \"Apple Vision Pro\",\n        slug: \"apple-vision-pro\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 3499.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"vr\"\n    },\n    // ACCESSORIES\n    {\n        id: \"57\",\n        title: \"Logitech MX Master 3S\",\n        slug: \"logitech-mx-master-3s\",\n        brand: {\n            name: \"Logitech\"\n        },\n        variants: [\n            {\n                price: 99.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"accessories\"\n    },\n    {\n        id: \"58\",\n        title: \"Apple Magic Keyboard\",\n        slug: \"apple-magic-keyboard\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 199.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"accessories\"\n    },\n    {\n        id: \"70\",\n        title: \"Apple MagSafe Charger\",\n        slug: \"apple-magsafe-charger\",\n        brand: {\n            name: \"Apple\"\n        },\n        variants: [\n            {\n                price: 39.99\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1609592806596-4d1b5e5e5e5e?w=400&h=300&fit=crop\"\n            }\n        ],\n        category: \"accessories\"\n    }\n];\nfunction AllProductsPage({ searchParams }) {\n    // Get unique brands and categories\n    const allBrands = [\n        ...new Set(allProducts.map((p)=>p.brand.name))\n    ].sort();\n    const allCategories = [\n        ...new Set(allProducts.map((p)=>p.category))\n    ];\n    // Apply filters\n    let filteredProducts = allProducts;\n    if (searchParams.brand) {\n        filteredProducts = filteredProducts.filter((p)=>p.brand.name.toLowerCase() === searchParams.brand.toLowerCase());\n    }\n    if (searchParams.category) {\n        filteredProducts = filteredProducts.filter((p)=>p.category === searchParams.category);\n    }\n    if (searchParams.min) {\n        const minPrice = parseFloat(searchParams.min);\n        filteredProducts = filteredProducts.filter((p)=>p.variants[0].price >= minPrice);\n    }\n    if (searchParams.max) {\n        const maxPrice = parseFloat(searchParams.max);\n        filteredProducts = filteredProducts.filter((p)=>p.variants[0].price <= maxPrice);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: \"1400px\",\n            margin: \"0 auto\",\n            padding: \"1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n                    color: \"white\",\n                    padding: \"2rem\",\n                    borderRadius: \"12px\",\n                    marginBottom: \"2rem\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"2.5rem\",\n                            margin: \"0 0 1rem 0\"\n                        },\n                        children: \"\\uD83D\\uDECD️ Complete Product Catalog\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            fontSize: \"1.2rem\",\n                            opacity: 0.9\n                        },\n                        children: [\n                            \"Browse all \",\n                            allProducts.length,\n                            \" premium electronics from top brands\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"280px 1fr\",\n                    gap: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterSidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                brands: allBrands\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: \"2rem\",\n                                    padding: \"1.5rem\",\n                                    backgroundColor: \"#f8f9fa\",\n                                    borderRadius: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            marginBottom: \"1rem\"\n                                        },\n                                        children: \"Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"0.5rem\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: \"/products\",\n                                                style: {\n                                                    textDecoration: \"none\",\n                                                    color: !searchParams.category ? \"#007bff\" : \"#666\",\n                                                    padding: \"0.5rem\",\n                                                    borderRadius: \"6px\",\n                                                    backgroundColor: !searchParams.category ? \"#e3f2fd\" : \"transparent\",\n                                                    fontWeight: !searchParams.category ? \"bold\" : \"normal\"\n                                                },\n                                                children: [\n                                                    \"All Categories (\",\n                                                    allProducts.length,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this),\n                                            allCategories.slice(0, 8).map((category)=>{\n                                                const count = allProducts.filter((p)=>p.category === category).length;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: `/products?category=${category}`,\n                                                    style: {\n                                                        textDecoration: \"none\",\n                                                        color: searchParams.category === category ? \"#007bff\" : \"#666\",\n                                                        padding: \"0.5rem\",\n                                                        borderRadius: \"6px\",\n                                                        backgroundColor: searchParams.category === category ? \"#e3f2fd\" : \"transparent\",\n                                                        fontWeight: searchParams.category === category ? \"bold\" : \"normal\"\n                                                    },\n                                                    children: [\n                                                        category,\n                                                        \" (\",\n                                                        count,\n                                                        \")\"\n                                                    ]\n                                                }, category, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\",\n                                    marginBottom: \"1.5rem\",\n                                    padding: \"1rem\",\n                                    backgroundColor: \"#f8f9fa\",\n                                    borderRadius: \"8px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: filteredProducts.length\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" of \",\n                                            allProducts.length,\n                                            \" products\",\n                                            (searchParams.brand || searchParams.category) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"0.9rem\",\n                                                    color: \"#666\",\n                                                    marginTop: \"0.25rem\"\n                                                },\n                                                children: [\n                                                    searchParams.brand && `Brand: ${searchParams.brand}`,\n                                                    searchParams.brand && searchParams.category && \" • \",\n                                                    searchParams.category && `Category: ${searchParams.category}`\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            color: \"#666\"\n                                        },\n                                        children: \"Sorted by: Featured\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            filteredProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n                                    gap: \"1.5rem\"\n                                },\n                                children: filteredProducts.map((p)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        product: p\n                                    }, p.id, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    padding: \"3rem\",\n                                    backgroundColor: \"#f8f9fa\",\n                                    borderRadius: \"12px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"No products found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: \"#666\",\n                                            marginBottom: \"1rem\"\n                                        },\n                                        children: \"No products match your current filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/products\",\n                                        style: {\n                                            color: \"#007bff\",\n                                            textDecoration: \"none\",\n                                            fontWeight: \"bold\"\n                                        },\n                                        children: \"Clear all filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/products/page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3Byb2R1Y3RzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBbUQ7QUFDSTtBQUMxQjtBQUU3Qiw4Q0FBOEM7QUFDOUMsTUFBTUcsY0FBYztJQUNsQixjQUFjO0lBQ2Q7UUFBRUMsSUFBSTtRQUFLQyxPQUFPO1FBQXFCQyxNQUFNO1FBQXFCQyxPQUFPO1lBQUVDLE1BQU07UUFBUTtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBYztJQUMzUDtRQUFFVCxJQUFJO1FBQUtDLE9BQU87UUFBaUJDLE1BQU07UUFBaUJDLE9BQU87WUFBRUMsTUFBTTtRQUFRO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFjO0lBQ2xQO1FBQUVULElBQUk7UUFBS0MsT0FBTztRQUE0QkMsTUFBTTtRQUFvQkMsT0FBTztZQUFFQyxNQUFNO1FBQVU7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQWM7SUFDblE7UUFBRVQsSUFBSTtRQUFLQyxPQUFPO1FBQXNCQyxNQUFNO1FBQWNDLE9BQU87WUFBRUMsTUFBTTtRQUFVO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFjO0lBQ3RQO1FBQUVULElBQUk7UUFBS0MsT0FBTztRQUFzQkMsTUFBTTtRQUFzQkMsT0FBTztZQUFFQyxNQUFNO1FBQVM7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQWM7SUFDN1A7UUFBRVQsSUFBSTtRQUFLQyxPQUFPO1FBQWNDLE1BQU07UUFBY0MsT0FBTztZQUFFQyxNQUFNO1FBQVU7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQWM7SUFDOU87UUFBRVQsSUFBSTtRQUFLQyxPQUFPO1FBQW1CQyxNQUFNO1FBQW1CQyxPQUFPO1lBQUVDLE1BQU07UUFBUztRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBYztJQUV2UCxVQUFVO0lBQ1Y7UUFBRVQsSUFBSTtRQUFLQyxPQUFPO1FBQTBCQyxNQUFNO1FBQXlCQyxPQUFPO1lBQUVDLE1BQU07UUFBUTtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFpRjtTQUFFO1FBQUVDLFVBQVU7SUFBVTtJQUM3UDtRQUFFVCxJQUFJO1FBQUtDLE9BQU87UUFBa0JDLE1BQU07UUFBa0JDLE9BQU87WUFBRUMsTUFBTTtRQUFRO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQWlGO1NBQUU7UUFBRUMsVUFBVTtJQUFVO0lBQzlPO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUFvQkMsTUFBTTtRQUFvQkMsT0FBTztZQUFFQyxNQUFNO1FBQU87UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQVU7SUFDclA7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQXNCQyxNQUFNO1FBQXNCQyxPQUFPO1lBQUVDLE1BQU07UUFBUztRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBVTtJQUMzUDtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBb0JDLE1BQU07UUFBb0JDLE9BQU87WUFBRUMsTUFBTTtRQUFZO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFVO0lBQzFQO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUF5QkMsTUFBTTtRQUF5QkMsT0FBTztZQUFFQyxNQUFNO1FBQU87UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQVU7SUFDL1A7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQW1CQyxNQUFNO1FBQW1CQyxPQUFPO1lBQUVDLE1BQU07UUFBSztRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBVTtJQUVqUCxRQUFRO0lBQ1I7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQWlCQyxNQUFNO1FBQWlCQyxPQUFPO1lBQUVDLE1BQU07UUFBUTtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBUTtJQUM3TztRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBbUJDLE1BQU07UUFBbUJDLE9BQU87WUFBRUMsTUFBTTtRQUFPO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFRO0lBQ2hQO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUEyQkMsTUFBTTtRQUEyQkMsT0FBTztZQUFFQyxNQUFNO1FBQU87UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQVE7SUFDaFE7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQXNCQyxNQUFNO1FBQXNCQyxPQUFPO1lBQUVDLE1BQU07UUFBYTtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBUTtJQUM3UDtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBMkJDLE1BQU07UUFBMkJDLE9BQU87WUFBRUMsTUFBTTtRQUFpQjtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBUTtJQUMxUTtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBb0JDLE1BQU07UUFBb0JDLE9BQU87WUFBRUMsTUFBTTtRQUFRO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFRO0lBRW5QLFNBQVM7SUFDVDtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBaUJDLE1BQU07UUFBaUJDLE9BQU87WUFBRUMsTUFBTTtRQUFPO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFTO0lBQzdPO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUFpQkMsTUFBTTtRQUFpQkMsT0FBTztZQUFFQyxNQUFNO1FBQVk7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQVM7SUFDbFA7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQXdCQyxNQUFNO1FBQXdCQyxPQUFPO1lBQUVDLE1BQU07UUFBVztRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBUztJQUMvUDtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBbUJDLE1BQU07UUFBbUJDLE9BQU87WUFBRUMsTUFBTTtRQUFRO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFTO0lBRWxQLFVBQVU7SUFDVjtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBcUJDLE1BQU07UUFBb0JDLE9BQU87WUFBRUMsTUFBTTtRQUFRO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFRO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQWlGO1NBQUU7UUFBRUMsVUFBVTtJQUFVO0lBQ3BQO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUFjQyxNQUFNO1FBQWNDLE9BQU87WUFBRUMsTUFBTTtRQUFRO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQWlGO1NBQUU7UUFBRUMsVUFBVTtJQUFVO0lBQ3RPO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUErQkMsTUFBTTtRQUF1QkMsT0FBTztZQUFFQyxNQUFNO1FBQVU7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBaUY7U0FBRTtRQUFFQyxVQUFVO0lBQVU7SUFDblE7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQTJCQyxNQUFNO1FBQWlCQyxPQUFPO1lBQUVDLE1BQU07UUFBWTtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFpRjtTQUFFO1FBQUVDLFVBQVU7SUFBVTtJQUUxUCxZQUFZO0lBQ1o7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQXVCQyxNQUFNO1FBQXVCQyxPQUFPO1lBQUVDLE1BQU07UUFBUTtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFpRjtTQUFFO1FBQUVDLFVBQVU7SUFBWTtJQUMxUDtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBd0JDLE1BQU07UUFBd0JDLE9BQU87WUFBRUMsTUFBTTtRQUFRO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQWlGO1NBQUU7UUFBRUMsVUFBVTtJQUFZO0lBQzVQO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUEwQkMsTUFBTTtRQUFrQkMsT0FBTztZQUFFQyxNQUFNO1FBQVU7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU87U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBaUY7U0FBRTtRQUFFQyxVQUFVO0lBQVk7SUFFMVAsVUFBVTtJQUNWO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUFnQkMsTUFBTTtRQUFnQkMsT0FBTztZQUFFQyxNQUFNO1FBQVE7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQVU7SUFDOU87UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQWNDLE1BQU07UUFBY0MsT0FBTztZQUFFQyxNQUFNO1FBQU87UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQVU7SUFDek87UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQVlDLE1BQU07UUFBWUMsT0FBTztZQUFFQyxNQUFNO1FBQVE7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQVU7SUFFdE8sTUFBTTtJQUNOO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUEyQkMsTUFBTTtRQUEwQkMsT0FBTztZQUFFQyxNQUFNO1FBQVU7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQU07SUFDalE7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQWtCQyxNQUFNO1FBQWlCQyxPQUFPO1lBQUVDLE1BQU07UUFBSztRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBTTtJQUUxTyxXQUFXO0lBQ1g7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQXdCQyxNQUFNO1FBQXdCQyxPQUFPO1lBQUVDLE1BQU07UUFBUTtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBUTtTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBVztJQUMvUDtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBMEJDLE1BQU07UUFBeUJDLE9BQU87WUFBRUMsTUFBTTtRQUFPO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFXO0lBRWhRLEtBQUs7SUFDTDtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBZ0JDLE1BQU07UUFBZ0JDLE9BQU87WUFBRUMsTUFBTTtRQUFPO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFPO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFLO0lBQ3ZPO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUFvQkMsTUFBTTtRQUFvQkMsT0FBTztZQUFFQyxNQUFNO1FBQVE7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQVE7U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQUs7SUFFalAsY0FBYztJQUNkO1FBQUVULElBQUk7UUFBTUMsT0FBTztRQUF5QkMsTUFBTTtRQUF5QkMsT0FBTztZQUFFQyxNQUFNO1FBQVc7UUFBR0MsVUFBVTtZQUFDO2dCQUFFQyxPQUFPO1lBQU07U0FBRTtRQUFFQyxPQUFPO1lBQUM7Z0JBQUVDLEtBQUs7WUFBb0Y7U0FBRTtRQUFFQyxVQUFVO0lBQWM7SUFDclE7UUFBRVQsSUFBSTtRQUFNQyxPQUFPO1FBQXdCQyxNQUFNO1FBQXdCQyxPQUFPO1lBQUVDLE1BQU07UUFBUTtRQUFHQyxVQUFVO1lBQUM7Z0JBQUVDLE9BQU87WUFBTztTQUFFO1FBQUVDLE9BQU87WUFBQztnQkFBRUMsS0FBSztZQUFvRjtTQUFFO1FBQUVDLFVBQVU7SUFBYztJQUNqUTtRQUFFVCxJQUFJO1FBQU1DLE9BQU87UUFBeUJDLE1BQU07UUFBeUJDLE9BQU87WUFBRUMsTUFBTTtRQUFRO1FBQUdDLFVBQVU7WUFBQztnQkFBRUMsT0FBTztZQUFNO1NBQUU7UUFBRUMsT0FBTztZQUFDO2dCQUFFQyxLQUFLO1lBQW9GO1NBQUU7UUFBRUMsVUFBVTtJQUFjO0NBQ25RO0FBRWMsU0FBU0MsZ0JBQWdCLEVBQUVDLFlBQVksRUFBTztJQUMzRCxtQ0FBbUM7SUFDbkMsTUFBTUMsWUFBWTtXQUFJLElBQUlDLElBQUlkLFlBQVllLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRVosS0FBSyxDQUFDQyxJQUFJO0tBQUcsQ0FBQ1ksSUFBSTtJQUN2RSxNQUFNQyxnQkFBZ0I7V0FBSSxJQUFJSixJQUFJZCxZQUFZZSxHQUFHLENBQUNDLENBQUFBLElBQUtBLEVBQUVOLFFBQVE7S0FBRztJQUVwRSxnQkFBZ0I7SUFDaEIsSUFBSVMsbUJBQW1CbkI7SUFFdkIsSUFBSVksYUFBYVIsS0FBSyxFQUFFO1FBQ3RCZSxtQkFBbUJBLGlCQUFpQkMsTUFBTSxDQUFDSixDQUFBQSxJQUN6Q0EsRUFBRVosS0FBSyxDQUFDQyxJQUFJLENBQUNnQixXQUFXLE9BQU9ULGFBQWFSLEtBQUssQ0FBQ2lCLFdBQVc7SUFFakU7SUFFQSxJQUFJVCxhQUFhRixRQUFRLEVBQUU7UUFDekJTLG1CQUFtQkEsaUJBQWlCQyxNQUFNLENBQUNKLENBQUFBLElBQ3pDQSxFQUFFTixRQUFRLEtBQUtFLGFBQWFGLFFBQVE7SUFFeEM7SUFFQSxJQUFJRSxhQUFhVSxHQUFHLEVBQUU7UUFDcEIsTUFBTUMsV0FBV0MsV0FBV1osYUFBYVUsR0FBRztRQUM1Q0gsbUJBQW1CQSxpQkFBaUJDLE1BQU0sQ0FBQ0osQ0FBQUEsSUFBS0EsRUFBRVYsUUFBUSxDQUFDLEVBQUUsQ0FBQ0MsS0FBSyxJQUFJZ0I7SUFDekU7SUFFQSxJQUFJWCxhQUFhYSxHQUFHLEVBQUU7UUFDcEIsTUFBTUMsV0FBV0YsV0FBV1osYUFBYWEsR0FBRztRQUM1Q04sbUJBQW1CQSxpQkFBaUJDLE1BQU0sQ0FBQ0osQ0FBQUEsSUFBS0EsRUFBRVYsUUFBUSxDQUFDLEVBQUUsQ0FBQ0MsS0FBSyxJQUFJbUI7SUFDekU7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsT0FBTztZQUFFQyxVQUFVO1lBQVVDLFFBQVE7WUFBVUMsU0FBUztRQUFPOzswQkFFbEUsOERBQUNKO2dCQUFJQyxPQUFPO29CQUNWSSxZQUFZO29CQUNaQyxPQUFPO29CQUNQRixTQUFTO29CQUNURyxjQUFjO29CQUNkQyxjQUFjO29CQUNkQyxXQUFXO2dCQUNiOztrQ0FDRSw4REFBQ0M7d0JBQUdULE9BQU87NEJBQUVVLFVBQVU7NEJBQVVSLFFBQVE7d0JBQWE7a0NBQUc7Ozs7OztrQ0FHekQsOERBQUNkO3dCQUFFWSxPQUFPOzRCQUFFVSxVQUFVOzRCQUFVQyxTQUFTO3dCQUFJOzs0QkFBRzs0QkFDbEN2QyxZQUFZd0MsTUFBTTs0QkFBQzs7Ozs7Ozs7Ozs7OzswQkFJbkMsOERBQUNiO2dCQUFJQyxPQUFPO29CQUFFYSxTQUFTO29CQUFRQyxxQkFBcUI7b0JBQWFDLEtBQUs7Z0JBQU87O2tDQUUzRSw4REFBQ2hCOzswQ0FDQyw4REFBQzdCLGlFQUFhQTtnQ0FBQzhDLFFBQVEvQjs7Ozs7OzBDQUd2Qiw4REFBQ2M7Z0NBQUlDLE9BQU87b0NBQ1ZpQixXQUFXO29DQUNYZCxTQUFTO29DQUNUZSxpQkFBaUI7b0NBQ2pCWixjQUFjO2dDQUNoQjs7a0RBQ0UsOERBQUNhO3dDQUFHbkIsT0FBTzs0Q0FBRU8sY0FBYzt3Q0FBTztrREFBRzs7Ozs7O2tEQUNyQyw4REFBQ1I7d0NBQUlDLE9BQU87NENBQUVhLFNBQVM7NENBQVFPLGVBQWU7NENBQVVMLEtBQUs7d0NBQVM7OzBEQUNwRSw4REFBQzVDLGlEQUFJQTtnREFDSGtELE1BQUs7Z0RBQ0xyQixPQUFPO29EQUNMc0IsZ0JBQWdCO29EQUNoQmpCLE9BQU8sQ0FBQ3JCLGFBQWFGLFFBQVEsR0FBRyxZQUFZO29EQUM1Q3FCLFNBQVM7b0RBQ1RHLGNBQWM7b0RBQ2RZLGlCQUFpQixDQUFDbEMsYUFBYUYsUUFBUSxHQUFHLFlBQVk7b0RBQ3REeUMsWUFBWSxDQUFDdkMsYUFBYUYsUUFBUSxHQUFHLFNBQVM7Z0RBQ2hEOztvREFDRDtvREFDa0JWLFlBQVl3QyxNQUFNO29EQUFDOzs7Ozs7OzRDQUVyQ3RCLGNBQWNrQyxLQUFLLENBQUMsR0FBRyxHQUFHckMsR0FBRyxDQUFDLENBQUNMO2dEQUM5QixNQUFNMkMsUUFBUXJELFlBQVlvQixNQUFNLENBQUNKLENBQUFBLElBQUtBLEVBQUVOLFFBQVEsS0FBS0EsVUFBVThCLE1BQU07Z0RBQ3JFLHFCQUNFLDhEQUFDekMsaURBQUlBO29EQUVIa0QsTUFBTSxDQUFDLG1CQUFtQixFQUFFdkMsU0FBUyxDQUFDO29EQUN0Q2tCLE9BQU87d0RBQ0xzQixnQkFBZ0I7d0RBQ2hCakIsT0FBT3JCLGFBQWFGLFFBQVEsS0FBS0EsV0FBVyxZQUFZO3dEQUN4RHFCLFNBQVM7d0RBQ1RHLGNBQWM7d0RBQ2RZLGlCQUFpQmxDLGFBQWFGLFFBQVEsS0FBS0EsV0FBVyxZQUFZO3dEQUNsRXlDLFlBQVl2QyxhQUFhRixRQUFRLEtBQUtBLFdBQVcsU0FBUztvREFDNUQ7O3dEQUVDQTt3REFBUzt3REFBRzJDO3dEQUFNOzttREFYZDNDOzs7Ozs0Q0FjWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNTiw4REFBQ2lCOzswQ0FDQyw4REFBQ0E7Z0NBQUlDLE9BQU87b0NBQ1ZhLFNBQVM7b0NBQ1RhLGdCQUFnQjtvQ0FDaEJDLFlBQVk7b0NBQ1pwQixjQUFjO29DQUNkSixTQUFTO29DQUNUZSxpQkFBaUI7b0NBQ2pCWixjQUFjO2dDQUNoQjs7a0RBQ0UsOERBQUNQOzswREFDQyw4REFBQzZCOzBEQUFRckMsaUJBQWlCcUIsTUFBTTs7Ozs7OzRDQUFVOzRDQUFLeEMsWUFBWXdDLE1BQU07NENBQUM7NENBQ2hFNUIsQ0FBQUEsYUFBYVIsS0FBSyxJQUFJUSxhQUFhRixRQUFRLG1CQUMzQyw4REFBQ2lCO2dEQUFJQyxPQUFPO29EQUFFVSxVQUFVO29EQUFVTCxPQUFPO29EQUFRWSxXQUFXO2dEQUFVOztvREFDbkVqQyxhQUFhUixLQUFLLElBQUksQ0FBQyxPQUFPLEVBQUVRLGFBQWFSLEtBQUssQ0FBQyxDQUFDO29EQUNwRFEsYUFBYVIsS0FBSyxJQUFJUSxhQUFhRixRQUFRLElBQUk7b0RBQy9DRSxhQUFhRixRQUFRLElBQUksQ0FBQyxVQUFVLEVBQUVFLGFBQWFGLFFBQVEsQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7O2tEQUlwRSw4REFBQ2lCO3dDQUFJQyxPQUFPOzRDQUFFVSxVQUFVOzRDQUFVTCxPQUFPO3dDQUFPO2tEQUFHOzs7Ozs7Ozs7Ozs7NEJBS3BEZCxpQkFBaUJxQixNQUFNLEdBQUcsa0JBQ3pCLDhEQUFDYjtnQ0FBSUMsT0FBTztvQ0FDVmEsU0FBUztvQ0FDVEMscUJBQXFCO29DQUNyQkMsS0FBSztnQ0FDUDswQ0FDR3hCLGlCQUFpQkosR0FBRyxDQUFDQyxDQUFBQSxrQkFDcEIsOERBQUNuQiwrREFBV0E7d0NBQVk0RCxTQUFTekM7dUNBQWZBLEVBQUVmLEVBQUU7Ozs7Ozs7OztxREFJMUIsOERBQUMwQjtnQ0FBSUMsT0FBTztvQ0FDVlEsV0FBVztvQ0FDWEwsU0FBUztvQ0FDVGUsaUJBQWlCO29DQUNqQlosY0FBYztnQ0FDaEI7O2tEQUNFLDhEQUFDYTtrREFBRzs7Ozs7O2tEQUNKLDhEQUFDL0I7d0NBQUVZLE9BQU87NENBQUVLLE9BQU87NENBQVFFLGNBQWM7d0NBQU87a0RBQUc7Ozs7OztrREFHbkQsOERBQUNwQyxpREFBSUE7d0NBQ0hrRCxNQUFLO3dDQUNMckIsT0FBTzs0Q0FDTEssT0FBTzs0Q0FDUGlCLGdCQUFnQjs0Q0FDaEJDLFlBQVk7d0NBQ2Q7a0RBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNmIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvcHJvZHVjdHMvcGFnZS50c3g/NGU3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUHJvZHVjdENhcmQgZnJvbSAnQC9jb21wb25lbnRzL1Byb2R1Y3RDYXJkJztcbmltcG9ydCBGaWx0ZXJTaWRlYmFyIGZyb20gJ0AvY29tcG9uZW50cy9GaWx0ZXJTaWRlYmFyJztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5cbi8vIENvbXBsZXRlIHByb2R1Y3QgY2F0YWxvZyAtIGFsbCA3MCsgcHJvZHVjdHNcbmNvbnN0IGFsbFByb2R1Y3RzID0gW1xuICAvLyBTTUFSVFBIT05FU1xuICB7IGlkOiAnMScsIHRpdGxlOiAnaVBob25lIDE1IFBybyBNYXgnLCBzbHVnOiAnaXBob25lLTE1LXByby1tYXgnLCBicmFuZDogeyBuYW1lOiAnQXBwbGUnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTE5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTkyNzUwNDc1MzM4LTc0YjdiMjEwODVhYj93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnc21hcnRwaG9uZXMnIH0sXG4gIHsgaWQ6ICcyJywgdGl0bGU6ICdpUGhvbmUgMTUgUHJvJywgc2x1ZzogJ2lwaG9uZS0xNS1wcm8nLCBicmFuZDogeyBuYW1lOiAnQXBwbGUnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogOTk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1OTI3NTA0NzUzMzgtNzRiN2IyMTA4NWFiP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdzbWFydHBob25lcycgfSxcbiAgeyBpZDogJzMnLCB0aXRsZTogJ1NhbXN1bmcgR2FsYXh5IFMyNCBVbHRyYScsIHNsdWc6ICdnYWxheHktczI0LXVsdHJhJywgYnJhbmQ6IHsgbmFtZTogJ1NhbXN1bmcnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTI5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTExNzA3MTcxNjM0LTVmODk3ZmYwMmFhOT93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnc21hcnRwaG9uZXMnIH0sXG4gIHsgaWQ6ICc0JywgdGl0bGU6ICdTYW1zdW5nIEdhbGF4eSBTMjQnLCBzbHVnOiAnZ2FsYXh5LXMyNCcsIGJyYW5kOiB7IG5hbWU6ICdTYW1zdW5nJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDg5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTExNzA3MTcxNjM0LTVmODk3ZmYwMmFhOT93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnc21hcnRwaG9uZXMnIH0sXG4gIHsgaWQ6ICc1JywgdGl0bGU6ICdHb29nbGUgUGl4ZWwgOCBQcm8nLCBzbHVnOiAnZ29vZ2xlLXBpeGVsLTgtcHJvJywgYnJhbmQ6IHsgbmFtZTogJ0dvb2dsZScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA5OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUxMTcwNzE3MTYzNC01Zjg5N2ZmMDJhYTk/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3NtYXJ0cGhvbmVzJyB9LFxuICB7IGlkOiAnNicsIHRpdGxlOiAnT25lUGx1cyAxMicsIHNsdWc6ICdvbmVwbHVzLTEyJywgYnJhbmQ6IHsgbmFtZTogJ09uZVBsdXMnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogNzk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MTE3MDcxNzE2MzQtNWY4OTdmZjAyYWE5P3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdzbWFydHBob25lcycgfSxcbiAgeyBpZDogJzcnLCB0aXRsZTogJ1hpYW9taSAxNCBVbHRyYScsIHNsdWc6ICd4aWFvbWktMTQtdWx0cmEnLCBicmFuZDogeyBuYW1lOiAnWGlhb21pJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDY5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTExNzA3MTcxNjM0LTVmODk3ZmYwMmFhOT93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnc21hcnRwaG9uZXMnIH0sXG5cbiAgLy8gTEFQVE9QU1xuICB7IGlkOiAnOCcsIHRpdGxlOiAnTWFjQm9vayBQcm8gMTZcIiBNMyBNYXgnLCBzbHVnOiAnbWFjYm9vay1wcm8tMTYtbTMtbWF4JywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDM0OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU0MTgwNzA4NC01YzUyYjZiM2FkZWY/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2xhcHRvcHMnIH0sXG4gIHsgaWQ6ICc5JywgdGl0bGU6ICdNYWNCb29rIEFpciBNMycsIHNsdWc6ICdtYWNib29rLWFpci1tMycsIGJyYW5kOiB7IG5hbWU6ICdBcHBsZScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAxMjk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NDE4MDcwODQtNWM1MmI2YjNhZGVmP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdsYXB0b3BzJyB9LFxuICB7IGlkOiAnMTAnLCB0aXRsZTogJ0RlbGwgWFBTIDEzIFBsdXMnLCBzbHVnOiAnZGVsbC14cHMtMTMtcGx1cycsIGJyYW5kOiB7IG5hbWU6ICdEZWxsJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDEzOTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ5NjE4MTEzMzIwNi04MGNlOWI4OGE4NTM/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2xhcHRvcHMnIH0sXG4gIHsgaWQ6ICcxMScsIHRpdGxlOiAnVGhpbmtQYWQgWDEgQ2FyYm9uJywgc2x1ZzogJ3RoaW5rcGFkLXgxLWNhcmJvbicsIGJyYW5kOiB7IG5hbWU6ICdMZW5vdm8nIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTU5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNDk2MTgxMTMzMjA2LTgwY2U5Yjg4YTg1Mz93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnbGFwdG9wcycgfSxcbiAgeyBpZDogJzEyJywgdGl0bGU6ICdTdXJmYWNlIExhcHRvcCA1Jywgc2x1ZzogJ3N1cmZhY2UtbGFwdG9wLTUnLCBicmFuZDogeyBuYW1lOiAnTWljcm9zb2Z0JyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDEyOTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ5NjE4MTEzMzIwNi04MGNlOWI4OGE4NTM/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2xhcHRvcHMnIH0sXG4gIHsgaWQ6ICcxMycsIHRpdGxlOiAnQVNVUyBST0cgWmVwaHlydXMgRzE0Jywgc2x1ZzogJ2FzdXMtcm9nLXplcGh5cnVzLWcxNCcsIGJyYW5kOiB7IG5hbWU6ICdBU1VTJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDE4OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ5NjE4MTEzMzIwNi04MGNlOWI4OGE4NTM/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2xhcHRvcHMnIH0sXG4gIHsgaWQ6ICcxNCcsIHRpdGxlOiAnSFAgU3BlY3RyZSB4MzYwJywgc2x1ZzogJ2hwLXNwZWN0cmUteDM2MCcsIGJyYW5kOiB7IG5hbWU6ICdIUCcgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAxMTk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE0OTYxODExMzMyMDYtODBjZTliODhhODUzP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdsYXB0b3BzJyB9LFxuXG4gIC8vIEFVRElPXG4gIHsgaWQ6ICcxNScsIHRpdGxlOiAnQWlyUG9kcyBQcm8gMicsIHNsdWc6ICdhaXJwb2RzLXByby0yJywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDI0OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNjA2MjIwOTQ1NzcwLWI1YjZjMmM1NWJmMT93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnYXVkaW8nIH0sXG4gIHsgaWQ6ICcxNicsIHRpdGxlOiAnU29ueSBXSC0xMDAwWE01Jywgc2x1ZzogJ3Nvbnktd2gtMTAwMHhtNScsIGJyYW5kOiB7IG5hbWU6ICdTb255JyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDM5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTA1NzQwNDIwOTI4LTVlNTYwYzA2ZDMwZT93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnYXVkaW8nIH0sXG4gIHsgaWQ6ICcxNycsIHRpdGxlOiAnQm9zZSBRdWlldENvbWZvcnQgVWx0cmEnLCBzbHVnOiAnYm9zZS1xdWlldGNvbWZvcnQtdWx0cmEnLCBicmFuZDogeyBuYW1lOiAnQm9zZScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA0MjkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNTc0MDQyMDkyOC01ZTU2MGMwNmQzMGU/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2F1ZGlvJyB9LFxuICB7IGlkOiAnMTgnLCB0aXRsZTogJ1Nlbm5oZWlzZXIgSEQgODAwUycsIHNsdWc6ICdzZW5uaGVpc2VyLWhkLTgwMHMnLCBicmFuZDogeyBuYW1lOiAnU2VubmhlaXNlcicgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAxNjk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MDU3NDA0MjA5MjgtNWU1NjBjMDZkMzBlP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdhdWRpbycgfSxcbiAgeyBpZDogJzE5JywgdGl0bGU6ICdBdWRpby1UZWNobmljYSBBVEgtTTUweCcsIHNsdWc6ICdhdWRpby10ZWNobmljYS1hdGgtbTUweCcsIGJyYW5kOiB7IG5hbWU6ICdBdWRpby1UZWNobmljYScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAxNDkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNTc0MDQyMDkyOC01ZTU2MGMwNmQzMGU/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2F1ZGlvJyB9LFxuICB7IGlkOiAnMjAnLCB0aXRsZTogJ0JlYXRzIFN0dWRpbyBQcm8nLCBzbHVnOiAnYmVhdHMtc3R1ZGlvLXBybycsIGJyYW5kOiB7IG5hbWU6ICdCZWF0cycgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAzNDkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwNTc0MDQyMDkyOC01ZTU2MGMwNmQzMGU/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2F1ZGlvJyB9LFxuXG4gIC8vIEdBTUlOR1xuICB7IGlkOiAnMjEnLCB0aXRsZTogJ1BsYXlTdGF0aW9uIDUnLCBzbHVnOiAncGxheXN0YXRpb24tNScsIGJyYW5kOiB7IG5hbWU6ICdTb255JyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDQ5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNjA2MTQ0MDQyNjE0LWIyNDE3ZTk5YzRlMz93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnZ2FtaW5nJyB9LFxuICB7IGlkOiAnMjInLCB0aXRsZTogJ1hib3ggU2VyaWVzIFgnLCBzbHVnOiAneGJveC1zZXJpZXMteCcsIGJyYW5kOiB7IG5hbWU6ICdNaWNyb3NvZnQnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogNDk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MDYxNDQwNDI2MTQtYjI0MTdlOTljNGUzP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdnYW1pbmcnIH0sXG4gIHsgaWQ6ICcyMycsIHRpdGxlOiAnTmludGVuZG8gU3dpdGNoIE9MRUQnLCBzbHVnOiAnbmludGVuZG8tc3dpdGNoLW9sZWQnLCBicmFuZDogeyBuYW1lOiAnTmludGVuZG8nIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMzQ5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MDYxNDQwNDI2MTQtYjI0MTdlOTljNGUzP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdnYW1pbmcnIH0sXG4gIHsgaWQ6ICcyNCcsIHRpdGxlOiAnU3RlYW0gRGVjayBPTEVEJywgc2x1ZzogJ3N0ZWFtLWRlY2stb2xlZCcsIGJyYW5kOiB7IG5hbWU6ICdWYWx2ZScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA1NDkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTYwNjE0NDA0MjYxNC1iMjQxN2U5OWM0ZTM/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2dhbWluZycgfSxcblxuICAvLyBUQUJMRVRTXG4gIHsgaWQ6ICcyNScsIHRpdGxlOiAnaVBhZCBQcm8gMTIuOVwiIE0yJywgc2x1ZzogJ2lwYWQtcHJvLTEyLTktbTInLCBicmFuZDogeyBuYW1lOiAnQXBwbGUnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTA5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTQ0MjQ0MDE1LTBkZjRiM2ZmYzZiMD93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAndGFibGV0cycgfSxcbiAgeyBpZDogJzI2JywgdGl0bGU6ICdpUGFkIEFpciA1Jywgc2x1ZzogJ2lwYWQtYWlyLTUnLCBicmFuZDogeyBuYW1lOiAnQXBwbGUnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogNTk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NDQyNDQwMTUtMGRmNGIzZmZjNmIwP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICd0YWJsZXRzJyB9LFxuICB7IGlkOiAnMjcnLCB0aXRsZTogJ1NhbXN1bmcgR2FsYXh5IFRhYiBTOSBVbHRyYScsIHNsdWc6ICdnYWxheHktdGFiLXM5LXVsdHJhJywgYnJhbmQ6IHsgbmFtZTogJ1NhbXN1bmcnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTE5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTQ0MjQ0MDE1LTBkZjRiM2ZmYzZiMD93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAndGFibGV0cycgfSxcbiAgeyBpZDogJzI4JywgdGl0bGU6ICdNaWNyb3NvZnQgU3VyZmFjZSBQcm8gOScsIHNsdWc6ICdzdXJmYWNlLXByby05JywgYnJhbmQ6IHsgbmFtZTogJ01pY3Jvc29mdCcgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA5OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU0NDI0NDAxNS0wZGY0YjNmZmM2YjA/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3RhYmxldHMnIH0sXG5cbiAgLy8gV0VBUkFCTEVTXG4gIHsgaWQ6ICczMCcsIHRpdGxlOiAnQXBwbGUgV2F0Y2ggVWx0cmEgMicsIHNsdWc6ICdhcHBsZS13YXRjaC11bHRyYS0yJywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDc5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTUxODE2MjMwLWVmNWRlYWVkNGEyNj93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnd2VhcmFibGVzJyB9LFxuICB7IGlkOiAnMzEnLCB0aXRsZTogJ0FwcGxlIFdhdGNoIFNlcmllcyA5Jywgc2x1ZzogJ2FwcGxlLXdhdGNoLXNlcmllcy05JywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDM5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTUxODE2MjMwLWVmNWRlYWVkNGEyNj93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnd2VhcmFibGVzJyB9LFxuICB7IGlkOiAnMzInLCB0aXRsZTogJ1NhbXN1bmcgR2FsYXh5IFdhdGNoIDYnLCBzbHVnOiAnZ2FsYXh5LXdhdGNoLTYnLCBicmFuZDogeyBuYW1lOiAnU2Ftc3VuZycgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAzMjkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU1MTgxNjIzMC1lZjVkZWFlZDRhMjY/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3dlYXJhYmxlcycgfSxcblxuICAvLyBDQU1FUkFTXG4gIHsgaWQ6ICc0MCcsIHRpdGxlOiAnQ2Fub24gRU9TIFI1Jywgc2x1ZzogJ2Nhbm9uLWVvcy1yNScsIGJyYW5kOiB7IG5hbWU6ICdDYW5vbicgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAzODk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1MDI5MjA5MTcxMjgtMWFhNTAwNzY0Y2JkP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdjYW1lcmFzJyB9LFxuICB7IGlkOiAnNDEnLCB0aXRsZTogJ1NvbnkgQTdSIFYnLCBzbHVnOiAnc29ueS1hN3ItdicsIGJyYW5kOiB7IG5hbWU6ICdTb255JyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDM4OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUwMjkyMDkxNzEyOC0xYWE1MDA3NjRjYmQ/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ2NhbWVyYXMnIH0sXG4gIHsgaWQ6ICc0MicsIHRpdGxlOiAnTmlrb24gWjknLCBzbHVnOiAnbmlrb24tejknLCBicmFuZDogeyBuYW1lOiAnTmlrb24nIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogNTQ5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTAyOTIwOTE3MTI4LTFhYTUwMDc2NGNiZD93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnY2FtZXJhcycgfSxcblxuICAvLyBUVlNcbiAgeyBpZDogJzQ1JywgdGl0bGU6ICdTYW1zdW5nIDY1XCIgTmVvIFFMRUQgOEsnLCBzbHVnOiAnc2Ftc3VuZy02NS1uZW8tcWxlZC04aycsIGJyYW5kOiB7IG5hbWU6ICdTYW1zdW5nJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDI5OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU5MzM1OTY3Nzg3OS1hNGJiOTJmODI5ZDE/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3R2cycgfSxcbiAgeyBpZDogJzQ2JywgdGl0bGU6ICdMRyA3N1wiIE9MRUQgQzMnLCBzbHVnOiAnbGctNzctb2xlZC1jMycsIGJyYW5kOiB7IG5hbWU6ICdMRycgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAzNDk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1OTMzNTk2Nzc4NzktYTRiYjkyZjgyOWQxP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICd0dnMnIH0sXG5cbiAgLy8gTU9OSVRPUlNcbiAgeyBpZDogJzUzJywgdGl0bGU6ICdBcHBsZSBTdHVkaW8gRGlzcGxheScsIHNsdWc6ICdhcHBsZS1zdHVkaW8tZGlzcGxheScsIGJyYW5kOiB7IG5hbWU6ICdBcHBsZScgfSwgdmFyaWFudHM6IFt7IHByaWNlOiAxNTk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1Mjc4NjQ1NTA0MTctN2ZkOTFmYzUxYTQ2P3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdtb25pdG9ycycgfSxcbiAgeyBpZDogJzU0JywgdGl0bGU6ICdEZWxsIFVsdHJhU2hhcnAgMzJcIiA0SycsIHNsdWc6ICdkZWxsLXVsdHJhc2hhcnAtMzItNGsnLCBicmFuZDogeyBuYW1lOiAnRGVsbCcgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA4OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTUyNzg2NDU1MDQxNy03ZmQ5MWZjNTFhNDY/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ21vbml0b3JzJyB9LFxuXG4gIC8vIFZSXG4gIHsgaWQ6ICc2NCcsIHRpdGxlOiAnTWV0YSBRdWVzdCAzJywgc2x1ZzogJ21ldGEtcXVlc3QtMycsIGJyYW5kOiB7IG5hbWU6ICdNZXRhJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDQ5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTkzNTA4NTEyMjU1LTg2YWI0MmE4ZTYyMD93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAndnInIH0sXG4gIHsgaWQ6ICc2NScsIHRpdGxlOiAnQXBwbGUgVmlzaW9uIFBybycsIHNsdWc6ICdhcHBsZS12aXNpb24tcHJvJywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDM0OTkuOTkgfV0sIG1lZGlhOiBbeyB1cmw6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU5MzUwODUxMjI1NS04NmFiNDJhOGU2MjA/dz00MDAmaD0zMDAmZml0PWNyb3AnIH1dLCBjYXRlZ29yeTogJ3ZyJyB9LFxuXG4gIC8vIEFDQ0VTU09SSUVTXG4gIHsgaWQ6ICc1NycsIHRpdGxlOiAnTG9naXRlY2ggTVggTWFzdGVyIDNTJywgc2x1ZzogJ2xvZ2l0ZWNoLW14LW1hc3Rlci0zcycsIGJyYW5kOiB7IG5hbWU6ICdMb2dpdGVjaCcgfSwgdmFyaWFudHM6IFt7IHByaWNlOiA5OS45OSB9XSwgbWVkaWE6IFt7IHVybDogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTQxMTQwNTMyMTU0LWIwMjRkNzA1YjkwYT93PTQwMCZoPTMwMCZmaXQ9Y3JvcCcgfV0sIGNhdGVnb3J5OiAnYWNjZXNzb3JpZXMnIH0sXG4gIHsgaWQ6ICc1OCcsIHRpdGxlOiAnQXBwbGUgTWFnaWMgS2V5Ym9hcmQnLCBzbHVnOiAnYXBwbGUtbWFnaWMta2V5Ym9hcmQnLCBicmFuZDogeyBuYW1lOiAnQXBwbGUnIH0sIHZhcmlhbnRzOiBbeyBwcmljZTogMTk5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NDExNDA1MzIxNTQtYjAyNGQ3MDViOTBhP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdhY2Nlc3NvcmllcycgfSxcbiAgeyBpZDogJzcwJywgdGl0bGU6ICdBcHBsZSBNYWdTYWZlIENoYXJnZXInLCBzbHVnOiAnYXBwbGUtbWFnc2FmZS1jaGFyZ2VyJywgYnJhbmQ6IHsgbmFtZTogJ0FwcGxlJyB9LCB2YXJpYW50czogW3sgcHJpY2U6IDM5Ljk5IH1dLCBtZWRpYTogW3sgdXJsOiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MDk1OTI4MDY1OTYtNGQxYjVlNWU1ZTVlP3c9NDAwJmg9MzAwJmZpdD1jcm9wJyB9XSwgY2F0ZWdvcnk6ICdhY2Nlc3NvcmllcycgfSxcbl07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFsbFByb2R1Y3RzUGFnZSh7IHNlYXJjaFBhcmFtcyB9OiBhbnkpIHtcbiAgLy8gR2V0IHVuaXF1ZSBicmFuZHMgYW5kIGNhdGVnb3JpZXNcbiAgY29uc3QgYWxsQnJhbmRzID0gWy4uLm5ldyBTZXQoYWxsUHJvZHVjdHMubWFwKHAgPT4gcC5icmFuZC5uYW1lKSldLnNvcnQoKTtcbiAgY29uc3QgYWxsQ2F0ZWdvcmllcyA9IFsuLi5uZXcgU2V0KGFsbFByb2R1Y3RzLm1hcChwID0+IHAuY2F0ZWdvcnkpKV07XG4gIFxuICAvLyBBcHBseSBmaWx0ZXJzXG4gIGxldCBmaWx0ZXJlZFByb2R1Y3RzID0gYWxsUHJvZHVjdHM7XG4gIFxuICBpZiAoc2VhcmNoUGFyYW1zLmJyYW5kKSB7XG4gICAgZmlsdGVyZWRQcm9kdWN0cyA9IGZpbHRlcmVkUHJvZHVjdHMuZmlsdGVyKHAgPT4gXG4gICAgICBwLmJyYW5kLm5hbWUudG9Mb3dlckNhc2UoKSA9PT0gc2VhcmNoUGFyYW1zLmJyYW5kLnRvTG93ZXJDYXNlKClcbiAgICApO1xuICB9XG4gIFxuICBpZiAoc2VhcmNoUGFyYW1zLmNhdGVnb3J5KSB7XG4gICAgZmlsdGVyZWRQcm9kdWN0cyA9IGZpbHRlcmVkUHJvZHVjdHMuZmlsdGVyKHAgPT4gXG4gICAgICBwLmNhdGVnb3J5ID09PSBzZWFyY2hQYXJhbXMuY2F0ZWdvcnlcbiAgICApO1xuICB9XG4gIFxuICBpZiAoc2VhcmNoUGFyYW1zLm1pbikge1xuICAgIGNvbnN0IG1pblByaWNlID0gcGFyc2VGbG9hdChzZWFyY2hQYXJhbXMubWluKTtcbiAgICBmaWx0ZXJlZFByb2R1Y3RzID0gZmlsdGVyZWRQcm9kdWN0cy5maWx0ZXIocCA9PiBwLnZhcmlhbnRzWzBdLnByaWNlID49IG1pblByaWNlKTtcbiAgfVxuICBcbiAgaWYgKHNlYXJjaFBhcmFtcy5tYXgpIHtcbiAgICBjb25zdCBtYXhQcmljZSA9IHBhcnNlRmxvYXQoc2VhcmNoUGFyYW1zLm1heCk7XG4gICAgZmlsdGVyZWRQcm9kdWN0cyA9IGZpbHRlcmVkUHJvZHVjdHMuZmlsdGVyKHAgPT4gcC52YXJpYW50c1swXS5wcmljZSA8PSBtYXhQcmljZSk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3sgbWF4V2lkdGg6ICcxNDAwcHgnLCBtYXJnaW46ICcwIGF1dG8nLCBwYWRkaW5nOiAnMXJlbScgfX0+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBzdHlsZT17eyBcbiAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSknLFxuICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgcGFkZGluZzogJzJyZW0nLFxuICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgbWFyZ2luQm90dG9tOiAnMnJlbScsXG4gICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcidcbiAgICAgIH19PlxuICAgICAgICA8aDEgc3R5bGU9e3sgZm9udFNpemU6ICcyLjVyZW0nLCBtYXJnaW46ICcwIDAgMXJlbSAwJyB9fT5cbiAgICAgICAgICDwn5uN77iPIENvbXBsZXRlIFByb2R1Y3QgQ2F0YWxvZ1xuICAgICAgICA8L2gxPlxuICAgICAgICA8cCBzdHlsZT17eyBmb250U2l6ZTogJzEuMnJlbScsIG9wYWNpdHk6IDAuOSB9fT5cbiAgICAgICAgICBCcm93c2UgYWxsIHthbGxQcm9kdWN0cy5sZW5ndGh9IHByZW1pdW0gZWxlY3Ryb25pY3MgZnJvbSB0b3AgYnJhbmRzXG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogJzI4MHB4IDFmcicsIGdhcDogJzJyZW0nIH19PlxuICAgICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8RmlsdGVyU2lkZWJhciBicmFuZHM9e2FsbEJyYW5kc30gLz5cbiAgICAgICAgICBcbiAgICAgICAgICB7LyogQ2F0ZWdvcnkgRmlsdGVyICovfVxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgXG4gICAgICAgICAgICBtYXJnaW5Ub3A6ICcycmVtJyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICcxLjVyZW0nLFxuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2Y4ZjlmYScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4J1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPGgzIHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PkNhdGVnb3JpZXM8L2gzPlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLCBnYXA6ICcwLjVyZW0nIH19PlxuICAgICAgICAgICAgICA8TGluayBcbiAgICAgICAgICAgICAgICBocmVmPVwiL3Byb2R1Y3RzXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgdGV4dERlY29yYXRpb246ICdub25lJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAhc2VhcmNoUGFyYW1zLmNhdGVnb3J5ID8gJyMwMDdiZmYnIDogJyM2NjYnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNXJlbScsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAhc2VhcmNoUGFyYW1zLmNhdGVnb3J5ID8gJyNlM2YyZmQnIDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICFzZWFyY2hQYXJhbXMuY2F0ZWdvcnkgPyAnYm9sZCcgOiAnbm9ybWFsJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBBbGwgQ2F0ZWdvcmllcyAoe2FsbFByb2R1Y3RzLmxlbmd0aH0pXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAge2FsbENhdGVnb3JpZXMuc2xpY2UoMCwgOCkubWFwKChjYXRlZ29yeSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGNvdW50ID0gYWxsUHJvZHVjdHMuZmlsdGVyKHAgPT4gcC5jYXRlZ29yeSA9PT0gY2F0ZWdvcnkpLmxlbmd0aDtcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgPExpbmsgXG4gICAgICAgICAgICAgICAgICAgIGtleT17Y2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2AvcHJvZHVjdHM/Y2F0ZWdvcnk9JHtjYXRlZ29yeX1gfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIHRleHREZWNvcmF0aW9uOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgY29sb3I6IHNlYXJjaFBhcmFtcy5jYXRlZ29yeSA9PT0gY2F0ZWdvcnkgPyAnIzAwN2JmZicgOiAnIzY2NicsXG4gICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IHNlYXJjaFBhcmFtcy5jYXRlZ29yeSA9PT0gY2F0ZWdvcnkgPyAnI2UzZjJmZCcgOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IHNlYXJjaFBhcmFtcy5jYXRlZ29yeSA9PT0gY2F0ZWdvcnkgPyAnYm9sZCcgOiAnbm9ybWFsJ1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnl9ICh7Y291bnR9KVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcm9kdWN0cyBHcmlkICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgXG4gICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsIFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgXG4gICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzEuNXJlbScsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMXJlbScsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjhmOWZhJyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxzdHJvbmc+e2ZpbHRlcmVkUHJvZHVjdHMubGVuZ3RofTwvc3Ryb25nPiBvZiB7YWxsUHJvZHVjdHMubGVuZ3RofSBwcm9kdWN0c1xuICAgICAgICAgICAgICB7KHNlYXJjaFBhcmFtcy5icmFuZCB8fCBzZWFyY2hQYXJhbXMuY2F0ZWdvcnkpICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMC45cmVtJywgY29sb3I6ICcjNjY2JywgbWFyZ2luVG9wOiAnMC4yNXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICB7c2VhcmNoUGFyYW1zLmJyYW5kICYmIGBCcmFuZDogJHtzZWFyY2hQYXJhbXMuYnJhbmR9YH1cbiAgICAgICAgICAgICAgICAgIHtzZWFyY2hQYXJhbXMuYnJhbmQgJiYgc2VhcmNoUGFyYW1zLmNhdGVnb3J5ICYmICcg4oCiICd9XG4gICAgICAgICAgICAgICAgICB7c2VhcmNoUGFyYW1zLmNhdGVnb3J5ICYmIGBDYXRlZ29yeTogJHtzZWFyY2hQYXJhbXMuY2F0ZWdvcnl9YH1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzAuOXJlbScsIGNvbG9yOiAnIzY2NicgfX0+XG4gICAgICAgICAgICAgIFNvcnRlZCBieTogRmVhdHVyZWRcbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHtmaWx0ZXJlZFByb2R1Y3RzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IFxuICAgICAgICAgICAgICBkaXNwbGF5OiAnZ3JpZCcsIFxuICAgICAgICAgICAgICBncmlkVGVtcGxhdGVDb2x1bW5zOiAncmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjgwcHgsIDFmcikpJywgXG4gICAgICAgICAgICAgIGdhcDogJzEuNXJlbScgXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAge2ZpbHRlcmVkUHJvZHVjdHMubWFwKHAgPT4gKFxuICAgICAgICAgICAgICAgIDxQcm9kdWN0Q2FyZCBrZXk9e3AuaWR9IHByb2R1Y3Q9e3B9IC8+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgXG4gICAgICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsIFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnM3JlbScsXG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmOGY5ZmEnLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4J1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxoMz5ObyBwcm9kdWN0cyBmb3VuZDwvaDM+XG4gICAgICAgICAgICAgIDxwIHN0eWxlPXt7IGNvbG9yOiAnIzY2NicsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgIE5vIHByb2R1Y3RzIG1hdGNoIHlvdXIgY3VycmVudCBmaWx0ZXJzLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxMaW5rIFxuICAgICAgICAgICAgICAgIGhyZWY9XCIvcHJvZHVjdHNcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBjb2xvcjogJyMwMDdiZmYnLFxuICAgICAgICAgICAgICAgICAgdGV4dERlY29yYXRpb246ICdub25lJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDbGVhciBhbGwgZmlsdGVyc1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlByb2R1Y3RDYXJkIiwiRmlsdGVyU2lkZWJhciIsIkxpbmsiLCJhbGxQcm9kdWN0cyIsImlkIiwidGl0bGUiLCJzbHVnIiwiYnJhbmQiLCJuYW1lIiwidmFyaWFudHMiLCJwcmljZSIsIm1lZGlhIiwidXJsIiwiY2F0ZWdvcnkiLCJBbGxQcm9kdWN0c1BhZ2UiLCJzZWFyY2hQYXJhbXMiLCJhbGxCcmFuZHMiLCJTZXQiLCJtYXAiLCJwIiwic29ydCIsImFsbENhdGVnb3JpZXMiLCJmaWx0ZXJlZFByb2R1Y3RzIiwiZmlsdGVyIiwidG9Mb3dlckNhc2UiLCJtaW4iLCJtaW5QcmljZSIsInBhcnNlRmxvYXQiLCJtYXgiLCJtYXhQcmljZSIsImRpdiIsInN0eWxlIiwibWF4V2lkdGgiLCJtYXJnaW4iLCJwYWRkaW5nIiwiYmFja2dyb3VuZCIsImNvbG9yIiwiYm9yZGVyUmFkaXVzIiwibWFyZ2luQm90dG9tIiwidGV4dEFsaWduIiwiaDEiLCJmb250U2l6ZSIsIm9wYWNpdHkiLCJsZW5ndGgiLCJkaXNwbGF5IiwiZ3JpZFRlbXBsYXRlQ29sdW1ucyIsImdhcCIsImJyYW5kcyIsIm1hcmdpblRvcCIsImJhY2tncm91bmRDb2xvciIsImgzIiwiZmxleERpcmVjdGlvbiIsImhyZWYiLCJ0ZXh0RGVjb3JhdGlvbiIsImZvbnRXZWlnaHQiLCJzbGljZSIsImNvdW50IiwianVzdGlmeUNvbnRlbnQiLCJhbGlnbkl0ZW1zIiwic3Ryb25nIiwicHJvZHVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/products/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/FilterSidebar.tsx":
/*!******************************************!*\
  !*** ./src/components/FilterSidebar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/FilterSidebar.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/currency */ \"(rsc)/./src/lib/currency.ts\");\n\n\n\nfunction ProductCard({ product }) {\n    const img = product.media?.[0]?.url || \"https://via.placeholder.com/400x300?text=ElectroHub\";\n    const price = Number(product.variants?.[0]?.price ?? 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n        style: {\n            border: \"1px solid #ddd\",\n            borderRadius: 8,\n            overflow: \"hidden\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: `/product/${product.slug}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: img,\n                    alt: product.title,\n                    width: 400,\n                    height: 300,\n                    style: {\n                        width: \"100%\",\n                        height: \"auto\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"0.75rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: 0\n                            },\n                            children: product.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: 0\n                            },\n                            children: product.brand?.name\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_2__.formatMoney)(price)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/ProductCard.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ProductCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx#SkipLink`);


/***/ }),

/***/ "(rsc)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatMoney: () => (/* binding */ formatMoney)\n/* harmony export */ });\nfunction formatMoney(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2N1cnJlbmN5LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxTQUFTQSxZQUFZQyxNQUFjLEVBQUVDLFdBQVcsS0FBSyxFQUFFQyxTQUFTLE9BQU87SUFDNUUsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUNGLFFBQVE7UUFBRUcsT0FBTztRQUFZSjtJQUFTLEdBQUdLLE1BQU0sQ0FBQ047QUFDL0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2xpYi9jdXJyZW5jeS50cz81OWM3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBmb3JtYXRNb25leShhbW91bnQ6IG51bWJlciwgY3VycmVuY3kgPSAnVVNEJywgbG9jYWxlID0gJ2VuLVVTJykge1xuICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KGxvY2FsZSwgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3kgfSkuZm9ybWF0KGFtb3VudCk7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0TW9uZXkiLCJhbW91bnQiLCJjdXJyZW5jeSIsImxvY2FsZSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImZvcm1hdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/currency.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproducts%2Fpage&page=%2Fproducts%2Fpage&appPaths=%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();