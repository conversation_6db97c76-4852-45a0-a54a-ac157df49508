/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/product/[slug]/page";
exports.ids = ["app/product/[slug]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproduct%2F%5Bslug%5D%2Fpage&page=%2Fproduct%2F%5Bslug%5D%2Fpage&appPaths=%2Fproduct%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fproduct%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproduct%2F%5Bslug%5D%2Fpage&page=%2Fproduct%2F%5Bslug%5D%2Fpage&appPaths=%2Fproduct%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fproduct%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'product',\n        {\n        children: [\n        '[slug]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/product/[slug]/page.tsx */ \"(rsc)/./src/app/product/[slug]/page.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/product/[slug]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/product/[slug]/page\",\n        pathname: \"/product/[slug]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproduct%2F%5Bslug%5D%2Fpage&page=%2Fproduct%2F%5Bslug%5D%2Fpage&appPaths=%2Fproduct%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fproduct%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SkipLink.tsx */ \"(ssr)/./src/components/SkipLink.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRlNraXBMaW5rLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraXBMaW5rJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEc7QUFDMUc7QUFDQSxzS0FBOEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLz9mNWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lwTGlua1wiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FAddToCartButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FPriceComparison.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FAddToCartButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FPriceComparison.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AddToCartButton.tsx */ \"(ssr)/./src/components/AddToCartButton.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PriceComparison.tsx */ \"(ssr)/./src/components/PriceComparison.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRkFkZFRvQ2FydEJ1dHRvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRnNyYyUyRmNvbXBvbmVudHMlMkZQcmljZUNvbXBhcmlzb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQTBHO0FBQzFHO0FBQ0Esb0xBQW9JO0FBQ3BJO0FBQ0Esb0xBQW9JIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8/YjNhMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvQWRkVG9DYXJ0QnV0dG9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvUHJpY2VDb21wYXJpc29uLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FAddToCartButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FPriceComparison.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/AddToCartButton.tsx":
/*!********************************************!*\
  !*** ./src/components/AddToCartButton.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddToCartButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction AddToCartButton({ variantId }) {\n    async function add() {\n        const res = await fetch(\"/api/cart\", {\n            method: \"POST\",\n            headers: {\n                \"content-type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                variantId,\n                quantity: 1\n            })\n        });\n        if (!res.ok) alert(\"Failed to add to cart\");\n        else alert(\"Added to cart\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: add,\n        \"aria-label\": \"Add to cart\",\n        children: \"Add to cart\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/AddToCartButton.tsx\",\n        lineNumber: 9,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BZGRUb0NhcnRCdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFZSxTQUFTQSxnQkFBZ0IsRUFBRUMsU0FBUyxFQUF5QjtJQUMxRSxlQUFlQztRQUNiLE1BQU1DLE1BQU0sTUFBTUMsTUFBTSxhQUFhO1lBQUVDLFFBQVE7WUFBUUMsU0FBUztnQkFBRSxnQkFBZ0I7WUFBbUI7WUFBR0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFUjtnQkFBV1MsVUFBVTtZQUFFO1FBQUc7UUFDekosSUFBSSxDQUFDUCxJQUFJUSxFQUFFLEVBQUVDLE1BQU07YUFDZEEsTUFBTTtJQUNiO0lBQ0EscUJBQU8sOERBQUNDO1FBQU9DLFNBQVNaO1FBQUthLGNBQVc7a0JBQWM7Ozs7OztBQUN4RCIsInNvdXJjZXMiOlsid2VicGFjazovL2VsZWN0cm9odWItbWFya2V0cGxhY2UvLi9zcmMvY29tcG9uZW50cy9BZGRUb0NhcnRCdXR0b24udHN4P2UyN2UiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBZGRUb0NhcnRCdXR0b24oeyB2YXJpYW50SWQgfTogeyB2YXJpYW50SWQ6IHN0cmluZyB9KSB7XG4gIGFzeW5jIGZ1bmN0aW9uIGFkZCgpIHtcbiAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaCgnL2FwaS9jYXJ0JywgeyBtZXRob2Q6ICdQT1NUJywgaGVhZGVyczogeyAnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgdmFyaWFudElkLCBxdWFudGl0eTogMSB9KSB9KTtcbiAgICBpZiAoIXJlcy5vaykgYWxlcnQoJ0ZhaWxlZCB0byBhZGQgdG8gY2FydCcpO1xuICAgIGVsc2UgYWxlcnQoJ0FkZGVkIHRvIGNhcnQnKTtcbiAgfVxuICByZXR1cm4gPGJ1dHRvbiBvbkNsaWNrPXthZGR9IGFyaWEtbGFiZWw9XCJBZGQgdG8gY2FydFwiPkFkZCB0byBjYXJ0PC9idXR0b24+O1xufVxuIl0sIm5hbWVzIjpbIkFkZFRvQ2FydEJ1dHRvbiIsInZhcmlhbnRJZCIsImFkZCIsInJlcyIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwicXVhbnRpdHkiLCJvayIsImFsZXJ0IiwiYnV0dG9uIiwib25DbGljayIsImFyaWEtbGFiZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AddToCartButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PriceAlertModal.tsx":
/*!********************************************!*\
  !*** ./src/components/PriceAlertModal.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PriceAlertModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PriceAlertModal({ isOpen, onClose, productName, currentPrice, productId }) {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [targetPrice, setTargetPrice] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(currentPrice * 0.9); // Default to 10% off\n    const [alertType, setAlertType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"price_drop\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSuccess, setIsSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    if (!isOpen) return null;\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        setIsSubmitting(false);\n        setIsSuccess(true);\n        // Auto close after success\n        setTimeout(()=>{\n            setIsSuccess(false);\n            onClose();\n        }, 3000);\n    };\n    const suggestedPrices = [\n        {\n            label: \"5% off\",\n            price: currentPrice * 0.95\n        },\n        {\n            label: \"10% off\",\n            price: currentPrice * 0.90\n        },\n        {\n            label: \"15% off\",\n            price: currentPrice * 0.85\n        },\n        {\n            label: \"20% off\",\n            price: currentPrice * 0.80\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"fixed\",\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            zIndex: 1000,\n            padding: \"1rem\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                borderRadius: \"12px\",\n                padding: \"2rem\",\n                maxWidth: \"500px\",\n                width: \"100%\",\n                maxHeight: \"90vh\",\n                overflowY: \"auto\",\n                position: \"relative\"\n            },\n            className: \"jsx-d525c98fb358669b\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    style: {\n                        position: \"absolute\",\n                        top: \"1rem\",\n                        right: \"1rem\",\n                        background: \"none\",\n                        border: \"none\",\n                        fontSize: \"1.5rem\",\n                        cursor: \"pointer\",\n                        color: \"#666\"\n                    },\n                    className: \"jsx-d525c98fb358669b\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                isSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        padding: \"2rem 0\"\n                    },\n                    className: \"jsx-d525c98fb358669b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: \"4rem\",\n                                marginBottom: \"1rem\"\n                            },\n                            className: \"jsx-d525c98fb358669b\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: \"#28a745\",\n                                marginBottom: \"1rem\"\n                            },\n                            className: \"jsx-d525c98fb358669b\",\n                            children: \"Price Alert Set!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"#666\"\n                            },\n                            className: \"jsx-d525c98fb358669b\",\n                            children: [\n                                \"We'll notify you when \",\n                                productName,\n                                \" meets your price criteria.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                marginBottom: \"1rem\",\n                                color: \"#333\"\n                            },\n                            className: \"jsx-d525c98fb358669b\",\n                            children: \"\\uD83D\\uDD14 Set Price Alert\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#f8f9fa\",\n                                padding: \"1rem\",\n                                borderRadius: \"8px\",\n                                marginBottom: \"1.5rem\"\n                            },\n                            className: \"jsx-d525c98fb358669b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontWeight: \"bold\",\n                                        marginBottom: \"0.5rem\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: productName\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: \"#666\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: [\n                                        \"Current price: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: [\n                                                \"$\",\n                                                currentPrice.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"jsx-d525c98fb358669b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"1.5rem\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: \"block\",\n                                                fontWeight: \"bold\",\n                                                marginBottom: \"0.5rem\",\n                                                color: \"#333\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: \"Alert Type\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                flexDirection: \"column\",\n                                                gap: \"0.5rem\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"0.5rem\"\n                                                    },\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            value: \"price_drop\",\n                                                            checked: alertType === \"price_drop\",\n                                                            onChange: (e)=>setAlertType(e.target.value),\n                                                            className: \"jsx-d525c98fb358669b\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d525c98fb358669b\",\n                                                            children: \"\\uD83D\\uDCB0 Price drops to target amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"0.5rem\"\n                                                    },\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            value: \"competitor_beat\",\n                                                            checked: alertType === \"competitor_beat\",\n                                                            onChange: (e)=>setAlertType(e.target.value),\n                                                            className: \"jsx-d525c98fb358669b\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d525c98fb358669b\",\n                                                            children: \"\\uD83C\\uDFC6 We beat competitor prices\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        gap: \"0.5rem\"\n                                                    },\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            value: \"back_in_stock\",\n                                                            checked: alertType === \"back_in_stock\",\n                                                            onChange: (e)=>setAlertType(e.target.value),\n                                                            className: \"jsx-d525c98fb358669b\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d525c98fb358669b\",\n                                                            children: \"\\uD83D\\uDCE6 Back in stock notification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                alertType === \"price_drop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"1.5rem\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: \"block\",\n                                                fontWeight: \"bold\",\n                                                marginBottom: \"0.5rem\",\n                                                color: \"#333\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: \"Target Price\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            step: \"0.01\",\n                                            min: \"0\",\n                                            max: currentPrice,\n                                            value: targetPrice,\n                                            onChange: (e)=>setTargetPrice(parseFloat(e.target.value)),\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"0.75rem\",\n                                                border: \"2px solid #e1e5e9\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"1rem\",\n                                                marginBottom: \"0.5rem\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"flex\",\n                                                gap: \"0.5rem\",\n                                                flexWrap: \"wrap\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: suggestedPrices.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setTargetPrice(suggestion.price),\n                                                    style: {\n                                                        padding: \"0.25rem 0.5rem\",\n                                                        backgroundColor: targetPrice === suggestion.price ? \"#007bff\" : \"#f8f9fa\",\n                                                        color: targetPrice === suggestion.price ? \"white\" : \"#666\",\n                                                        border: \"1px solid #e1e5e9\",\n                                                        borderRadius: \"12px\",\n                                                        fontSize: \"0.8rem\",\n                                                        cursor: \"pointer\"\n                                                    },\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: [\n                                                        suggestion.label,\n                                                        \" ($\",\n                                                        suggestion.price.toFixed(2),\n                                                        \")\"\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"0.9rem\",\n                                                color: targetPrice < currentPrice ? \"#28a745\" : \"#dc3545\",\n                                                marginTop: \"0.5rem\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: targetPrice < currentPrice ? `💰 Save $${(currentPrice - targetPrice).toFixed(2)} (${Math.round((1 - targetPrice / currentPrice) * 100)}% off)` : \"⚠️ Target price should be lower than current price\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginBottom: \"1.5rem\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: \"block\",\n                                                fontWeight: \"bold\",\n                                                marginBottom: \"0.5rem\",\n                                                color: \"#333\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: \"Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            placeholder: \"<EMAIL>\",\n                                            style: {\n                                                width: \"100%\",\n                                                padding: \"0.75rem\",\n                                                border: \"2px solid #e1e5e9\",\n                                                borderRadius: \"8px\",\n                                                fontSize: \"1rem\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: \"#e3f2fd\",\n                                        padding: \"1rem\",\n                                        borderRadius: \"8px\",\n                                        marginBottom: \"1.5rem\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            style: {\n                                                margin: \"0 0 0.5rem 0\",\n                                                color: \"#1976d2\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: \"\\uD83D\\uDE80 Smart Alert Features\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            style: {\n                                                margin: 0,\n                                                paddingLeft: \"1.2rem\",\n                                                color: \"#666\",\n                                                fontSize: \"0.9rem\"\n                                            },\n                                            className: \"jsx-d525c98fb358669b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: \"Real-time price monitoring across 10+ retailers\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: \"Instant notifications via email and SMS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: \"Price history and trend analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: \"Stock availability alerts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"jsx-d525c98fb358669b\",\n                                                    children: \"Competitor price beat notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting || alertType === \"price_drop\" && targetPrice >= currentPrice,\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"1rem\",\n                                        backgroundColor: isSubmitting ? \"#6c757d\" : \"#007bff\",\n                                        color: \"white\",\n                                        border: \"none\",\n                                        borderRadius: \"8px\",\n                                        fontSize: \"1.1rem\",\n                                        fontWeight: \"bold\",\n                                        cursor: isSubmitting ? \"not-allowed\" : \"pointer\",\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        justifyContent: \"center\",\n                                        gap: \"0.5rem\"\n                                    },\n                                    className: \"jsx-d525c98fb358669b\",\n                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    width: \"20px\",\n                                                    height: \"20px\",\n                                                    border: \"2px solid #ffffff\",\n                                                    borderTop: \"2px solid transparent\",\n                                                    borderRadius: \"50%\",\n                                                    animation: \"spin 1s linear infinite\"\n                                                },\n                                                className: \"jsx-d525c98fb358669b\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Setting Up Alert...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: \"\\uD83D\\uDD14 Create Price Alert\"\n                                    }, void 0, false)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"d525c98fb358669b\",\n                    children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n                }, void 0, false, void 0, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceAlertModal.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PriceAlertModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PriceComparison.tsx":
/*!********************************************!*\
  !*** ./src/components/PriceComparison.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PriceComparison)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _PriceAlertModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PriceAlertModal */ \"(ssr)/./src/components/PriceAlertModal.tsx\");\n/* harmony import */ var _PriceHistoryChart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PriceHistoryChart */ \"(ssr)/./src/components/PriceHistoryChart.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction PriceComparison({ productName, ourPrice, productId }) {\n    const [competitors, setCompetitors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Date());\n    const [showAll, setShowAll] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showPriceAlert, setShowPriceAlert] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showPriceHistory, setShowPriceHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Mock competitor data - in real app, this would come from API\n    const mockCompetitorData = {\n        \"iphone-15-pro-max\": [\n            {\n                store: \"Amazon\",\n                price: 1189.99,\n                originalPrice: 1199.99,\n                url: \"https://amazon.com\",\n                logo: \"\\uD83D\\uDED2\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"2 min ago\",\n                trustScore: 4.8\n            },\n            {\n                store: \"Best Buy\",\n                price: 1199.00,\n                url: \"https://bestbuy.com\",\n                logo: \"\\uD83C\\uDFEA\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"5 min ago\",\n                trustScore: 4.7\n            },\n            {\n                store: \"Apple Store\",\n                price: 1199.99,\n                url: \"https://apple.com\",\n                logo: \"\\uD83C\\uDF4E\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"1 min ago\",\n                trustScore: 5.0\n            },\n            {\n                store: \"B&H Photo\",\n                price: 1179.99,\n                url: \"https://bhphotovideo.com\",\n                logo: \"\\uD83D\\uDCF7\",\n                shipping: \"Free shipping\",\n                availability: \"limited\",\n                lastUpdated: \"8 min ago\",\n                trustScore: 4.6\n            },\n            {\n                store: \"Walmart\",\n                price: 1199.99,\n                url: \"https://walmart.com\",\n                logo: \"\\uD83C\\uDFEC\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"12 min ago\",\n                trustScore: 4.3\n            },\n            {\n                store: \"Target\",\n                price: 1209.99,\n                url: \"https://target.com\",\n                logo: \"\\uD83C\\uDFAF\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"15 min ago\",\n                trustScore: 4.4\n            }\n        ],\n        \"macbook-air-m3\": [\n            {\n                store: \"Apple Store\",\n                price: 1299.99,\n                url: \"https://apple.com\",\n                logo: \"\\uD83C\\uDF4E\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"1 min ago\",\n                trustScore: 5.0\n            },\n            {\n                store: \"Amazon\",\n                price: 1299.99,\n                url: \"https://amazon.com\",\n                logo: \"\\uD83D\\uDED2\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"3 min ago\",\n                trustScore: 4.8\n            },\n            {\n                store: \"Best Buy\",\n                price: 1299.99,\n                url: \"https://bestbuy.com\",\n                logo: \"\\uD83C\\uDFEA\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"7 min ago\",\n                trustScore: 4.7\n            },\n            {\n                store: \"Costco\",\n                price: 1279.99,\n                url: \"https://costco.com\",\n                logo: \"\\uD83C\\uDFE2\",\n                shipping: \"Free shipping\",\n                availability: \"limited\",\n                lastUpdated: \"20 min ago\",\n                trustScore: 4.5\n            }\n        ],\n        \"sony-wh-1000xm5\": [\n            {\n                store: \"Amazon\",\n                price: 379.99,\n                originalPrice: 399.99,\n                url: \"https://amazon.com\",\n                logo: \"\\uD83D\\uDED2\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"1 min ago\",\n                trustScore: 4.8\n            },\n            {\n                store: \"Best Buy\",\n                price: 399.99,\n                url: \"https://bestbuy.com\",\n                logo: \"\\uD83C\\uDFEA\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"4 min ago\",\n                trustScore: 4.7\n            },\n            {\n                store: \"Sony Direct\",\n                price: 399.99,\n                url: \"https://sony.com\",\n                logo: \"\\uD83D\\uDCFA\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"2 min ago\",\n                trustScore: 4.9\n            },\n            {\n                store: \"Target\",\n                price: 389.99,\n                url: \"https://target.com\",\n                logo: \"\\uD83C\\uDFAF\",\n                shipping: \"Free shipping\",\n                availability: \"in_stock\",\n                lastUpdated: \"6 min ago\",\n                trustScore: 4.4\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Simulate API call to fetch competitor prices\n        const fetchPrices = async ()=>{\n            setIsLoading(true);\n            // Simulate network delay\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            const competitorData = mockCompetitorData[productId] || [];\n            setCompetitors(competitorData);\n            setLastUpdated(new Date());\n            setIsLoading(false);\n        };\n        fetchPrices();\n        // Set up real-time updates every 5 minutes\n        const interval = setInterval(fetchPrices, 5 * 60 * 1000);\n        return ()=>clearInterval(interval);\n    }, [\n        productId\n    ]);\n    const getAvailabilityColor = (availability)=>{\n        switch(availability){\n            case \"in_stock\":\n                return \"#28a745\";\n            case \"limited\":\n                return \"#ffc107\";\n            case \"out_of_stock\":\n                return \"#dc3545\";\n            default:\n                return \"#6c757d\";\n        }\n    };\n    const getAvailabilityText = (availability)=>{\n        switch(availability){\n            case \"in_stock\":\n                return \"In Stock\";\n            case \"limited\":\n                return \"Limited Stock\";\n            case \"out_of_stock\":\n                return \"Out of Stock\";\n            default:\n                return \"Unknown\";\n        }\n    };\n    const lowestPrice = Math.min(...competitors.map((c)=>c.price));\n    const ourRank = competitors.filter((c)=>c.price < ourPrice).length + 1;\n    const savings = competitors.length > 0 ? Math.max(0, Math.max(...competitors.map((c)=>c.price)) - ourPrice) : 0;\n    const displayedCompetitors = showAll ? competitors : competitors.slice(0, 3);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"#f8f9fa\",\n                border: \"1px solid #e1e5e9\",\n                borderRadius: \"12px\",\n                padding: \"1.5rem\",\n                marginBottom: \"2rem\"\n            },\n            className: \"jsx-d525c98fb358669b\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"0.5rem\",\n                        marginBottom: \"1rem\"\n                    },\n                    className: \"jsx-d525c98fb358669b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: \"20px\",\n                                height: \"20px\",\n                                border: \"2px solid #007bff\",\n                                borderTop: \"2px solid transparent\",\n                                borderRadius: \"50%\",\n                                animation: \"spin 1s linear infinite\"\n                            },\n                            className: \"jsx-d525c98fb358669b\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: 0\n                            },\n                            className: \"jsx-d525c98fb358669b\",\n                            children: \"\\uD83D\\uDD0D Checking competitor prices...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: \"#666\",\n                        margin: 0\n                    },\n                    className: \"jsx-d525c98fb358669b\",\n                    children: [\n                        \"Scanning major retailers for the best deals on \",\n                        productName\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"d525c98fb358669b\",\n                    children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n                }, void 0, false, void 0, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: \"#ffffff\",\n            border: \"1px solid #e1e5e9\",\n            borderRadius: \"12px\",\n            padding: \"1.5rem\",\n            marginBottom: \"2rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"1.5rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: 0,\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\"\n                                },\n                                children: \"\\uD83D\\uDCB0 Real-Time Price Comparison\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: \"#666\"\n                                },\n                                children: [\n                                    \"\\uD83D\\uDD04 Updated \",\n                                    lastUpdated.toLocaleTimeString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"1rem\",\n                            padding: \"1rem\",\n                            backgroundColor: ourPrice <= lowestPrice ? \"#d4edda\" : \"#fff3cd\",\n                            borderRadius: \"8px\",\n                            marginBottom: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontWeight: \"bold\",\n                                            color: \"#333\"\n                                        },\n                                        children: \"ElectroHub Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"1.5rem\",\n                                            fontWeight: \"bold\",\n                                            color: \"#007bff\"\n                                        },\n                                        children: [\n                                            \"$\",\n                                            ourPrice.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    flex: 1\n                                },\n                                children: ourPrice <= lowestPrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"0.5rem\",\n                                        color: \"#155724\",\n                                        fontWeight: \"bold\"\n                                    },\n                                    children: [\n                                        \"\\uD83C\\uDFC6 BEST PRICE GUARANTEED\",\n                                        savings > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                backgroundColor: \"#28a745\",\n                                                color: \"white\",\n                                                padding: \"0.25rem 0.5rem\",\n                                                borderRadius: \"12px\",\n                                                fontSize: \"0.8rem\"\n                                            },\n                                            children: [\n                                                \"Save $\",\n                                                savings.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: \"#856404\"\n                                    },\n                                    children: [\n                                        \"#\",\n                                        ourRank,\n                                        \" of \",\n                                        competitors.length + 1,\n                                        \" retailers\",\n                                        lowestPrice < ourPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: \"0.9rem\"\n                                            },\n                                            children: [\n                                                \"Lowest found: $\",\n                                                lowestPrice.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"1rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        style: {\n                            marginBottom: \"1rem\",\n                            color: \"#333\"\n                        },\n                        children: [\n                            \"\\uD83C\\uDFEA Compare with \",\n                            competitors.length,\n                            \" other retailers:\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            gap: \"0.75rem\"\n                        },\n                        children: displayedCompetitors.map((competitor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"60px 1fr auto auto auto\",\n                                    alignItems: \"center\",\n                                    gap: \"1rem\",\n                                    padding: \"1rem\",\n                                    backgroundColor: \"#f8f9fa\",\n                                    borderRadius: \"8px\",\n                                    border: competitor.price === lowestPrice ? \"2px solid #28a745\" : \"1px solid #e1e5e9\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"1.5rem\",\n                                                    marginBottom: \"0.25rem\"\n                                                },\n                                                children: competitor.logo\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"0.7rem\",\n                                                    color: \"#666\"\n                                                },\n                                                children: [\n                                                    \"⭐ \",\n                                                    competitor.trustScore\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: \"bold\",\n                                                    marginBottom: \"0.25rem\"\n                                                },\n                                                children: competitor.store\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"0.8rem\",\n                                                    color: \"#666\"\n                                                },\n                                                children: [\n                                                    competitor.shipping,\n                                                    \" • \",\n                                                    competitor.lastUpdated\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: \"right\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"1.2rem\",\n                                                    fontWeight: \"bold\",\n                                                    color: competitor.price < ourPrice ? \"#dc3545\" : competitor.price > ourPrice ? \"#28a745\" : \"#007bff\"\n                                                },\n                                                children: [\n                                                    \"$\",\n                                                    competitor.price.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            competitor.originalPrice && competitor.originalPrice > competitor.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"0.9rem\",\n                                                    color: \"#666\",\n                                                    textDecoration: \"line-through\"\n                                                },\n                                                children: [\n                                                    \"$\",\n                                                    competitor.originalPrice.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            textAlign: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: \"0.25rem 0.5rem\",\n                                                backgroundColor: getAvailabilityColor(competitor.availability) + \"20\",\n                                                color: getAvailabilityColor(competitor.availability),\n                                                borderRadius: \"12px\",\n                                                fontSize: \"0.8rem\",\n                                                fontWeight: \"bold\"\n                                            },\n                                            children: getAvailabilityText(competitor.availability)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: competitor.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            style: {\n                                                padding: \"0.5rem 1rem\",\n                                                backgroundColor: \"#007bff\",\n                                                color: \"white\",\n                                                textDecoration: \"none\",\n                                                borderRadius: \"6px\",\n                                                fontSize: \"0.8rem\",\n                                                fontWeight: \"bold\"\n                                            },\n                                            children: \"Visit Store →\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    competitors.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowAll(!showAll),\n                        style: {\n                            marginTop: \"1rem\",\n                            padding: \"0.75rem 1.5rem\",\n                            backgroundColor: \"transparent\",\n                            color: \"#007bff\",\n                            border: \"1px solid #007bff\",\n                            borderRadius: \"6px\",\n                            cursor: \"pointer\",\n                            fontWeight: \"bold\",\n                            width: \"100%\"\n                        },\n                        children: showAll ? \"▲ Show Less\" : `▼ Show All ${competitors.length} Retailers`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                    gap: \"1rem\",\n                    marginBottom: \"1rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowPriceAlert(true),\n                        style: {\n                            padding: \"1rem\",\n                            backgroundColor: \"#ffc107\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            fontWeight: \"bold\",\n                            cursor: \"pointer\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            gap: \"0.5rem\"\n                        },\n                        children: \"\\uD83D\\uDD14 Set Price Alert\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowPriceHistory(!showPriceHistory),\n                        style: {\n                            padding: \"1rem\",\n                            backgroundColor: \"#17a2b8\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            fontWeight: \"bold\",\n                            cursor: \"pointer\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            gap: \"0.5rem\"\n                        },\n                        children: [\n                            \"\\uD83D\\uDCC8 \",\n                            showPriceHistory ? \"Hide\" : \"Show\",\n                            \" Price History\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: \"#e3f2fd\",\n                    border: \"1px solid #2196f3\",\n                    borderRadius: \"8px\",\n                    padding: \"1rem\",\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontWeight: \"bold\",\n                            color: \"#1976d2\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        children: \"\\uD83D\\uDEE1️ Price Match Guarantee\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"0.9rem\",\n                            color: \"#666\"\n                        },\n                        children: \"Found a lower price? We'll match it! Contact us with proof of a lower price from an authorized retailer.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                lineNumber: 497,\n                columnNumber: 7\n            }, this),\n            showPriceHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"1rem\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PriceHistoryChart__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    productId: productId,\n                    currentPrice: ourPrice\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                    lineNumber: 515,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                lineNumber: 514,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PriceAlertModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showPriceAlert,\n                onClose: ()=>setShowPriceAlert(false),\n                productName: productName,\n                currentPrice: ourPrice,\n                productId: productId\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n                lineNumber: 520,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PriceComparison.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PriceHistoryChart.tsx":
/*!**********************************************!*\
  !*** ./src/components/PriceHistoryChart.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PriceHistoryChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PriceHistoryChart({ productId, currentPrice }) {\n    const [priceHistory, setPriceHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"30d\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Mock price history data\n    const mockPriceHistory = {\n        \"iphone-15-pro-max\": [\n            {\n                date: \"2024-01-01\",\n                price: 1199.99,\n                store: \"ElectroHub\",\n                event: \"Launch\"\n            },\n            {\n                date: \"2024-01-15\",\n                price: 1189.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-02-01\",\n                price: 1199.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-02-14\",\n                price: 1179.99,\n                store: \"ElectroHub\",\n                event: \"Valentine Sale\"\n            },\n            {\n                date: \"2024-02-20\",\n                price: 1199.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-03-01\",\n                price: 1189.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-03-15\",\n                price: 1199.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-03-20\",\n                price: 1199.99,\n                store: \"ElectroHub\"\n            }\n        ],\n        \"macbook-air-m3\": [\n            {\n                date: \"2024-01-01\",\n                price: 1299.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-01-15\",\n                price: 1299.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-02-01\",\n                price: 1289.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-02-15\",\n                price: 1299.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-03-01\",\n                price: 1299.99,\n                store: \"ElectroHub\"\n            }\n        ],\n        \"sony-wh-1000xm5\": [\n            {\n                date: \"2024-01-01\",\n                price: 399.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-01-15\",\n                price: 389.99,\n                store: \"ElectroHub\",\n                event: \"Flash Sale\"\n            },\n            {\n                date: \"2024-02-01\",\n                price: 399.99,\n                store: \"ElectroHub\"\n            },\n            {\n                date: \"2024-02-15\",\n                price: 379.99,\n                store: \"ElectroHub\",\n                event: \"Price Match\"\n            },\n            {\n                date: \"2024-03-01\",\n                price: 399.99,\n                store: \"ElectroHub\"\n            }\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchPriceHistory = async ()=>{\n            setIsLoading(true);\n            // Simulate API delay\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const history = mockPriceHistory[productId] || [];\n            setPriceHistory(history);\n            setIsLoading(false);\n        };\n        fetchPriceHistory();\n    }, [\n        productId,\n        timeRange\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"#f8f9fa\",\n                border: \"1px solid #e1e5e9\",\n                borderRadius: \"12px\",\n                padding: \"2rem\",\n                textAlign: \"center\"\n            },\n            className: \"jsx-d525c98fb358669b\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        width: \"40px\",\n                        height: \"40px\",\n                        border: \"4px solid #007bff\",\n                        borderTop: \"4px solid transparent\",\n                        borderRadius: \"50%\",\n                        animation: \"spin 1s linear infinite\",\n                        margin: \"0 auto 1rem\"\n                    },\n                    className: \"jsx-d525c98fb358669b\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-d525c98fb358669b\",\n                    children: \"Loading price history...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"d525c98fb358669b\",\n                    children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n                }, void 0, false, void 0, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    if (priceHistory.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"#f8f9fa\",\n                border: \"1px solid #e1e5e9\",\n                borderRadius: \"12px\",\n                padding: \"2rem\",\n                textAlign: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: \"2rem\",\n                        marginBottom: \"1rem\"\n                    },\n                    children: \"\\uD83D\\uDCCA\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"No price history available for this product\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    const minPrice = Math.min(...priceHistory.map((p)=>p.price));\n    const maxPrice = Math.max(...priceHistory.map((p)=>p.price));\n    const priceRange = maxPrice - minPrice;\n    const currentSavings = maxPrice - currentPrice;\n    const avgPrice = priceHistory.reduce((sum, p)=>sum + p.price, 0) / priceHistory.length;\n    // Calculate chart dimensions\n    const chartWidth = 600;\n    const chartHeight = 200;\n    const padding = 40;\n    const getYPosition = (price)=>{\n        if (priceRange === 0) return chartHeight / 2;\n        return chartHeight - (price - minPrice) / priceRange * (chartHeight - padding * 2) - padding;\n    };\n    const getXPosition = (index)=>{\n        return index / (priceHistory.length - 1) * (chartWidth - padding * 2) + padding;\n    };\n    // Create SVG path for price line\n    const pathData = priceHistory.map((point, index)=>{\n        const x = getXPosition(index);\n        const y = getYPosition(point.price);\n        return `${index === 0 ? \"M\" : \"L\"} ${x} ${y}`;\n    }).join(\" \");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: \"#ffffff\",\n            border: \"1px solid #e1e5e9\",\n            borderRadius: \"12px\",\n            padding: \"1.5rem\",\n            marginBottom: \"2rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"1.5rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            margin: 0\n                        },\n                        children: \"\\uD83D\\uDCC8 Price History\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            gap: \"0.5rem\"\n                        },\n                        children: [\n                            \"7d\",\n                            \"30d\",\n                            \"90d\",\n                            \"1y\"\n                        ].map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setTimeRange(range),\n                                style: {\n                                    padding: \"0.5rem 1rem\",\n                                    backgroundColor: timeRange === range ? \"#007bff\" : \"#f8f9fa\",\n                                    color: timeRange === range ? \"white\" : \"#666\",\n                                    border: \"1px solid #e1e5e9\",\n                                    borderRadius: \"6px\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"0.9rem\"\n                                },\n                                children: range\n                            }, range, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(120px, 1fr))\",\n                    gap: \"1rem\",\n                    marginBottom: \"1.5rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1.2rem\",\n                                    fontWeight: \"bold\",\n                                    color: \"#28a745\"\n                                },\n                                children: [\n                                    \"$\",\n                                    minPrice.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: \"#666\"\n                                },\n                                children: \"Lowest Price\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1.2rem\",\n                                    fontWeight: \"bold\",\n                                    color: \"#007bff\"\n                                },\n                                children: [\n                                    \"$\",\n                                    avgPrice.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: \"#666\"\n                                },\n                                children: \"Average Price\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1.2rem\",\n                                    fontWeight: \"bold\",\n                                    color: \"#dc3545\"\n                                },\n                                children: [\n                                    \"$\",\n                                    maxPrice.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: \"#666\"\n                                },\n                                children: \"Highest Price\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    currentSavings > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1.2rem\",\n                                    fontWeight: \"bold\",\n                                    color: \"#28a745\"\n                                },\n                                children: [\n                                    \"$\",\n                                    currentSavings.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.8rem\",\n                                    color: \"#666\"\n                                },\n                                children: \"Current Savings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: \"#f8f9fa\",\n                    borderRadius: \"8px\",\n                    padding: \"1rem\",\n                    marginBottom: \"1rem\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"100%\",\n                    height: chartHeight,\n                    viewBox: `0 0 ${chartWidth} ${chartHeight}`,\n                    style: {\n                        overflow: \"visible\"\n                    },\n                    children: [\n                        [\n                            0,\n                            0.25,\n                            0.5,\n                            0.75,\n                            1\n                        ].map((ratio)=>{\n                            const y = padding + ratio * (chartHeight - padding * 2);\n                            const price = maxPrice - ratio * priceRange;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: padding,\n                                        y1: y,\n                                        x2: chartWidth - padding,\n                                        y2: y,\n                                        stroke: \"#e1e5e9\",\n                                        strokeWidth: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                        x: padding - 10,\n                                        y: y + 4,\n                                        fontSize: \"12\",\n                                        fill: \"#666\",\n                                        textAnchor: \"end\",\n                                        children: [\n                                            \"$\",\n                                            price.toFixed(0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, ratio, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this);\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: pathData,\n                            fill: \"none\",\n                            stroke: \"#007bff\",\n                            strokeWidth: \"3\",\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        priceHistory.map((point, index)=>{\n                            const x = getXPosition(index);\n                            const y = getYPosition(point.price);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: x,\n                                        cy: y,\n                                        r: \"4\",\n                                        fill: point.event ? \"#ffc107\" : \"#007bff\",\n                                        stroke: \"white\",\n                                        strokeWidth: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this),\n                                    point.event && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: x,\n                                                cy: y,\n                                                r: \"8\",\n                                                fill: \"none\",\n                                                stroke: \"#ffc107\",\n                                                strokeWidth: \"2\",\n                                                opacity: \"0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                x: x,\n                                                y: y - 15,\n                                                fontSize: \"10\",\n                                                fill: \"#856404\",\n                                                textAnchor: \"middle\",\n                                                fontWeight: \"bold\",\n                                                children: point.event\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, this);\n                        }),\n                        priceHistory.map((point, index)=>{\n                            if (index % Math.ceil(priceHistory.length / 5) === 0) {\n                                const x = getXPosition(index);\n                                const date = new Date(point.date).toLocaleDateString(\"en-US\", {\n                                    month: \"short\",\n                                    day: \"numeric\"\n                                });\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                    x: x,\n                                    y: chartHeight - 10,\n                                    fontSize: \"12\",\n                                    fill: \"#666\",\n                                    textAnchor: \"middle\",\n                                    children: date\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                            return null;\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: currentPrice < avgPrice ? \"#d4edda\" : \"#fff3cd\",\n                    border: `1px solid ${currentPrice < avgPrice ? \"#c3e6cb\" : \"#ffeaa7\"}`,\n                    borderRadius: \"8px\",\n                    padding: \"1rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"0.5rem\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    fontSize: \"1.2rem\"\n                                },\n                                children: currentPrice < avgPrice ? \"\\uD83D\\uDCC9\" : \"\\uD83D\\uDCC8\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                style: {\n                                    color: currentPrice < avgPrice ? \"#155724\" : \"#856404\"\n                                },\n                                children: \"Price Trend Analysis\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"0.9rem\",\n                            color: currentPrice < avgPrice ? \"#155724\" : \"#856404\"\n                        },\n                        children: currentPrice < avgPrice ? `Current price is ${((1 - currentPrice / avgPrice) * 100).toFixed(1)}% below average. Great time to buy!` : `Current price is ${((currentPrice / avgPrice - 1) * 100).toFixed(1)}% above average. Consider waiting for a better deal.`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceHistoryChart.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PriceHistoryChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SkipLink auto */ \nfunction SkipLink() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main\",\n        style: {\n            position: \"absolute\",\n            left: -9999,\n            top: -9999\n        },\n        onFocus: (e)=>{\n            e.currentTarget.style.left = \"8px\";\n            e.currentTarget.style.top = \"8px\";\n            e.currentTarget.style.background = \"white\";\n            e.currentTarget.style.padding = \"8px\";\n        },\n        children: \"Skip to content\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ta2lwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUVDLE1BQUs7UUFBUUMsT0FBTztZQUFFQyxVQUFVO1lBQVlDLE1BQU0sQ0FBQztZQUFNQyxLQUFLLENBQUM7UUFBSztRQUFHQyxTQUFTLENBQUNDO1lBQ2pGQSxFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNFLElBQUksR0FBRztZQUNuREcsRUFBRUMsYUFBYSxDQUF1Qk4sS0FBSyxDQUFDRyxHQUFHLEdBQUc7WUFDbERFLEVBQUVDLGFBQWEsQ0FBdUJOLEtBQUssQ0FBQ08sVUFBVSxHQUFHO1lBQ3pERixFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNRLE9BQU8sR0FBRztRQUN6RDtrQkFBRzs7Ozs7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4Pzk5NTEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2tpcExpbmsoKSB7XG4gIHJldHVybiAoXG4gICAgPGEgaHJlZj1cIiNtYWluXCIgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGxlZnQ6IC05OTk5LCB0b3A6IC05OTk5IH19IG9uRm9jdXM9eyhlKSA9PiB7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS5sZWZ0ID0gJzhweCc7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS50b3AgPSAnOHB4JztcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnQpLnN0eWxlLmJhY2tncm91bmQgPSAnd2hpdGUnO1xuICAgICAgKGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCkuc3R5bGUucGFkZGluZyA9ICc4cHgnO1xuICAgIH19PlNraXAgdG8gY29udGVudDwvYT5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTa2lwTGluayIsImEiLCJocmVmIiwic3R5bGUiLCJwb3NpdGlvbiIsImxlZnQiLCJ0b3AiLCJvbkZvY3VzIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJiYWNrZ3JvdW5kIiwicGFkZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SkipLink.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1cc9da9c511c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYzlkYTljNTExY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SkipLink */ \"(rsc)/./src/components/SkipLink.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ElectroHub — Electronics Marketplace\",\n    description: \"Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3000\"),\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SkipLink__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    \"aria-label\": \"Site header\",\n                    style: {\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        \"aria-label\": \"Main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"ElectroHub\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            \" | \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 48\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    id: \"main\",\n                    style: {\n                        minHeight: \"60vh\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    style: {\n                        padding: \"2rem\",\n                        borderTop: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" ElectroHub \\xb7 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 60\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/product/[slug]/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/product/[slug]/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_currency__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/currency */ \"(rsc)/./src/lib/currency.ts\");\n/* harmony import */ var _components_AddToCartButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AddToCartButton */ \"(rsc)/./src/components/AddToCartButton.tsx\");\n/* harmony import */ var _components_PriceComparison__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/PriceComparison */ \"(rsc)/./src/components/PriceComparison.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\n// Mock product data - comprehensive product details\nconst mockProducts = {\n    \"iphone-15-pro-max\": {\n        id: \"1\",\n        title: \"iPhone 15 Pro Max\",\n        slug: \"iphone-15-pro-max\",\n        brand: {\n            name: \"Apple\"\n        },\n        category: {\n            name: \"Smartphones\",\n            slug: \"smartphones\"\n        },\n        variants: [\n            {\n                id: \"v1\",\n                price: 1199.99,\n                sku: \"IPHONE15PM-256-TIT\"\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop\"\n            },\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop&sat=-100\"\n            },\n            {\n                url: \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop&hue=180\"\n            }\n        ],\n        description: \"The most advanced iPhone ever with titanium design, A17 Pro chip, and professional camera system.\",\n        features: [\n            \"6.7-inch Super Retina XDR display\",\n            \"A17 Pro chip with 6-core GPU\",\n            \"Pro camera system with 48MP main camera\",\n            \"Up to 29 hours video playback\",\n            \"Titanium design with textured matte glass back\",\n            \"Action Button for quick access to features\",\n            \"USB-C connector\",\n            \"Face ID for secure authentication\"\n        ],\n        specifications: {\n            \"Display\": \"6.7-inch Super Retina XDR OLED, 2796 x 1290 pixels\",\n            \"Processor\": \"A17 Pro chip\",\n            \"Storage\": \"256GB, 512GB, 1TB\",\n            \"Camera\": \"48MP Main, 12MP Ultra Wide, 12MP Telephoto\",\n            \"Battery\": \"Up to 29 hours video playback\",\n            \"Operating System\": \"iOS 17\",\n            \"Connectivity\": \"5G, Wi-Fi 6E, Bluetooth 5.3\",\n            \"Dimensions\": \"159.9 \\xd7 76.7 \\xd7 8.25 mm\",\n            \"Weight\": \"221 grams\"\n        },\n        inStock: true,\n        rating: 4.8,\n        reviewCount: 1247\n    },\n    \"macbook-air-m3\": {\n        id: \"9\",\n        title: \"MacBook Air M3\",\n        slug: \"macbook-air-m3\",\n        brand: {\n            name: \"Apple\"\n        },\n        category: {\n            name: \"Laptops & Computers\",\n            slug: \"laptops-computers\"\n        },\n        variants: [\n            {\n                id: \"v9\",\n                price: 1299.99,\n                sku: \"MBA-M3-13-256-SG\"\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=800&h=600&fit=crop\"\n            },\n            {\n                url: \"https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=800&h=600&fit=crop&sat=-50\"\n            }\n        ],\n        description: \"Supercharged by the M3 chip, MacBook Air is up to 60% faster than the previous generation.\",\n        features: [\n            \"13.6-inch Liquid Retina display\",\n            \"Apple M3 chip with 8-core CPU and 10-core GPU\",\n            \"Up to 18 hours battery life\",\n            \"256GB SSD storage\",\n            \"8GB unified memory\",\n            \"Two Thunderbolt ports\",\n            \"MagSafe 3 charging\",\n            \"Backlit Magic Keyboard with Touch ID\"\n        ],\n        specifications: {\n            \"Display\": \"13.6-inch Liquid Retina, 2560 x 1664 pixels\",\n            \"Processor\": \"Apple M3 chip\",\n            \"Memory\": \"8GB unified memory\",\n            \"Storage\": \"256GB SSD\",\n            \"Graphics\": \"10-core GPU\",\n            \"Battery\": \"Up to 18 hours\",\n            \"Operating System\": \"macOS Sonoma\",\n            \"Ports\": \"2x Thunderbolt, MagSafe 3, 3.5mm headphone\",\n            \"Dimensions\": \"30.41 \\xd7 21.5 \\xd7 1.13 cm\",\n            \"Weight\": \"1.24 kg\"\n        },\n        inStock: true,\n        rating: 4.7,\n        reviewCount: 892\n    },\n    \"sony-wh-1000xm5\": {\n        id: \"16\",\n        title: \"Sony WH-1000XM5\",\n        slug: \"sony-wh-1000xm5\",\n        brand: {\n            name: \"Sony\"\n        },\n        category: {\n            name: \"Audio & Headphones\",\n            slug: \"audio-headphones\"\n        },\n        variants: [\n            {\n                id: \"v16\",\n                price: 399.99,\n                sku: \"SONY-WH1000XM5-BLK\"\n            }\n        ],\n        media: [\n            {\n                url: \"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800&h=600&fit=crop\"\n            }\n        ],\n        description: \"Industry-leading noise canceling with new lightweight design for all-day comfort.\",\n        features: [\n            \"Industry-leading noise canceling technology\",\n            \"30-hour battery life with quick charge\",\n            \"Crystal clear hands-free calling\",\n            \"Intuitive touch control settings\",\n            \"Multipoint connection to two devices\",\n            \"Adaptive Sound Control\",\n            \"Speak-to-Chat technology\",\n            \"Premium comfort and sound\"\n        ],\n        specifications: {\n            \"Driver\": \"30mm dynamic drivers\",\n            \"Frequency Response\": \"4Hz - 40kHz\",\n            \"Battery Life\": \"30 hours with ANC on\",\n            \"Charging\": \"USB-C, 3 min charge = 3 hours playback\",\n            \"Weight\": \"250g\",\n            \"Connectivity\": \"Bluetooth 5.2, NFC\",\n            \"Noise Canceling\": \"Dual Noise Sensor technology\",\n            \"Microphone\": \"Beamforming microphone\"\n        },\n        inStock: true,\n        rating: 4.6,\n        reviewCount: 2156\n    }\n};\nfunction ProductDetailPage({ params }) {\n    const productSlug = params.slug;\n    const product = mockProducts[productSlug];\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: \"2rem\",\n                textAlign: \"center\",\n                maxWidth: \"600px\",\n                margin: \"0 auto\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        'The product \"',\n                        productSlug,\n                        \"\\\" doesn't exist in our catalog.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    href: \"/\",\n                    style: {\n                        color: \"#007bff\",\n                        textDecoration: \"none\",\n                        fontWeight: \"bold\"\n                    },\n                    children: \"← Back to Home\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    const variant = product.variants[0];\n    const mainImage = product.media[0]?.url || \"https://via.placeholder.com/800x600?text=ElectroHub\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: \"1400px\",\n            margin: \"0 auto\",\n            padding: \"1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                style: {\n                    marginBottom: \"2rem\",\n                    fontSize: \"0.9rem\",\n                    color: \"#666\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/\",\n                        style: {\n                            color: \"#007bff\",\n                            textDecoration: \"none\"\n                        },\n                        children: \"Home\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    \" > \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: `/category/${product.category.slug}`,\n                        style: {\n                            color: \"#007bff\",\n                            textDecoration: \"none\"\n                        },\n                        children: product.category.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    \" > \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: product.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"1fr 1fr\",\n                    gap: \"3rem\",\n                    marginBottom: \"3rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"1rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: mainImage,\n                                    alt: product.title,\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"auto\",\n                                        borderRadius: \"12px\",\n                                        boxShadow: \"0 4px 20px rgba(0,0,0,0.1)\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            product.media.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"0.5rem\",\n                                    overflowX: \"auto\"\n                                },\n                                children: product.media.map((media, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: media.url,\n                                        alt: `${product.title} view ${index + 1}`,\n                                        style: {\n                                            width: \"80px\",\n                                            height: \"80px\",\n                                            objectFit: \"cover\",\n                                            borderRadius: \"8px\",\n                                            cursor: \"pointer\",\n                                            border: index === 0 ? \"2px solid #007bff\" : \"2px solid transparent\"\n                                        }\n                                    }, index, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"1rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: `/category/${product.category.slug}`,\n                                    style: {\n                                        color: \"#007bff\",\n                                        textDecoration: \"none\",\n                                        fontSize: \"0.9rem\",\n                                        fontWeight: \"500\"\n                                    },\n                                    children: product.category.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: \"2.5rem\",\n                                    marginBottom: \"0.5rem\",\n                                    lineHeight: \"1.2\"\n                                },\n                                children: product.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"1rem\",\n                                    color: \"#666\",\n                                    fontSize: \"1.1rem\"\n                                },\n                                children: [\n                                    \"by \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: product.brand.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 16\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\",\n                                    marginBottom: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: \"#ffa500\"\n                                        },\n                                        children: [\n                                            \"★\".repeat(Math.floor(product.rating)),\n                                            \"☆\".repeat(5 - Math.floor(product.rating))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontWeight: \"bold\"\n                                        },\n                                        children: product.rating\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"#666\"\n                                        },\n                                        children: [\n                                            \"(\",\n                                            product.reviewCount.toLocaleString(),\n                                            \" reviews)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"2rem\",\n                                    fontWeight: \"bold\",\n                                    color: \"#007bff\",\n                                    marginBottom: \"1.5rem\"\n                                },\n                                children: (0,_lib_currency__WEBPACK_IMPORTED_MODULE_1__.formatMoney)(variant.price)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"1.5rem\",\n                                    padding: \"0.5rem 1rem\",\n                                    backgroundColor: product.inStock ? \"#d4edda\" : \"#f8d7da\",\n                                    color: product.inStock ? \"#155724\" : \"#721c24\",\n                                    borderRadius: \"6px\",\n                                    display: \"inline-block\",\n                                    fontWeight: \"500\"\n                                },\n                                children: product.inStock ? \"✓ In Stock\" : \"✗ Out of Stock\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    fontSize: \"1.1rem\",\n                                    lineHeight: \"1.6\",\n                                    marginBottom: \"2rem\",\n                                    color: \"#333\"\n                                },\n                                children: product.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"2rem\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddToCartButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variantId: variant.id\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#f8f9fa\",\n                                    padding: \"1.5rem\",\n                                    borderRadius: \"12px\",\n                                    marginBottom: \"2rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            marginBottom: \"1rem\"\n                                        },\n                                        children: \"Key Features\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            margin: 0,\n                                            paddingLeft: \"1.2rem\"\n                                        },\n                                        children: product.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                style: {\n                                                    marginBottom: \"0.5rem\",\n                                                    lineHeight: \"1.5\"\n                                                },\n                                                children: feature\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PriceComparison__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                productName: product.title,\n                ourPrice: variant.price,\n                productId: product.slug\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: \"#ffffff\",\n                    border: \"1px solid #e1e5e9\",\n                    borderRadius: \"12px\",\n                    padding: \"2rem\",\n                    marginBottom: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: \"1.5rem\"\n                        },\n                        children: \"Technical Specifications\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n                            gap: \"1rem\"\n                        },\n                        children: Object.entries(product.specifications).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    padding: \"0.75rem\",\n                                    backgroundColor: \"#f8f9fa\",\n                                    borderRadius: \"8px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        style: {\n                                            minWidth: \"120px\",\n                                            color: \"#333\"\n                                        },\n                                        children: [\n                                            key,\n                                            \":\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            color: \"#666\"\n                                        },\n                                        children: value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, key, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\",\n                    padding: \"2rem\",\n                    backgroundColor: \"#f8f9fa\",\n                    borderRadius: \"12px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Related Products\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: \"#666\",\n                            marginBottom: \"1rem\"\n                        },\n                        children: [\n                            \"Discover more products from \",\n                            product.brand.name,\n                            \" and similar items in \",\n                            product.category.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: `/category/${product.category.slug}`,\n                        style: {\n                            display: \"inline-block\",\n                            padding: \"12px 24px\",\n                            backgroundColor: \"#007bff\",\n                            color: \"white\",\n                            textDecoration: \"none\",\n                            borderRadius: \"8px\",\n                            fontWeight: \"bold\"\n                        },\n                        children: [\n                            \"Browse \",\n                            product.category.name,\n                            \" →\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/product/[slug]/page.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/product/[slug]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AddToCartButton.tsx":
/*!********************************************!*\
  !*** ./src/components/AddToCartButton.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/AddToCartButton.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/PriceComparison.tsx":
/*!********************************************!*\
  !*** ./src/components/PriceComparison.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparison.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx#SkipLink`);


/***/ }),

/***/ "(rsc)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatMoney: () => (/* binding */ formatMoney)\n/* harmony export */ });\nfunction formatMoney(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2N1cnJlbmN5LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxTQUFTQSxZQUFZQyxNQUFjLEVBQUVDLFdBQVcsS0FBSyxFQUFFQyxTQUFTLE9BQU87SUFDNUUsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUNGLFFBQVE7UUFBRUcsT0FBTztRQUFZSjtJQUFTLEdBQUdLLE1BQU0sQ0FBQ047QUFDL0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2xpYi9jdXJyZW5jeS50cz81OWM3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBmb3JtYXRNb25leShhbW91bnQ6IG51bWJlciwgY3VycmVuY3kgPSAnVVNEJywgbG9jYWxlID0gJ2VuLVVTJykge1xuICByZXR1cm4gbmV3IEludGwuTnVtYmVyRm9ybWF0KGxvY2FsZSwgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3kgfSkuZm9ybWF0KGFtb3VudCk7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0TW9uZXkiLCJhbW91bnQiLCJjdXJyZW5jeSIsImxvY2FsZSIsIkludGwiLCJOdW1iZXJGb3JtYXQiLCJzdHlsZSIsImZvcm1hdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/currency.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fproduct%2F%5Bslug%5D%2Fpage&page=%2Fproduct%2F%5Bslug%5D%2Fpage&appPaths=%2Fproduct%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2Fproduct%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();