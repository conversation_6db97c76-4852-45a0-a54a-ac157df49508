/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/products/add/page";
exports.ids = ["app/admin/products/add/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fadd%2Fpage&page=%2Fadmin%2Fproducts%2Fadd%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fadd%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fadd%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fadd%2Fpage&page=%2Fadmin%2Fproducts%2Fadd%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fadd%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fadd%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'products',\n        {\n        children: [\n        'add',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/add/page.tsx */ \"(rsc)/./src/app/admin/products/add/page.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/products/add/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/products/add/page\",\n        pathname: \"/admin/products/add\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fadd%2Fpage&page=%2Fadmin%2Fproducts%2Fadd%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fadd%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fadd%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SkipLink.tsx */ \"(ssr)/./src/components/SkipLink.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRlNraXBMaW5rLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraXBMaW5rJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEc7QUFDMUc7QUFDQSxzS0FBOEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLz9mNWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lwTGlua1wiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fadmin%2Fproducts%2Fadd%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fadmin%2Fproducts%2Fadd%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/products/add/page.tsx */ \"(ssr)/./src/app/admin/products/add/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRnNyYyUyRmFwcCUyRmFkbWluJTJGcHJvZHVjdHMlMkZhZGQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQXdHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8/N2UyMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2FwcC9hZG1pbi9wcm9kdWN0cy9hZGQvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fadmin%2Fproducts%2Fadd%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/products/add/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/admin/products/add/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AddProductPage() {\n    const [productInput, setProductInput] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [productData, setProductData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"input\");\n    // Mock AI analysis function\n    const analyzeProduct = async (input)=>{\n        setIsAnalyzing(true);\n        // Simulate AI processing delay\n        await new Promise((resolve)=>setTimeout(resolve, 3000));\n        // Mock AI-generated product data\n        const mockData = {\n            name: input.includes(\"iPhone\") ? \"iPhone 15 Pro Max\" : input.includes(\"MacBook\") ? \"MacBook Air M3\" : input.includes(\"AirPods\") ? \"AirPods Pro 2\" : input.includes(\"Samsung\") ? \"Samsung Galaxy S24 Ultra\" : \"Generic Product\",\n            brand: input.includes(\"iPhone\") || input.includes(\"MacBook\") || input.includes(\"AirPods\") ? \"Apple\" : input.includes(\"Samsung\") ? \"Samsung\" : input.includes(\"Sony\") ? \"Sony\" : \"Unknown Brand\",\n            category: input.toLowerCase().includes(\"phone\") ? \"smartphones\" : input.toLowerCase().includes(\"laptop\") || input.toLowerCase().includes(\"macbook\") ? \"laptops\" : input.toLowerCase().includes(\"airpods\") || input.toLowerCase().includes(\"headphone\") ? \"audio\" : \"electronics\",\n            description: `Premium ${input} with advanced features and cutting-edge technology. Perfect for professionals and enthusiasts alike.`,\n            price: Math.floor(Math.random() * 2000) + 299,\n            specifications: {\n                \"Display\": \"6.7-inch Super Retina XDR\",\n                \"Processor\": \"A17 Pro chip\",\n                \"Storage\": \"256GB, 512GB, 1TB\",\n                \"Camera\": \"48MP Pro camera system\",\n                \"Battery\": \"All-day battery life\",\n                \"Operating System\": \"iOS 17\",\n                \"Connectivity\": \"5G, Wi-Fi 6E, Bluetooth 5.3\",\n                \"Dimensions\": \"159.9 \\xd7 76.7 \\xd7 8.25 mm\"\n            },\n            images: [\n                \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=400&fit=crop\",\n                \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=400&fit=crop&sat=-50\",\n                \"https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=400&fit=crop&hue=180\"\n            ],\n            competitors: [\n                {\n                    store: \"Amazon\",\n                    price: 1199.99,\n                    url: \"https://amazon.com\"\n                },\n                {\n                    store: \"Best Buy\",\n                    price: 1199.00,\n                    url: \"https://bestbuy.com\"\n                },\n                {\n                    store: \"Apple Store\",\n                    price: 1199.99,\n                    url: \"https://apple.com\"\n                },\n                {\n                    store: \"B&H Photo\",\n                    price: 1189.99,\n                    url: \"https://bhphotovideo.com\"\n                }\n            ]\n        };\n        setProductData(mockData);\n        setIsAnalyzing(false);\n        setStep(\"review\");\n    };\n    const handleSubmit = async ()=>{\n        // Simulate saving to database\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setStep(\"success\");\n    };\n    if (step === \"success\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: \"800px\",\n                margin: \"0 auto\",\n                padding: \"2rem\",\n                textAlign: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: \"4rem\",\n                        marginBottom: \"1rem\"\n                    },\n                    children: \"✅\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    style: {\n                        color: \"#28a745\",\n                        marginBottom: \"1rem\"\n                    },\n                    children: \"Product Added Successfully!\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: \"#666\",\n                        marginBottom: \"2rem\"\n                    },\n                    children: [\n                        productData?.name,\n                        \" has been added to your catalog with AI-optimized pricing and images.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        gap: \"1rem\",\n                        justifyContent: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/admin/products/add\",\n                            onClick: ()=>{\n                                setStep(\"input\");\n                                setProductInput(\"\");\n                                setProductData(null);\n                            },\n                            style: {\n                                padding: \"12px 24px\",\n                                backgroundColor: \"#007bff\",\n                                color: \"white\",\n                                textDecoration: \"none\",\n                                borderRadius: \"8px\",\n                                fontWeight: \"bold\"\n                            },\n                            children: \"Add Another Product\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/admin\",\n                            style: {\n                                padding: \"12px 24px\",\n                                backgroundColor: \"#6c757d\",\n                                color: \"white\",\n                                textDecoration: \"none\",\n                                borderRadius: \"8px\",\n                                fontWeight: \"bold\"\n                            },\n                            children: \"Back to Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: \"1200px\",\n            margin: \"0 auto\",\n            padding: \"1rem\"\n        },\n        className: \"jsx-ff161281ed666c63\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"2rem\"\n                },\n                className: \"jsx-ff161281ed666c63\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            fontSize: \"0.9rem\",\n                            color: \"#666\",\n                            marginBottom: \"1rem\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/admin\",\n                                style: {\n                                    color: \"#007bff\",\n                                    textDecoration: \"none\"\n                                },\n                                children: \"Admin\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            \" > \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-ff161281ed666c63\",\n                                children: \"Add Product\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"2.5rem\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: \"\\uD83E\\uDD16 AI-Powered Product Addition\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: \"#666\",\n                            fontSize: \"1.1rem\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: \"Simply describe or name the product - our AI will handle the rest!\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            step === \"input\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: \"#ffffff\",\n                    border: \"1px solid #e1e5e9\",\n                    borderRadius: \"12px\",\n                    padding: \"2rem\"\n                },\n                className: \"jsx-ff161281ed666c63\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            marginBottom: \"1.5rem\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: \"\\uD83D\\uDCDD Product Information\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"2rem\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                style: {\n                                    display: \"block\",\n                                    fontWeight: \"bold\",\n                                    marginBottom: \"0.5rem\",\n                                    color: \"#333\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: \"Product Name or Description\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: productInput,\n                                onChange: (e)=>setProductInput(e.target.value),\n                                placeholder: \"e.g., iPhone 15 Pro Max 256GB Titanium, MacBook Air M3 13-inch, Sony WH-1000XM5 Headphones...\",\n                                style: {\n                                    width: \"100%\",\n                                    minHeight: \"120px\",\n                                    padding: \"1rem\",\n                                    border: \"2px solid #e1e5e9\",\n                                    borderRadius: \"8px\",\n                                    fontSize: \"1rem\",\n                                    resize: \"vertical\"\n                                },\n                                className: \"jsx-ff161281ed666c63\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"0.9rem\",\n                                    color: \"#666\",\n                                    marginTop: \"0.5rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: \"\\uD83D\\uDCA1 The more specific you are, the better our AI can identify and price the product!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>analyzeProduct(productInput),\n                        disabled: !productInput.trim() || isAnalyzing,\n                        style: {\n                            padding: \"1rem 2rem\",\n                            backgroundColor: isAnalyzing ? \"#6c757d\" : \"#007bff\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"8px\",\n                            fontSize: \"1.1rem\",\n                            fontWeight: \"bold\",\n                            cursor: isAnalyzing ? \"not-allowed\" : \"pointer\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"0.5rem\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: \"20px\",\n                                        height: \"20px\",\n                                        border: \"2px solid #ffffff\",\n                                        borderTop: \"2px solid transparent\",\n                                        borderRadius: \"50%\",\n                                        animation: \"spin 1s linear infinite\"\n                                    },\n                                    className: \"jsx-ff161281ed666c63\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 17\n                                }, this),\n                                \"Analyzing Product...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: \"\\uD83D\\uDD0D Analyze with AI\"\n                        }, void 0, false)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    isAnalyzing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"2rem\",\n                            padding: \"1.5rem\",\n                            backgroundColor: \"#f8f9fa\",\n                            borderRadius: \"8px\",\n                            border: \"1px solid #e1e5e9\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    marginBottom: \"1rem\",\n                                    color: \"#007bff\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: \"\\uD83E\\uDD16 AI Processing...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"0.5rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"✅ Identifying product specifications...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"\\uD83D\\uDD0D Searching for competitive pricing...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"\\uD83D\\uDCF8 Finding high-quality product images...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"\\uD83D\\uDCB0 Calculating optimal pricing strategy...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this),\n            step === \"review\" && productData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"2fr 1fr\",\n                    gap: \"2rem\"\n                },\n                className: \"jsx-ff161281ed666c63\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#ffffff\",\n                            border: \"1px solid #e1e5e9\",\n                            borderRadius: \"12px\",\n                            padding: \"2rem\"\n                        },\n                        className: \"jsx-ff161281ed666c63\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    marginBottom: \"1.5rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: \"\\uD83D\\uDCCB Review Product Details\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"2rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: \"#007bff\",\n                                            marginBottom: \"1rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: productData.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"1fr 1fr\",\n                                            gap: \"1rem\",\n                                            marginBottom: \"1rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"Brand:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    productData.brand\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: \"Category:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    productData.category\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"1rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: \"Description:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: \"#666\",\n                                                    marginTop: \"0.5rem\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: productData.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"2rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            marginBottom: \"1rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"\\uD83D\\uDD27 Specifications\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gap: \"0.5rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: Object.entries(productData.specifications).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"grid\",\n                                                    gridTemplateColumns: \"150px 1fr\",\n                                                    padding: \"0.5rem\",\n                                                    backgroundColor: \"#f8f9fa\",\n                                                    borderRadius: \"6px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            key,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: value\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"2rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            marginBottom: \"1rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"\\uD83D\\uDCF8 Product Images\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"grid\",\n                                            gridTemplateColumns: \"repeat(auto-fit, minmax(150px, 1fr))\",\n                                            gap: \"1rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: productData.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image,\n                                                alt: `${productData.name} view ${index + 1}`,\n                                                style: {\n                                                    width: \"100%\",\n                                                    height: \"150px\",\n                                                    objectFit: \"cover\",\n                                                    borderRadius: \"8px\",\n                                                    border: \"1px solid #e1e5e9\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\"\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"1rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSubmit,\n                                        style: {\n                                            padding: \"1rem 2rem\",\n                                            backgroundColor: \"#28a745\",\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"8px\",\n                                            fontSize: \"1.1rem\",\n                                            fontWeight: \"bold\",\n                                            cursor: \"pointer\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"✅ Add Product to Catalog\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setStep(\"input\"),\n                                        style: {\n                                            padding: \"1rem 2rem\",\n                                            backgroundColor: \"#6c757d\",\n                                            color: \"white\",\n                                            border: \"none\",\n                                            borderRadius: \"8px\",\n                                            fontSize: \"1.1rem\",\n                                            fontWeight: \"bold\",\n                                            cursor: \"pointer\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"← Back to Edit\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-ff161281ed666c63\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ffffff\",\n                                    border: \"1px solid #e1e5e9\",\n                                    borderRadius: \"12px\",\n                                    padding: \"1.5rem\",\n                                    marginBottom: \"1rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            marginBottom: \"1rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"\\uD83D\\uDCB0 Pricing Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"2rem\",\n                                            fontWeight: \"bold\",\n                                            color: \"#007bff\",\n                                            marginBottom: \"1rem\",\n                                            textAlign: \"center\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: [\n                                            \"$\",\n                                            productData.price.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            backgroundColor: \"#d4edda\",\n                                            color: \"#155724\",\n                                            padding: \"0.75rem\",\n                                            borderRadius: \"6px\",\n                                            marginBottom: \"1rem\",\n                                            textAlign: \"center\",\n                                            fontSize: \"0.9rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"✅ Competitively Priced\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ffffff\",\n                                    border: \"1px solid #e1e5e9\",\n                                    borderRadius: \"12px\",\n                                    padding: \"1.5rem\"\n                                },\n                                className: \"jsx-ff161281ed666c63\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            marginBottom: \"1rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"\\uD83C\\uDFEA Competitor Prices\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"0.75rem\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: productData.competitors.map((competitor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    alignItems: \"center\",\n                                                    padding: \"0.75rem\",\n                                                    backgroundColor: \"#f8f9fa\",\n                                                    borderRadius: \"6px\"\n                                                },\n                                                className: \"jsx-ff161281ed666c63\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontWeight: \"bold\"\n                                                            },\n                                                            className: \"jsx-ff161281ed666c63\",\n                                                            children: competitor.store\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontWeight: \"bold\",\n                                                            color: competitor.price < productData.price ? \"#dc3545\" : competitor.price > productData.price ? \"#28a745\" : \"#007bff\"\n                                                        },\n                                                        className: \"jsx-ff161281ed666c63\",\n                                                        children: [\n                                                            \"$\",\n                                                            competitor.price.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: \"1rem\",\n                                            fontSize: \"0.9rem\",\n                                            color: \"#666\",\n                                            textAlign: \"center\"\n                                        },\n                                        className: \"jsx-ff161281ed666c63\",\n                                        children: \"\\uD83D\\uDD04 Updated 5 minutes ago\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"ff161281ed666c63\",\n                children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/products/add/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SkipLink auto */ \nfunction SkipLink() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main\",\n        style: {\n            position: \"absolute\",\n            left: -9999,\n            top: -9999\n        },\n        onFocus: (e)=>{\n            e.currentTarget.style.left = \"8px\";\n            e.currentTarget.style.top = \"8px\";\n            e.currentTarget.style.background = \"white\";\n            e.currentTarget.style.padding = \"8px\";\n        },\n        children: \"Skip to content\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ta2lwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUVDLE1BQUs7UUFBUUMsT0FBTztZQUFFQyxVQUFVO1lBQVlDLE1BQU0sQ0FBQztZQUFNQyxLQUFLLENBQUM7UUFBSztRQUFHQyxTQUFTLENBQUNDO1lBQ2pGQSxFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNFLElBQUksR0FBRztZQUNuREcsRUFBRUMsYUFBYSxDQUF1Qk4sS0FBSyxDQUFDRyxHQUFHLEdBQUc7WUFDbERFLEVBQUVDLGFBQWEsQ0FBdUJOLEtBQUssQ0FBQ08sVUFBVSxHQUFHO1lBQ3pERixFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNRLE9BQU8sR0FBRztRQUN6RDtrQkFBRzs7Ozs7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4Pzk5NTEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2tpcExpbmsoKSB7XG4gIHJldHVybiAoXG4gICAgPGEgaHJlZj1cIiNtYWluXCIgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGxlZnQ6IC05OTk5LCB0b3A6IC05OTk5IH19IG9uRm9jdXM9eyhlKSA9PiB7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS5sZWZ0ID0gJzhweCc7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS50b3AgPSAnOHB4JztcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnQpLnN0eWxlLmJhY2tncm91bmQgPSAnd2hpdGUnO1xuICAgICAgKGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCkuc3R5bGUucGFkZGluZyA9ICc4cHgnO1xuICAgIH19PlNraXAgdG8gY29udGVudDwvYT5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTa2lwTGluayIsImEiLCJocmVmIiwic3R5bGUiLCJwb3NpdGlvbiIsImxlZnQiLCJ0b3AiLCJvbkZvY3VzIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJiYWNrZ3JvdW5kIiwicGFkZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SkipLink.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1cc9da9c511c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYzlkYTljNTExY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/products/add/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/admin/products/add/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/app/admin/products/add/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SkipLink */ \"(rsc)/./src/components/SkipLink.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ElectroHub — Electronics Marketplace\",\n    description: \"Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3000\"),\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SkipLink__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    \"aria-label\": \"Site header\",\n                    style: {\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        \"aria-label\": \"Main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"ElectroHub\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            \" | \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 48\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    id: \"main\",\n                    style: {\n                        minHeight: \"60vh\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    style: {\n                        padding: \"2rem\",\n                        borderTop: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" ElectroHub \\xb7 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 60\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx#SkipLink`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fproducts%2Fadd%2Fpage&page=%2Fadmin%2Fproducts%2Fadd%2Fpage&appPaths=%2Fadmin%2Fproducts%2Fadd%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fproducts%2Fadd%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();