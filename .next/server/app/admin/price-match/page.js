/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/price-match/page";
exports.ids = ["app/admin/price-match/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fprice-match%2Fpage&page=%2Fadmin%2Fprice-match%2Fpage&appPaths=%2Fadmin%2Fprice-match%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fprice-match%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fprice-match%2Fpage&page=%2Fadmin%2Fprice-match%2Fpage&appPaths=%2Fadmin%2Fprice-match%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fprice-match%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'price-match',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/price-match/page.tsx */ \"(rsc)/./src/app/admin/price-match/page.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/price-match/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/price-match/page\",\n        pathname: \"/admin/price-match\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fprice-match%2Fpage&page=%2Fadmin%2Fprice-match%2Fpage&appPaths=%2Fadmin%2Fprice-match%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fprice-match%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SkipLink.tsx */ \"(ssr)/./src/components/SkipLink.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZBcHBsZSUyRkRlc2t0b3AlMkZFbGVjdHJvSHViJTJGc3JjJTJGY29tcG9uZW50cyUyRlNraXBMaW5rLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNraXBMaW5rJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBMEc7QUFDMUc7QUFDQSxzS0FBOEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLz9mNWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0FwcGxlL0Rlc2t0b3AvRWxlY3Ryb0h1Yi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTa2lwTGlua1wiXSAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fcomponents%2FSkipLink.tsx%22%2C%22ids%22%3A%5B%22SkipLink%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fadmin%2Fprice-match%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fadmin%2Fprice-match%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/price-match/page.tsx */ \"(ssr)/./src/app/admin/price-match/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGQXBwbGUlMkZEZXNrdG9wJTJGRWxlY3Ryb0h1YiUyRnNyYyUyRmFwcCUyRmFkbWluJTJGcHJpY2UtbWF0Y2glMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXVHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8/YTEzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9BcHBsZS9EZXNrdG9wL0VsZWN0cm9IdWIvc3JjL2FwcC9hZG1pbi9wcmljZS1tYXRjaC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp%2Fadmin%2Fprice-match%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/admin/price-match/page.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/price-match/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PriceMatchPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PriceMatchPage() {\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock price data\n    const priceData = [\n        {\n            id: \"1\",\n            product: \"iPhone 15 Pro Max\",\n            currentPrice: 1199.99,\n            suggestedPrice: 1189.99,\n            competitors: [\n                {\n                    store: \"Amazon\",\n                    price: 1189.99,\n                    lastUpdated: \"2 hours ago\"\n                },\n                {\n                    store: \"Best Buy\",\n                    price: 1199.00,\n                    lastUpdated: \"1 hour ago\"\n                },\n                {\n                    store: \"B&H Photo\",\n                    price: 1179.99,\n                    lastUpdated: \"30 min ago\"\n                },\n                {\n                    store: \"Walmart\",\n                    price: 1199.99,\n                    lastUpdated: \"45 min ago\"\n                }\n            ],\n            priceHistory: [\n                {\n                    date: \"2024-01-01\",\n                    price: 1199.99\n                },\n                {\n                    date: \"2024-01-15\",\n                    price: 1189.99\n                },\n                {\n                    date: \"2024-02-01\",\n                    price: 1199.99\n                },\n                {\n                    date: \"2024-02-15\",\n                    price: 1179.99\n                }\n            ],\n            recommendation: \"decrease\",\n            potentialImpact: \"+15% sales volume, -0.8% margin\"\n        },\n        {\n            id: \"2\",\n            product: \"MacBook Air M3\",\n            currentPrice: 1299.99,\n            suggestedPrice: 1299.99,\n            competitors: [\n                {\n                    store: \"Apple Store\",\n                    price: 1299.99,\n                    lastUpdated: \"1 hour ago\"\n                },\n                {\n                    store: \"Amazon\",\n                    price: 1299.99,\n                    lastUpdated: \"2 hours ago\"\n                },\n                {\n                    store: \"Best Buy\",\n                    price: 1299.99,\n                    lastUpdated: \"1 hour ago\"\n                },\n                {\n                    store: \"Costco\",\n                    price: 1279.99,\n                    lastUpdated: \"3 hours ago\"\n                }\n            ],\n            priceHistory: [\n                {\n                    date: \"2024-01-01\",\n                    price: 1299.99\n                },\n                {\n                    date: \"2024-01-15\",\n                    price: 1299.99\n                },\n                {\n                    date: \"2024-02-01\",\n                    price: 1299.99\n                },\n                {\n                    date: \"2024-02-15\",\n                    price: 1299.99\n                }\n            ],\n            recommendation: \"maintain\",\n            potentialImpact: \"Stable pricing, optimal margin\"\n        },\n        {\n            id: \"3\",\n            product: \"Sony WH-1000XM5\",\n            currentPrice: 399.99,\n            suggestedPrice: 379.99,\n            competitors: [\n                {\n                    store: \"Amazon\",\n                    price: 379.99,\n                    lastUpdated: \"1 hour ago\"\n                },\n                {\n                    store: \"Best Buy\",\n                    price: 399.99,\n                    lastUpdated: \"2 hours ago\"\n                },\n                {\n                    store: \"Target\",\n                    price: 389.99,\n                    lastUpdated: \"1 hour ago\"\n                },\n                {\n                    store: \"Sony Direct\",\n                    price: 399.99,\n                    lastUpdated: \"30 min ago\"\n                }\n            ],\n            priceHistory: [\n                {\n                    date: \"2024-01-01\",\n                    price: 399.99\n                },\n                {\n                    date: \"2024-01-15\",\n                    price: 389.99\n                },\n                {\n                    date: \"2024-02-01\",\n                    price: 399.99\n                },\n                {\n                    date: \"2024-02-15\",\n                    price: 379.99\n                }\n            ],\n            recommendation: \"decrease\",\n            potentialImpact: \"+22% sales volume, -2.1% margin\"\n        }\n    ];\n    const handlePriceUpdate = async (productId, newPrice)=>{\n        setIsUpdating(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        setIsUpdating(false);\n        alert(`Price updated successfully for product ${productId} to $${newPrice}`);\n    };\n    const getRecommendationColor = (recommendation)=>{\n        switch(recommendation){\n            case \"increase\":\n                return \"#28a745\";\n            case \"decrease\":\n                return \"#dc3545\";\n            case \"maintain\":\n                return \"#007bff\";\n            default:\n                return \"#6c757d\";\n        }\n    };\n    const getRecommendationIcon = (recommendation)=>{\n        switch(recommendation){\n            case \"increase\":\n                return \"\\uD83D\\uDCC8\";\n            case \"decrease\":\n                return \"\\uD83D\\uDCC9\";\n            case \"maintain\":\n                return \"\\uD83D\\uDCCA\";\n            default:\n                return \"❓\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: \"1400px\",\n            margin: \"0 auto\",\n            padding: \"1rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        style: {\n                            fontSize: \"0.9rem\",\n                            color: \"#666\",\n                            marginBottom: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/admin\",\n                                style: {\n                                    color: \"#007bff\",\n                                    textDecoration: \"none\"\n                                },\n                                children: \"Admin\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            \" > \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Price Matching\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        style: {\n                            fontSize: \"2.5rem\",\n                            marginBottom: \"0.5rem\"\n                        },\n                        children: \"\\uD83D\\uDCB0 AI Price Matching & Optimization\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: \"#666\",\n                            fontSize: \"1.1rem\"\n                        },\n                        children: \"Real-time competitor analysis and intelligent pricing recommendations\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                    gap: \"1rem\",\n                    marginBottom: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#ffffff\",\n                            border: \"1px solid #e1e5e9\",\n                            borderRadius: \"12px\",\n                            padding: \"1.5rem\",\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"2rem\",\n                                    color: \"#dc3545\",\n                                    marginBottom: \"0.5rem\"\n                                },\n                                children: \"\\uD83D\\uDCC9\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: \"bold\",\n                                    marginBottom: \"0.25rem\"\n                                },\n                                children: \"Price Decrease\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: \"#666\",\n                                    fontSize: \"0.9rem\"\n                                },\n                                children: \"2 products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#ffffff\",\n                            border: \"1px solid #e1e5e9\",\n                            borderRadius: \"12px\",\n                            padding: \"1.5rem\",\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"2rem\",\n                                    color: \"#007bff\",\n                                    marginBottom: \"0.5rem\"\n                                },\n                                children: \"\\uD83D\\uDCCA\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: \"bold\",\n                                    marginBottom: \"0.25rem\"\n                                },\n                                children: \"Maintain Price\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: \"#666\",\n                                    fontSize: \"0.9rem\"\n                                },\n                                children: \"1 product\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#ffffff\",\n                            border: \"1px solid #e1e5e9\",\n                            borderRadius: \"12px\",\n                            padding: \"1.5rem\",\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"2rem\",\n                                    color: \"#28a745\",\n                                    marginBottom: \"0.5rem\"\n                                },\n                                children: \"\\uD83D\\uDCB5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: \"bold\",\n                                    marginBottom: \"0.25rem\"\n                                },\n                                children: \"Potential Savings\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: \"#666\",\n                                    fontSize: \"0.9rem\"\n                                },\n                                children: \"$30.00\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: \"#ffffff\",\n                            border: \"1px solid #e1e5e9\",\n                            borderRadius: \"12px\",\n                            padding: \"1.5rem\",\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"2rem\",\n                                    color: \"#ffc107\",\n                                    marginBottom: \"0.5rem\"\n                                },\n                                children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: \"bold\",\n                                    marginBottom: \"0.25rem\"\n                                },\n                                children: \"Last Update\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: \"#666\",\n                                    fontSize: \"0.9rem\"\n                                },\n                                children: \"5 min ago\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: \"#ffffff\",\n                    border: \"1px solid #e1e5e9\",\n                    borderRadius: \"12px\",\n                    overflow: \"hidden\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: \"1.5rem\",\n                            backgroundColor: \"#f8f9fa\",\n                            borderBottom: \"1px solid #e1e5e9\",\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    margin: 0\n                                },\n                                children: \"\\uD83D\\uDCCA Price Analysis Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                style: {\n                                    padding: \"0.5rem 1rem\",\n                                    backgroundColor: \"#007bff\",\n                                    color: \"white\",\n                                    border: \"none\",\n                                    borderRadius: \"6px\",\n                                    cursor: \"pointer\",\n                                    fontSize: \"0.9rem\"\n                                },\n                                children: \"\\uD83D\\uDD04 Refresh Prices\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            overflowX: \"auto\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            style: {\n                                width: \"100%\",\n                                borderCollapse: \"collapse\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        style: {\n                                            backgroundColor: \"#f8f9fa\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"1rem\",\n                                                    textAlign: \"left\",\n                                                    borderBottom: \"1px solid #e1e5e9\"\n                                                },\n                                                children: \"Product\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"1rem\",\n                                                    textAlign: \"center\",\n                                                    borderBottom: \"1px solid #e1e5e9\"\n                                                },\n                                                children: \"Current Price\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"1rem\",\n                                                    textAlign: \"center\",\n                                                    borderBottom: \"1px solid #e1e5e9\"\n                                                },\n                                                children: \"Suggested Price\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"1rem\",\n                                                    textAlign: \"center\",\n                                                    borderBottom: \"1px solid #e1e5e9\"\n                                                },\n                                                children: \"Recommendation\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"1rem\",\n                                                    textAlign: \"center\",\n                                                    borderBottom: \"1px solid #e1e5e9\"\n                                                },\n                                                children: \"Impact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: \"1rem\",\n                                                    textAlign: \"center\",\n                                                    borderBottom: \"1px solid #e1e5e9\"\n                                                },\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: priceData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            style: {\n                                                borderBottom: \"1px solid #e1e5e9\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"1rem\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontWeight: \"bold\"\n                                                            },\n                                                            children: item.product\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"0.9rem\",\n                                                                color: \"#666\"\n                                                            },\n                                                            children: [\n                                                                \"Lowest competitor: $\",\n                                                                Math.min(...item.competitors.map((c)=>c.price)).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"1rem\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"1.2rem\",\n                                                            fontWeight: \"bold\"\n                                                        },\n                                                        children: [\n                                                            \"$\",\n                                                            item.currentPrice.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"1rem\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"1.2rem\",\n                                                                fontWeight: \"bold\",\n                                                                color: item.suggestedPrice < item.currentPrice ? \"#dc3545\" : item.suggestedPrice > item.currentPrice ? \"#28a745\" : \"#007bff\"\n                                                            },\n                                                            children: [\n                                                                \"$\",\n                                                                item.suggestedPrice.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        item.suggestedPrice !== item.currentPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"0.8rem\",\n                                                                color: \"#666\"\n                                                            },\n                                                            children: [\n                                                                item.suggestedPrice < item.currentPrice ? \"-\" : \"+\",\n                                                                \"$\",\n                                                                Math.abs(item.suggestedPrice - item.currentPrice).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"1rem\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: \"inline-flex\",\n                                                            alignItems: \"center\",\n                                                            gap: \"0.5rem\",\n                                                            padding: \"0.5rem 1rem\",\n                                                            backgroundColor: getRecommendationColor(item.recommendation) + \"20\",\n                                                            color: getRecommendationColor(item.recommendation),\n                                                            borderRadius: \"20px\",\n                                                            fontSize: \"0.9rem\",\n                                                            fontWeight: \"bold\"\n                                                        },\n                                                        children: [\n                                                            getRecommendationIcon(item.recommendation),\n                                                            item.recommendation.toUpperCase()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"1rem\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"0.9rem\",\n                                                            color: \"#666\"\n                                                        },\n                                                        children: item.potentialImpact\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        padding: \"1rem\",\n                                                        textAlign: \"center\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            gap: \"0.5rem\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handlePriceUpdate(item.id, item.suggestedPrice),\n                                                                disabled: isUpdating || item.suggestedPrice === item.currentPrice,\n                                                                style: {\n                                                                    padding: \"0.5rem 1rem\",\n                                                                    backgroundColor: item.suggestedPrice === item.currentPrice ? \"#6c757d\" : \"#28a745\",\n                                                                    color: \"white\",\n                                                                    border: \"none\",\n                                                                    borderRadius: \"6px\",\n                                                                    cursor: item.suggestedPrice === item.currentPrice ? \"not-allowed\" : \"pointer\",\n                                                                    fontSize: \"0.8rem\"\n                                                                },\n                                                                children: [\n                                                                    isUpdating ? \"⏳\" : \"✅\",\n                                                                    \" Apply\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setSelectedProduct(selectedProduct === item.id ? null : item.id),\n                                                                style: {\n                                                                    padding: \"0.5rem 1rem\",\n                                                                    backgroundColor: \"#007bff\",\n                                                                    color: \"white\",\n                                                                    border: \"none\",\n                                                                    borderRadius: \"6px\",\n                                                                    cursor: \"pointer\",\n                                                                    fontSize: \"0.8rem\"\n                                                                },\n                                                                children: \"\\uD83D\\uDCCA Details\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            selectedProduct && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"2rem\",\n                    backgroundColor: \"#ffffff\",\n                    border: \"1px solid #e1e5e9\",\n                    borderRadius: \"12px\",\n                    padding: \"2rem\"\n                },\n                children: (()=>{\n                    const product = priceData.find((p)=>p.id === selectedProduct);\n                    if (!product) return null;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: \"1.5rem\"\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA Detailed Analysis: \",\n                                    product.product\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"grid\",\n                                    gridTemplateColumns: \"1fr 1fr\",\n                                    gap: \"2rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                style: {\n                                                    marginBottom: \"1rem\"\n                                                },\n                                                children: \"\\uD83C\\uDFEA Competitor Prices\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    gap: \"0.75rem\"\n                                                },\n                                                children: product.competitors.map((competitor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            alignItems: \"center\",\n                                                            padding: \"1rem\",\n                                                            backgroundColor: \"#f8f9fa\",\n                                                            borderRadius: \"8px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontWeight: \"bold\"\n                                                                        },\n                                                                        children: competitor.store\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: \"0.8rem\",\n                                                                            color: \"#666\"\n                                                                        },\n                                                                        children: [\n                                                                            \"Updated \",\n                                                                            competitor.lastUpdated\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontWeight: \"bold\",\n                                                                    fontSize: \"1.1rem\",\n                                                                    color: competitor.price < product.currentPrice ? \"#dc3545\" : competitor.price > product.currentPrice ? \"#28a745\" : \"#007bff\"\n                                                                },\n                                                                children: [\n                                                                    \"$\",\n                                                                    competitor.price.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                style: {\n                                                    marginBottom: \"1rem\"\n                                                },\n                                                children: \"\\uD83D\\uDCC8 Price History\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: \"flex\",\n                                                    flexDirection: \"column\",\n                                                    gap: \"0.75rem\"\n                                                },\n                                                children: product.priceHistory.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            alignItems: \"center\",\n                                                            padding: \"1rem\",\n                                                            backgroundColor: \"#f8f9fa\",\n                                                            borderRadius: \"8px\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontWeight: \"bold\"\n                                                                },\n                                                                children: new Date(entry.date).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontWeight: \"bold\",\n                                                                    fontSize: \"1.1rem\"\n                                                                },\n                                                                children: [\n                                                                    \"$\",\n                                                                    entry.price.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true);\n                })()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin/price-match/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SkipLink auto */ \nfunction SkipLink() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: \"#main\",\n        style: {\n            position: \"absolute\",\n            left: -9999,\n            top: -9999\n        },\n        onFocus: (e)=>{\n            e.currentTarget.style.left = \"8px\";\n            e.currentTarget.style.top = \"8px\";\n            e.currentTarget.style.background = \"white\";\n            e.currentTarget.style.padding = \"8px\";\n        },\n        children: \"Skip to content\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ta2lwTGluay50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUVDLE1BQUs7UUFBUUMsT0FBTztZQUFFQyxVQUFVO1lBQVlDLE1BQU0sQ0FBQztZQUFNQyxLQUFLLENBQUM7UUFBSztRQUFHQyxTQUFTLENBQUNDO1lBQ2pGQSxFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNFLElBQUksR0FBRztZQUNuREcsRUFBRUMsYUFBYSxDQUF1Qk4sS0FBSyxDQUFDRyxHQUFHLEdBQUc7WUFDbERFLEVBQUVDLGFBQWEsQ0FBdUJOLEtBQUssQ0FBQ08sVUFBVSxHQUFHO1lBQ3pERixFQUFFQyxhQUFhLENBQXVCTixLQUFLLENBQUNRLE9BQU8sR0FBRztRQUN6RDtrQkFBRzs7Ozs7O0FBRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbGVjdHJvaHViLW1hcmtldHBsYWNlLy4vc3JjL2NvbXBvbmVudHMvU2tpcExpbmsudHN4Pzk5NTEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5leHBvcnQgZnVuY3Rpb24gU2tpcExpbmsoKSB7XG4gIHJldHVybiAoXG4gICAgPGEgaHJlZj1cIiNtYWluXCIgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGxlZnQ6IC05OTk5LCB0b3A6IC05OTk5IH19IG9uRm9jdXM9eyhlKSA9PiB7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS5sZWZ0ID0gJzhweCc7XG4gICAgICAoZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxBbmNob3JFbGVtZW50KS5zdHlsZS50b3AgPSAnOHB4JztcbiAgICAgIChlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEFuY2hvckVsZW1lbnQpLnN0eWxlLmJhY2tncm91bmQgPSAnd2hpdGUnO1xuICAgICAgKGUuY3VycmVudFRhcmdldCBhcyBIVE1MQW5jaG9yRWxlbWVudCkuc3R5bGUucGFkZGluZyA9ICc4cHgnO1xuICAgIH19PlNraXAgdG8gY29udGVudDwvYT5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTa2lwTGluayIsImEiLCJocmVmIiwic3R5bGUiLCJwb3NpdGlvbiIsImxlZnQiLCJ0b3AiLCJvbkZvY3VzIiwiZSIsImN1cnJlbnRUYXJnZXQiLCJiYWNrZ3JvdW5kIiwicGFkZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SkipLink.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1cc9da9c511c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZWxlY3Ryb2h1Yi1tYXJrZXRwbGFjZS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYzlkYTljNTExY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/admin/price-match/page.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/price-match/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/app/admin/price-match/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_SkipLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SkipLink */ \"(rsc)/./src/components/SkipLink.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ElectroHub — Electronics Marketplace\",\n    description: \"Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.\",\n    metadataBase: new URL(process.env.NEXTAUTH_URL || \"http://localhost:3000\"),\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SkipLink__WEBPACK_IMPORTED_MODULE_3__.SkipLink, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    \"aria-label\": \"Site header\",\n                    style: {\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        \"aria-label\": \"Main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"ElectroHub\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            \" | \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/cart\",\n                                children: \"Cart\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 48\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    id: \"main\",\n                    style: {\n                        minHeight: \"60vh\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    style: {\n                        padding: \"2rem\",\n                        borderTop: \"1px solid #eee\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" ElectroHub \\xb7 \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 60\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SkipLink.tsx":
/*!*************************************!*\
  !*** ./src/components/SkipLink.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SkipLink: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/ElectroHub/src/components/SkipLink.tsx#SkipLink`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fprice-match%2Fpage&page=%2Fadmin%2Fprice-match%2Fpage&appPaths=%2Fadmin%2Fprice-match%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fprice-match%2Fpage.tsx&appDir=%2FUsers%2FApple%2FDesktop%2FElectroHub%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FApple%2FDesktop%2FElectroHub&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();