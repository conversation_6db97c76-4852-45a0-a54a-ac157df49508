"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PriceMatchBanner.tsx":
/*!*********************************************!*\
  !*** ./src/components/PriceMatchBanner.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PriceMatchBanner; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PriceMatchBanner() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalComparisons: 0,\n        averageSavings: 0,\n        bestPricePercentage: 0,\n        lastUpdated: new Date()\n    });\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Simulate real-time stats\n        const updateStats = ()=>{\n            setStats({\n                totalComparisons: Math.floor(Math.random() * 1000) + 15000,\n                averageSavings: Math.floor(Math.random() * 50) + 25,\n                bestPricePercentage: Math.floor(Math.random() * 20) + 75,\n                lastUpdated: new Date()\n            });\n        };\n        updateStats();\n        const interval = setInterval(updateStats, 30000); // Update every 30 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\",\n            background: \"linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)\",\n            color: \"white\",\n            padding: \"3rem 1rem\",\n            overflow: \"hidden\"\n        },\n        className: \"jsx-d2923c391209b29a\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    inset: 0,\n                    background: \"conic-gradient(from 0deg at 50% 50%, rgba(255,255,255,0.1), transparent, rgba(255,255,255,0.1), transparent)\",\n                    animation: \"rotate 30s linear infinite\",\n                    pointerEvents: \"none\"\n                },\n                className: \"jsx-d2923c391209b29a\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10%\",\n                    left: \"5%\",\n                    width: \"100px\",\n                    height: \"100px\",\n                    background: \"rgba(255, 255, 255, 0.1)\",\n                    borderRadius: \"50%\",\n                    animation: \"float 20s ease-in-out infinite\",\n                    pointerEvents: \"none\"\n                },\n                className: \"jsx-d2923c391209b29a\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"60%\",\n                    right: \"10%\",\n                    width: \"80px\",\n                    height: \"80px\",\n                    background: \"rgba(255, 255, 255, 0.08)\",\n                    borderRadius: \"20px\",\n                    transform: \"rotate(45deg)\",\n                    animation: \"float 25s ease-in-out infinite reverse\",\n                    pointerEvents: \"none\"\n                },\n                className: \"jsx-d2923c391209b29a\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"-50%\",\n                    left: \"-10%\",\n                    width: \"120%\",\n                    height: \"200%\",\n                    background: \"radial-gradient(circle, rgba(255,255,255,0.15) 2px, transparent 2px)\",\n                    backgroundSize: \"60px 60px\",\n                    animation: \"float 40s infinite linear\",\n                    pointerEvents: \"none\",\n                    opacity: 0.6\n                },\n                className: \"jsx-d2923c391209b29a\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsVisible(false),\n                style: {\n                    position: \"absolute\",\n                    top: \"2rem\",\n                    right: \"2rem\",\n                    background: \"rgba(0, 0, 0, 0.3)\",\n                    backdropFilter: \"blur(20px)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                    color: \"white\",\n                    borderRadius: \"50%\",\n                    width: \"50px\",\n                    height: \"50px\",\n                    cursor: \"pointer\",\n                    fontSize: \"1.5rem\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    transition: \"all 0.4s cubic-bezier(0.4, 0, 0.2, 1)\",\n                    zIndex: 20,\n                    boxShadow: \"0 8px 25px rgba(0, 0, 0, 0.3)\"\n                },\n                onMouseEnter: (e)=>{\n                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.2)\";\n                    e.currentTarget.style.transform = \"scale(1.15) rotate(90deg)\";\n                    e.currentTarget.style.boxShadow = \"0 12px 35px rgba(0, 0, 0, 0.4)\";\n                },\n                onMouseLeave: (e)=>{\n                    e.currentTarget.style.background = \"rgba(0, 0, 0, 0.3)\";\n                    e.currentTarget.style.transform = \"scale(1) rotate(0deg)\";\n                    e.currentTarget.style.boxShadow = \"0 8px 25px rgba(0, 0, 0, 0.3)\";\n                },\n                className: \"jsx-d2923c391209b29a\",\n                children: \"\\xd7\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"1200px\",\n                    margin: \"0 auto\",\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n                    gap: \"2rem\",\n                    alignItems: \"center\",\n                    position: \"relative\",\n                    zIndex: 1\n                },\n                className: \"jsx-d2923c391209b29a\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-d2923c391209b29a\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"1rem\",\n                                    marginBottom: \"1rem\"\n                                },\n                                className: \"jsx-d2923c391209b29a\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"rgba(255, 255, 255, 0.15)\",\n                                            backdropFilter: \"blur(10px)\",\n                                            borderRadius: \"16px\",\n                                            padding: \"12px\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                                        },\n                                        className: \"jsx-d2923c391209b29a\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"2rem\"\n                                            },\n                                            className: \"jsx-d2923c391209b29a\",\n                                            children: \"\\uD83C\\uDFC6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"2rem\",\n                                            fontWeight: \"800\",\n                                            textShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        className: \"jsx-d2923c391209b29a\",\n                                        children: \"Real-Time Price Intelligence\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    margin: 0,\n                                    opacity: 0.95,\n                                    fontSize: \"1.1rem\",\n                                    lineHeight: \"1.6\",\n                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                },\n                                className: \"jsx-d2923c391209b29a\",\n                                children: \"AI-powered monitoring across 10+ premium retailers ensures you always get the absolute best deals available\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(3, 1fr)\",\n                            gap: \"1.5rem\"\n                        },\n                        className: \"jsx-d2923c391209b29a\",\n                        children: [\n                            {\n                                value: \"\".concat(stats.totalComparisons.toLocaleString(), \"+\"),\n                                label: \"Daily Price Checks\",\n                                icon: \"\\uD83D\\uDD0D\"\n                            },\n                            {\n                                value: \"$\".concat(stats.averageSavings),\n                                label: \"Average Savings\",\n                                icon: \"\\uD83D\\uDCB0\"\n                            },\n                            {\n                                value: \"\".concat(stats.bestPricePercentage, \"%\"),\n                                label: \"Best Price Match\",\n                                icon: \"\\uD83C\\uDFAF\"\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255, 255, 255, 0.1)\",\n                                    backdropFilter: \"blur(10px)\",\n                                    borderRadius: \"16px\",\n                                    padding: \"1.5rem\",\n                                    textAlign: \"center\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    transition: \"transform 0.3s ease\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.transform = \"translateY(-4px)\";\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.1)\";\n                                },\n                                className: \"jsx-d2923c391209b29a\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"1.5rem\",\n                                            marginBottom: \"0.5rem\"\n                                        },\n                                        className: \"jsx-d2923c391209b29a\",\n                                        children: stat.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"2.2rem\",\n                                            fontWeight: \"800\",\n                                            marginBottom: \"0.5rem\",\n                                            textShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        className: \"jsx-d2923c391209b29a\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            opacity: 0.9,\n                                            fontWeight: \"500\"\n                                        },\n                                        className: \"jsx-d2923c391209b29a\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            background: \"rgba(255, 255, 255, 0.1)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"20px\",\n                            padding: \"2rem\",\n                            border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                        },\n                        className: \"jsx-d2923c391209b29a\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255, 255, 255, 0.15)\",\n                                    borderRadius: \"16px\",\n                                    padding: \"12px\",\n                                    display: \"inline-flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    marginBottom: \"1rem\"\n                                },\n                                className: \"jsx-d2923c391209b29a\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"2rem\"\n                                    },\n                                    className: \"jsx-d2923c391209b29a\",\n                                    children: \"\\uD83D\\uDCA1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"1rem\",\n                                    fontWeight: \"800\",\n                                    fontSize: \"1.3rem\",\n                                    textShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                                },\n                                className: \"jsx-d2923c391209b29a\",\n                                children: \"100% Price Match Guarantee\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1rem\",\n                                    opacity: 0.95,\n                                    lineHeight: \"1.5\",\n                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                },\n                                className: \"jsx-d2923c391209b29a\",\n                                children: \"Found a lower price? We'll match it instantly and give you 10% of the difference as store credit!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    bottom: \"0.25rem\",\n                    right: \"0.5rem\",\n                    fontSize: \"0.7rem\",\n                    opacity: 0.7\n                },\n                className: \"jsx-d2923c391209b29a\",\n                children: [\n                    \"\\uD83D\\uDD04 Updated \",\n                    stats.lastUpdated.toLocaleTimeString()\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"-50%\",\n                    left: \"-10%\",\n                    width: \"120%\",\n                    height: \"200%\",\n                    background: \"radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)\",\n                    backgroundSize: \"20px 20px\",\n                    animation: \"float 20s infinite linear\",\n                    pointerEvents: \"none\"\n                },\n                className: \"jsx-d2923c391209b29a\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"d2923c391209b29a\",\n                children: \"@-webkit-keyframes rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes rotate{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes rotate{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes rotate{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.6}25%{-webkit-transform:translatey(-20px)rotate(90deg);transform:translatey(-20px)rotate(90deg);opacity:.8}50%{-webkit-transform:translatey(-40px)rotate(180deg);transform:translatey(-40px)rotate(180deg);opacity:1}75%{-webkit-transform:translatey(-20px)rotate(270deg);transform:translatey(-20px)rotate(270deg);opacity:.8}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.6}25%{-moz-transform:translatey(-20px)rotate(90deg);transform:translatey(-20px)rotate(90deg);opacity:.8}50%{-moz-transform:translatey(-40px)rotate(180deg);transform:translatey(-40px)rotate(180deg);opacity:1}75%{-moz-transform:translatey(-20px)rotate(270deg);transform:translatey(-20px)rotate(270deg);opacity:.8}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.6}25%{-o-transform:translatey(-20px)rotate(90deg);transform:translatey(-20px)rotate(90deg);opacity:.8}50%{-o-transform:translatey(-40px)rotate(180deg);transform:translatey(-40px)rotate(180deg);opacity:1}75%{-o-transform:translatey(-20px)rotate(270deg);transform:translatey(-20px)rotate(270deg);opacity:.8}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg);opacity:.6}25%{-webkit-transform:translatey(-20px)rotate(90deg);-moz-transform:translatey(-20px)rotate(90deg);-o-transform:translatey(-20px)rotate(90deg);transform:translatey(-20px)rotate(90deg);opacity:.8}50%{-webkit-transform:translatey(-40px)rotate(180deg);-moz-transform:translatey(-40px)rotate(180deg);-o-transform:translatey(-40px)rotate(180deg);transform:translatey(-40px)rotate(180deg);opacity:1}75%{-webkit-transform:translatey(-20px)rotate(270deg);-moz-transform:translatey(-20px)rotate(270deg);-o-transform:translatey(-20px)rotate(270deg);transform:translatey(-20px)rotate(270deg);opacity:.8}}@-webkit-keyframes pulse{0%,100%{opacity:1;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:.8;-webkit-transform:scale(1.05);transform:scale(1.05)}}@-moz-keyframes pulse{0%,100%{opacity:1;-moz-transform:scale(1);transform:scale(1)}50%{opacity:.8;-moz-transform:scale(1.05);transform:scale(1.05)}}@-o-keyframes pulse{0%,100%{opacity:1;-o-transform:scale(1);transform:scale(1)}50%{opacity:.8;-o-transform:scale(1.05);transform:scale(1.05)}}@keyframes pulse{0%,100%{opacity:1;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:.8;-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05)}}@-webkit-keyframes glow{0%,100%{-webkit-box-shadow:0 0 20px rgba(255,255,255,.3);box-shadow:0 0 20px rgba(255,255,255,.3)}50%{-webkit-box-shadow:0 0 40px rgba(255,255,255,.6);box-shadow:0 0 40px rgba(255,255,255,.6)}}@-moz-keyframes glow{0%,100%{-moz-box-shadow:0 0 20px rgba(255,255,255,.3);box-shadow:0 0 20px rgba(255,255,255,.3)}50%{-moz-box-shadow:0 0 40px rgba(255,255,255,.6);box-shadow:0 0 40px rgba(255,255,255,.6)}}@-o-keyframes glow{0%,100%{box-shadow:0 0 20px rgba(255,255,255,.3)}50%{box-shadow:0 0 40px rgba(255,255,255,.6)}}@keyframes glow{0%,100%{-webkit-box-shadow:0 0 20px rgba(255,255,255,.3);-moz-box-shadow:0 0 20px rgba(255,255,255,.3);box-shadow:0 0 20px rgba(255,255,255,.3)}50%{-webkit-box-shadow:0 0 40px rgba(255,255,255,.6);-moz-box-shadow:0 0 40px rgba(255,255,255,.6);box-shadow:0 0 40px rgba(255,255,255,.6)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(PriceMatchBanner, \"IUR9yXxwbCiNlwvfry/qEh26wmI=\");\n_c = PriceMatchBanner;\nvar _c;\n$RefreshReg$(_c, \"PriceMatchBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PriceMatchBanner.tsx\n"));

/***/ })

});