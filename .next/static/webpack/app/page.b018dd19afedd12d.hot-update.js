"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PriceComparisonBadge.tsx":
/*!*************************************************!*\
  !*** ./src/components/PriceComparisonBadge.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PriceComparisonBadge; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PriceComparisonBadge(param) {\n    let { productId, ourPrice, compact = false } = param;\n    _s();\n    const [competitorPrices, setCompetitorPrices] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Mock competitor prices - in real app, this would come from API\n    const mockPrices = {\n        \"iphone-15-pro-max\": [\n            1189.99,\n            1199.00,\n            1199.99,\n            1179.99,\n            1199.99,\n            1209.99\n        ],\n        \"macbook-air-m3\": [\n            1299.99,\n            1299.99,\n            1299.99,\n            1279.99\n        ],\n        \"sony-wh-1000xm5\": [\n            379.99,\n            399.99,\n            399.99,\n            389.99\n        ],\n        \"samsung-galaxy-s24-ultra\": [\n            1289.99,\n            1299.99,\n            1309.99,\n            1279.99\n        ],\n        \"airpods-pro-2\": [\n            239.99,\n            249.99,\n            249.99,\n            244.99\n        ],\n        \"dell-xps-13-plus\": [\n            1389.99,\n            1399.99,\n            1409.99\n        ],\n        \"nintendo-switch-oled\": [\n            349.99,\n            349.99,\n            359.99,\n            339.99\n        ],\n        \"ipad-pro-12-9-m2\": [\n            1089.99,\n            1099.99,\n            1109.99,\n            1079.99\n        ],\n        \"google-pixel-8-pro\": [\n            989.99,\n            999.99,\n            1009.99,\n            979.99\n        ],\n        \"surface-laptop-5\": [\n            1289.99,\n            1299.99,\n            1309.99\n        ]\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchPrices = async ()=>{\n            setIsLoading(true);\n            // Simulate API delay\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            const prices = mockPrices[productId] || [];\n            setCompetitorPrices(prices);\n            setIsLoading(false);\n        };\n        fetchPrices();\n    }, [\n        productId\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: \"0.5rem\",\n                padding: compact ? \"0.25rem 0.5rem\" : \"0.5rem 0.75rem\",\n                backgroundColor: \"#f8f9fa\",\n                borderRadius: \"6px\",\n                fontSize: compact ? \"0.8rem\" : \"0.9rem\"\n            },\n            className: \"jsx-d525c98fb358669b\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        width: \"12px\",\n                        height: \"12px\",\n                        border: \"2px solid #007bff\",\n                        borderTop: \"2px solid transparent\",\n                        borderRadius: \"50%\",\n                        animation: \"spin 1s linear infinite\"\n                    },\n                    className: \"jsx-d525c98fb358669b\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        color: \"#666\"\n                    },\n                    className: \"jsx-d525c98fb358669b\",\n                    children: \"Checking prices...\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"d525c98fb358669b\",\n                    children: \"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}\"\n                }, void 0, false, void 0, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    if (competitorPrices.length === 0) {\n        return null;\n    }\n    const lowestPrice = Math.min(...competitorPrices);\n    const highestPrice = Math.max(...competitorPrices);\n    const averagePrice = competitorPrices.reduce((sum, price)=>sum + price, 0) / competitorPrices.length;\n    const savings = Math.max(0, highestPrice - ourPrice);\n    const isBestPrice = ourPrice <= lowestPrice;\n    const isGoodDeal = ourPrice <= averagePrice;\n    if (compact) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                gap: \"0.75rem\"\n            },\n            children: [\n                isBestPrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"0.5rem\",\n                        padding: \"0.5rem 1rem\",\n                        background: \"linear-gradient(135deg, #10b981 0%, #059669 100%)\",\n                        color: \"white\",\n                        borderRadius: \"16px\",\n                        fontWeight: \"600\",\n                        fontSize: \"0.85rem\",\n                        boxShadow: \"0 2px 8px rgba(16, 185, 129, 0.3)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"1rem\"\n                            },\n                            children: \"\\uD83C\\uDFC6\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        \"Best Price\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this) : isGoodDeal ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"0.5rem\",\n                        padding: \"0.5rem 1rem\",\n                        background: \"linear-gradient(135deg, #f59e0b 0%, #d97706 100%)\",\n                        color: \"white\",\n                        borderRadius: \"16px\",\n                        fontWeight: \"600\",\n                        fontSize: \"0.85rem\",\n                        boxShadow: \"0 2px 8px rgba(245, 158, 11, 0.3)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"1rem\"\n                            },\n                            children: \"\\uD83D\\uDCB0\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        \"Great Deal\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"0.5rem\",\n                        padding: \"0.5rem 1rem\",\n                        background: \"linear-gradient(135deg, #6b7280 0%, #4b5563 100%)\",\n                        color: \"white\",\n                        borderRadius: \"16px\",\n                        fontWeight: \"600\",\n                        fontSize: \"0.85rem\",\n                        boxShadow: \"0 2px 8px rgba(107, 114, 128, 0.3)\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: \"1rem\"\n                            },\n                            children: \"\\uD83D\\uDCCA\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        \"Compared\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: \"0.8rem\",\n                        color: \"#64748b\",\n                        fontWeight: \"500\"\n                    },\n                    children: [\n                        \"vs \",\n                        competitorPrices.length,\n                        \" stores\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"0.75rem\",\n            backgroundColor: isBestPrice ? \"#d4edda\" : isGoodDeal ? \"#fff3cd\" : \"#f8f9fa\",\n            border: \"1px solid \".concat(isBestPrice ? \"#c3e6cb\" : isGoodDeal ? \"#ffeaa7\" : \"#e1e5e9\"),\n            borderRadius: \"8px\",\n            marginTop: \"0.5rem\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    marginBottom: \"0.5rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: \"0.5rem\",\n                            fontWeight: \"bold\",\n                            color: isBestPrice ? \"#155724\" : isGoodDeal ? \"#856404\" : \"#333\"\n                        },\n                        children: isBestPrice ? \"\\uD83C\\uDFC6 BEST PRICE\" : isGoodDeal ? \"\\uD83D\\uDCB0 GREAT DEAL\" : \"\\uD83D\\uDCCA PRICE CHECKED\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"0.8rem\",\n                            color: \"#666\"\n                        },\n                        children: [\n                            \"vs \",\n                            competitorPrices.length,\n                            \" retailers\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(80px, 1fr))\",\n                    gap: \"0.5rem\",\n                    fontSize: \"0.8rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: \"#666\"\n                                },\n                                children: \"Lowest\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: \"bold\",\n                                    color: ourPrice <= lowestPrice ? \"#28a745\" : \"#dc3545\"\n                                },\n                                children: [\n                                    \"$\",\n                                    lowestPrice.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: \"#666\"\n                                },\n                                children: \"Average\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: \"bold\",\n                                    color: ourPrice <= averagePrice ? \"#28a745\" : \"#ffc107\"\n                                },\n                                children: [\n                                    \"$\",\n                                    averagePrice.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: \"#666\"\n                                },\n                                children: \"Highest\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: \"bold\",\n                                    color: \"#666\"\n                                },\n                                children: [\n                                    \"$\",\n                                    highestPrice.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    savings > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: \"#666\"\n                                },\n                                children: \"You Save\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontWeight: \"bold\",\n                                    color: \"#28a745\"\n                                },\n                                children: [\n                                    \"$\",\n                                    savings.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            isBestPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"0.5rem\",\n                    fontSize: \"0.8rem\",\n                    color: \"#155724\",\n                    fontWeight: \"bold\"\n                },\n                children: \"✅ Price Match Guaranteed\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceComparisonBadge.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(PriceComparisonBadge, \"62aSoDWu4+FQf/4FzL9VKgD+91s=\");\n_c = PriceComparisonBadge;\nvar _c;\n$RefreshReg$(_c, \"PriceComparisonBadge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PriceComparisonBadge.tsx\n"));

/***/ })

});