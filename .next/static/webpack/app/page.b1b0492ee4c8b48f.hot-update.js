"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PriceMatchBanner.tsx":
/*!*********************************************!*\
  !*** ./src/components/PriceMatchBanner.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PriceMatchBanner; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PriceMatchBanner() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalComparisons: 0,\n        averageSavings: 0,\n        bestPricePercentage: 0,\n        lastUpdated: new Date()\n    });\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Simulate real-time stats\n        const updateStats = ()=>{\n            setStats({\n                totalComparisons: Math.floor(Math.random() * 1000) + 15000,\n                averageSavings: Math.floor(Math.random() * 50) + 25,\n                bestPricePercentage: Math.floor(Math.random() * 20) + 75,\n                lastUpdated: new Date()\n            });\n        };\n        updateStats();\n        const interval = setInterval(updateStats, 30000); // Update every 30 seconds\n        return ()=>clearInterval(interval);\n    }, []);\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\",\n            background: \"linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)\",\n            color: \"white\",\n            padding: \"3rem 1rem\",\n            overflow: \"hidden\"\n        },\n        className: \"jsx-de60d29569a91761\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    inset: 0,\n                    background: \"conic-gradient(from 0deg at 50% 50%, rgba(255,255,255,0.1), transparent, rgba(255,255,255,0.1), transparent)\",\n                    animation: \"rotate 30s linear infinite\",\n                    pointerEvents: \"none\"\n                },\n                className: \"jsx-de60d29569a91761\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10%\",\n                    left: \"5%\",\n                    width: \"100px\",\n                    height: \"100px\",\n                    background: \"rgba(255, 255, 255, 0.1)\",\n                    borderRadius: \"50%\",\n                    animation: \"float 20s ease-in-out infinite\",\n                    pointerEvents: \"none\"\n                },\n                className: \"jsx-de60d29569a91761\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"60%\",\n                    right: \"10%\",\n                    width: \"80px\",\n                    height: \"80px\",\n                    background: \"rgba(255, 255, 255, 0.08)\",\n                    borderRadius: \"20px\",\n                    transform: \"rotate(45deg)\",\n                    animation: \"float 25s ease-in-out infinite reverse\",\n                    pointerEvents: \"none\"\n                },\n                className: \"jsx-de60d29569a91761\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"-50%\",\n                    left: \"-10%\",\n                    width: \"120%\",\n                    height: \"200%\",\n                    background: \"radial-gradient(circle, rgba(255,255,255,0.15) 2px, transparent 2px)\",\n                    backgroundSize: \"60px 60px\",\n                    animation: \"float 40s infinite linear\",\n                    pointerEvents: \"none\",\n                    opacity: 0.6\n                },\n                className: \"jsx-de60d29569a91761\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsVisible(false),\n                style: {\n                    position: \"absolute\",\n                    top: \"1rem\",\n                    right: \"1rem\",\n                    background: \"rgba(255, 255, 255, 0.15)\",\n                    backdropFilter: \"blur(10px)\",\n                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                    color: \"white\",\n                    borderRadius: \"50%\",\n                    width: \"40px\",\n                    height: \"40px\",\n                    cursor: \"pointer\",\n                    fontSize: \"1.2rem\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    transition: \"all 0.3s ease\",\n                    zIndex: 10\n                },\n                onMouseEnter: (e)=>{\n                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.25)\";\n                    e.currentTarget.style.transform = \"scale(1.1)\";\n                },\n                onMouseLeave: (e)=>{\n                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                    e.currentTarget.style.transform = \"scale(1)\";\n                },\n                className: \"jsx-de60d29569a91761\",\n                children: \"\\xd7\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"1200px\",\n                    margin: \"0 auto\",\n                    display: \"grid\",\n                    gridTemplateColumns: \"repeat(auto-fit, minmax(280px, 1fr))\",\n                    gap: \"2rem\",\n                    alignItems: \"center\",\n                    position: \"relative\",\n                    zIndex: 1\n                },\n                className: \"jsx-de60d29569a91761\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-de60d29569a91761\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"1rem\",\n                                    marginBottom: \"1rem\"\n                                },\n                                className: \"jsx-de60d29569a91761\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: \"rgba(255, 255, 255, 0.15)\",\n                                            backdropFilter: \"blur(10px)\",\n                                            borderRadius: \"16px\",\n                                            padding: \"12px\",\n                                            border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                                        },\n                                        className: \"jsx-de60d29569a91761\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"2rem\"\n                                            },\n                                            className: \"jsx-de60d29569a91761\",\n                                            children: \"\\uD83C\\uDFC6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            margin: 0,\n                                            fontSize: \"2rem\",\n                                            fontWeight: \"800\",\n                                            textShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        className: \"jsx-de60d29569a91761\",\n                                        children: \"Real-Time Price Intelligence\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    margin: 0,\n                                    opacity: 0.95,\n                                    fontSize: \"1.1rem\",\n                                    lineHeight: \"1.6\",\n                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                },\n                                className: \"jsx-de60d29569a91761\",\n                                children: \"AI-powered monitoring across 10+ premium retailers ensures you always get the absolute best deals available\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(3, 1fr)\",\n                            gap: \"1.5rem\"\n                        },\n                        className: \"jsx-de60d29569a91761\",\n                        children: [\n                            {\n                                value: \"\".concat(stats.totalComparisons.toLocaleString(), \"+\"),\n                                label: \"Daily Price Checks\",\n                                icon: \"\\uD83D\\uDD0D\"\n                            },\n                            {\n                                value: \"$\".concat(stats.averageSavings),\n                                label: \"Average Savings\",\n                                icon: \"\\uD83D\\uDCB0\"\n                            },\n                            {\n                                value: \"\".concat(stats.bestPricePercentage, \"%\"),\n                                label: \"Best Price Match\",\n                                icon: \"\\uD83C\\uDFAF\"\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255, 255, 255, 0.1)\",\n                                    backdropFilter: \"blur(10px)\",\n                                    borderRadius: \"16px\",\n                                    padding: \"1.5rem\",\n                                    textAlign: \"center\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    transition: \"transform 0.3s ease\"\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.transform = \"translateY(-4px)\";\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.15)\";\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.transform = \"translateY(0)\";\n                                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.1)\";\n                                },\n                                className: \"jsx-de60d29569a91761\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"1.5rem\",\n                                            marginBottom: \"0.5rem\"\n                                        },\n                                        className: \"jsx-de60d29569a91761\",\n                                        children: stat.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"2.2rem\",\n                                            fontWeight: \"800\",\n                                            marginBottom: \"0.5rem\",\n                                            textShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                                        },\n                                        className: \"jsx-de60d29569a91761\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            opacity: 0.9,\n                                            fontWeight: \"500\"\n                                        },\n                                        className: \"jsx-de60d29569a91761\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            background: \"rgba(255, 255, 255, 0.1)\",\n                            backdropFilter: \"blur(10px)\",\n                            borderRadius: \"20px\",\n                            padding: \"2rem\",\n                            border: \"1px solid rgba(255, 255, 255, 0.2)\"\n                        },\n                        className: \"jsx-de60d29569a91761\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: \"rgba(255, 255, 255, 0.15)\",\n                                    borderRadius: \"16px\",\n                                    padding: \"12px\",\n                                    display: \"inline-flex\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    marginBottom: \"1rem\"\n                                },\n                                className: \"jsx-de60d29569a91761\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: \"2rem\"\n                                    },\n                                    className: \"jsx-de60d29569a91761\",\n                                    children: \"\\uD83D\\uDCA1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: \"1rem\",\n                                    fontWeight: \"800\",\n                                    fontSize: \"1.3rem\",\n                                    textShadow: \"0 2px 4px rgba(0, 0, 0, 0.1)\"\n                                },\n                                className: \"jsx-de60d29569a91761\",\n                                children: \"100% Price Match Guarantee\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: \"1rem\",\n                                    opacity: 0.95,\n                                    lineHeight: \"1.5\",\n                                    textShadow: \"0 1px 2px rgba(0, 0, 0, 0.1)\"\n                                },\n                                className: \"jsx-de60d29569a91761\",\n                                children: \"Found a lower price? We'll match it instantly and give you 10% of the difference as store credit!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    bottom: \"0.25rem\",\n                    right: \"0.5rem\",\n                    fontSize: \"0.7rem\",\n                    opacity: 0.7\n                },\n                className: \"jsx-de60d29569a91761\",\n                children: [\n                    \"\\uD83D\\uDD04 Updated \",\n                    stats.lastUpdated.toLocaleTimeString()\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"-50%\",\n                    left: \"-10%\",\n                    width: \"120%\",\n                    height: \"200%\",\n                    background: \"radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)\",\n                    backgroundSize: \"20px 20px\",\n                    animation: \"float 20s infinite linear\",\n                    pointerEvents: \"none\"\n                },\n                className: \"jsx-de60d29569a91761\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"de60d29569a91761\",\n                children: \"@-webkit-keyframes float{0%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}100%{-webkit-transform:translatey(-20px)rotate(360deg);transform:translatey(-20px)rotate(360deg)}}@-moz-keyframes float{0%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}100%{-moz-transform:translatey(-20px)rotate(360deg);transform:translatey(-20px)rotate(360deg)}}@-o-keyframes float{0%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}100%{-o-transform:translatey(-20px)rotate(360deg);transform:translatey(-20px)rotate(360deg)}}@keyframes float{0%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}100%{-webkit-transform:translatey(-20px)rotate(360deg);-moz-transform:translatey(-20px)rotate(360deg);-o-transform:translatey(-20px)rotate(360deg);transform:translatey(-20px)rotate(360deg)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/ElectroHub/src/components/PriceMatchBanner.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(PriceMatchBanner, \"IUR9yXxwbCiNlwvfry/qEh26wmI=\");\n_c = PriceMatchBanner;\nvar _c;\n$RefreshReg$(_c, \"PriceMatchBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PriceMatchBanner.tsx\n"));

/***/ })

});