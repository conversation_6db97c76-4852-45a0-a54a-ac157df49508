# ElectroHub — <PERSON>+

## Quick Start
1. Install deps: `npm ci`
2. Create DB + env: copy `.env.example` to `.env` and set `DATABASE_URL` and `NEXTAUTH_SECRET`.
3. Generate client: `npm run db:generate`
4. Migrate + seed: `npm run db:migrate && npm run db:seed`
5. Run: `npm run dev` → http://localhost:3000

## Stripe (test)
- Set `STRIPE_SECRET_KEY` and `STRIPE_WEBHOOK_SECRET`.
- Start webhook: `stripe listen --events checkout.session.completed --forward-to localhost:3000/api/webhooks/stripe`

## Notes
- Replace in-memory rate limiter with <PERSON><PERSON> in prod.
- Configure S3 + CDN for media uploads.
