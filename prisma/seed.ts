import { prisma } from '../src/lib/prisma';

async function main() {
  const brands = await prisma.$transaction([
    prisma.brand.upsert({ where: { slug: 'apple' }, update: {}, create: { name: 'Apple', slug: 'apple' } }),
    prisma.brand.upsert({ where: { slug: 'samsung' }, update: {}, create: { name: 'Samsung', slug: 'samsung' } }),
    prisma.brand.upsert({ where: { slug: 'sony' }, update: {}, create: { name: 'Sony', slug: 'sony' } })
  ]);

  const cats = await prisma.$transaction([
    prisma.category.upsert({ where: { slug: 'phones' }, update: {}, create: { name: 'Phones', slug: 'phones' } }),
    prisma.category.upsert({ where: { slug: 'laptops' }, update: {}, create: { name: 'Laptops', slug: 'laptops' } }),
    prisma.category.upsert({ where: { slug: 'audio' }, update: {}, create: { name: 'Audio', slug: 'audio' } }),
    prisma.category.upsert({ where: { slug: 'gaming' }, update: {}, create: { name: 'Gaming', slug: 'gaming' } }),
    prisma.category.upsert({ where: { slug: 'accessories' }, update: {}, create: { name: 'Accessories', slug: 'accessories' } })
  ]);

  const vendor = await prisma.vendor.upsert({ where: { slug: 'tech-galaxy' }, update: {}, create: { name: 'Tech Galaxy', slug: 'tech-galaxy', contactEmail: '<EMAIL>' } });

  const iphone = await prisma.product.create({
    data: {
      title: 'iPhone 15 Pro 128GB',
      slug: 'iphone-15-pro-128',
      description: 'Flagship smartphone with A17 Pro, 128GB storage.',
      status: 'PUBLISHED',
      vendorId: vendor.id,
      brandId: brands[0].id,
      categoryId: cats[0].id,
      variants: { create: { sku: 'IP15P-128-GR', attributes: { color: 'Graphite', storage: '128GB' }, price: 999.0, currency: 'USD', inventory: { create: { stock: 10 } } } },
      media: { create: { url: 'https://via.placeholder.com/800x600?text=iPhone+15+Pro', alt: 'iPhone 15 Pro', type: 'image/jpeg', position: 0 } }
    }
  });

  const laptop = await prisma.product.create({
    data: {
      title: 'Samsung Galaxy Book3 Pro',
      slug: 'galaxy-book3-pro',
      description: 'Ultra-thin laptop, AMOLED display.',
      status: 'PUBLISHED', vendorId: vendor.id, brandId: brands[1].id, categoryId: cats[1].id,
      variants: { create: { sku: 'GB3P-16-512', attributes: { ram: '16GB', storage: '512GB' }, price: 1499.0, currency: 'USD', inventory: { create: { stock: 5 } } } },
      media: { create: { url: 'https://via.placeholder.com/800x600?text=Galaxy+Book3+Pro', alt: 'Galaxy Book3 Pro', type: 'image/jpeg', position: 0 } }
    }
  });

  const headphones = await prisma.product.create({
    data: {
      title: 'Sony WH‑1000XM5', slug: 'sony-wh-1000xm5', description: 'Industry-leading noise cancelling headphones.', status: 'PUBLISHED', vendorId: vendor.id, brandId: brands[2].id, categoryId: cats[2].id,
      variants: { create: { sku: 'SONY-XM5-BLK', attributes: { color: 'Black' }, price: 399.0, currency: 'USD', inventory: { create: { stock: 20 } } } },
      media: { create: { url: 'https://via.placeholder.com/800x600?text=Sony+XM5', alt: 'Sony WH-1000XM5', type: 'image/jpeg', position: 0 } }
    }
  });

  console.log({ iphone: iphone.slug, laptop: laptop.slug, headphones: headphones.slug });
}

main().catch((e) => { console.error(e); process.exit(1); }).finally(async () => { await prisma.$disconnect(); });
