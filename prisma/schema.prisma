generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole { BUYER VENDOR ADMIN }
enum ProductStatus { DRAFT PUBLISHED ARCHIVED }
enum Condition { NEW LIKE_NEW REFURBISHED USED }
enum OrderStatus { PLACED PAID PACKED SHIPPED DELIVERED CANCELLED }
enum PaymentStatus { INITIATED SUCCEEDED FAILED REFUNDED PARTIALLY_REFUNDED }
enum Provider { STRIPE PAYPAL }
enum KycStatus { NONE PENDING VERIFIED }
enum ReviewStatus { PENDING APPROVED REJECTED }

model User {
  id        String   @id @default(cuid())
  name      String?
  email     String?  @unique
  image     String?
  role      UserRole @default(BUYER)
  vendor    Vendor?  @relation(fields: [vendorId], references: [id])
  vendorId  String?
  addresses Address[]
  carts     Cart[]
  orders    Order[]
  reviews   Review[]
  tickets   SupportTicket[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  accounts  Account[]
  sessions  Session[]
}

model Vendor {
  id           String   @id @default(cuid())
  name         String
  slug         String   @unique
  about        String?
  contactEmail String?
  kycStatus    KycStatus @default(NONE)
  stripeAccountId String?
  paypalPayoutId  String?
  commissionPercent Int @default(10)
  products     Product[]
  orders       OrderItem[]
  payouts      Payout[]
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model Brand {
  id    String @id @default(cuid())
  name  String @unique
  slug  String @unique
  products Product[]
}

model Category {
  id       String   @id @default(cuid())
  name     String
  slug     String   @unique
  parent   Category? @relation("CategoryToCategory", fields: [parentId], references: [id])
  parentId String?
  children Category[] @relation("CategoryToCategory")
  products Product[]
}

model Product {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  description String
  status      ProductStatus @default(DRAFT)
  vendor      Vendor   @relation(fields: [vendorId], references: [id])
  vendorId    String
  brand       Brand?   @relation(fields: [brandId], references: [id])
  brandId     String?
  category    Category @relation(fields: [categoryId], references: [id])
  categoryId  String
  variants    ProductVariant[]
  media       Media[]
  reviews     Review[]
  questions   Question[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model ProductVariant {
  id        String   @id @default(cuid())
  product   Product  @relation(fields: [productId], references: [id])
  productId String
  sku       String   @unique
  attributes Json
  condition Condition @default(NEW)
  price     Decimal  @db.Decimal(10,2)
  currency  String   @default("USD")
  inventory Inventory?
  orderItems OrderItem[]
}

model Inventory {
  id         String @id @default(cuid())
  variant    ProductVariant @relation(fields: [variantId], references: [id])
  variantId  String @unique
  stock      Int    @default(0)
  reserved   Int    @default(0)
}

model Media {
  id        String  @id @default(cuid())
  product   Product @relation(fields: [productId], references: [id])
  productId String
  url       String
  alt       String
  type      String
  position  Int     @default(0)
}

model Cart {
  id        String   @id @default(cuid())
  user      User?    @relation(fields: [userId], references: [id])
  userId    String?
  sessionId String?  @unique
  items     CartItem[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CartItem {
  id        String   @id @default(cuid())
  cart      Cart     @relation(fields: [cartId], references: [id])
  cartId    String
  variant   ProductVariant @relation(fields: [variantId], references: [id])
  variantId String
  quantity  Int      @default(1)
  unitPrice Decimal  @db.Decimal(10,2)
}

model Order {
  id        String   @id @default(cuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  status    OrderStatus @default(PLACED)
  items     OrderItem[]
  total     Decimal  @db.Decimal(10,2)
  currency  String   @default("USD")
  shipping  Address? @relation(fields: [shippingId], references: [id])
  shippingId String?
  payments  Payment[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model OrderItem {
  id        String   @id @default(cuid())
  order     Order    @relation(fields: [orderId], references: [id])
  orderId   String
  vendor    Vendor   @relation(fields: [vendorId], references: [id])
  vendorId  String
  variant   ProductVariant @relation(fields: [variantId], references: [id])
  variantId String
  quantity  Int
  unitPrice Decimal  @db.Decimal(10,2)
}

model Payment {
  id        String   @id @default(cuid())
  order     Order    @relation(fields: [orderId], references: [id])
  orderId   String
  provider  Provider
  providerId String
  status    PaymentStatus
  amount    Decimal @db.Decimal(10,2)
  currency  String  @default("USD")
  raw       Json
  createdAt DateTime @default(now())
}

model Payout {
  id        String  @id @default(cuid())
  vendor    Vendor  @relation(fields: [vendorId], references: [id])
  vendorId  String
  amount    Decimal @db.Decimal(10,2)
  currency  String  @default("USD")
  status    String
  providerId String?
  periodStart DateTime
  periodEnd   DateTime
  createdAt DateTime @default(now())
}

model Review {
  id        String  @id @default(cuid())
  product   Product @relation(fields: [productId], references: [id])
  productId String
  user      User    @relation(fields: [userId], references: [id])
  userId    String
  rating    Int
  body      String
  status    ReviewStatus @default(PENDING)
  createdAt DateTime @default(now())
}

model Question {
  id        String  @id @default(cuid())
  product   Product @relation(fields: [productId], references: [id])
  productId String
  user      User    @relation(fields: [userId], references: [id])
  userId    String
  body      String
  answers   Answer[]
  createdAt DateTime @default(now())
}

model Answer {
  id        String  @id @default(cuid())
  question  Question @relation(fields: [questionId], references: [id])
  questionId String
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  body      String
  createdAt DateTime @default(now())
}

model ReturnRequest {
  id          String  @id @default(cuid())
  orderItem   OrderItem @relation(fields: [orderItemId], references: [id])
  orderItemId String
  reason      String
  status      String @default("OPEN")
  resolvedAt  DateTime?
  createdAt   DateTime @default(now())
}

model SupportTicket {
  id        String  @id @default(cuid())
  user      User    @relation(fields: [userId], references: [id])
  userId    String
  order     Order?  @relation(fields: [orderId], references: [id])
  orderId   String?
  subject   String
  body      String
  status    String @default("OPEN")
  createdAt DateTime @default(now())
}

model Address {
  id       String  @id @default(cuid())
  user     User?   @relation(fields: [userId], references: [id])
  userId   String?
  name     String
  line1    String
  line2    String?
  city     String
  state    String?
  postal   String
  country  String
}

model Coupon {
  id        String @id @default(cuid())
  code      String @unique
  type      String // PERCENT or AMOUNT
  value     Int
  validFrom DateTime
  validTo   DateTime
  usageLimit Int?
  usedCount Int @default(0)
}

model WebhookEvent {
  id        String  @id @default(cuid())
  provider  String
  eventId   String  @unique
  payload   Json
  processedAt DateTime?
  createdAt DateTime @default(now())
}

model AuditLog {
  id          String  @id @default(cuid())
  actorUserId String?
  action      String
  target      String
  meta        Json
  createdAt   DateTime @default(now())
}

model Account {
  id                 String  @id @default(cuid())
  userId             String
  type               String
  provider           String
  providerAccountId  String
  refresh_token      String?
  access_token       String?
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String?
  session_state      String?
  user               User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}
