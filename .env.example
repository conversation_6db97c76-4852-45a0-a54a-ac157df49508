# Core
NODE_ENV=development
DATABASE_URL=postgresql://USER:PASSWORD@localhost:5432/electrohub?schema=public
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=replace-with-strong-random

# Auth Providers (optional for dev)
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
EMAIL_SERVER=smtp://user:<EMAIL>:587
EMAIL_FROM=ElectroHub <<EMAIL>>

# Stripe
STRIPE_PUBLIC_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
STRIPE_CONNECT_CLIENT_ID=

# Storage (S3 compatible)
AWS_S3_REGION=
AWS_S3_BUCKET=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

# Redis (for prod rate limiting / queues)
REDIS_URL=
