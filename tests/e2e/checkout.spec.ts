import { test, expect } from '@playwright/test';

test('home loads, product link works, cart shows checkout', async ({ page }) => {
  await page.goto('/');
  await expect(page.getByRole('heading', { name: 'Electronics-first Marketplace' })).toBeVisible();
  const firstProduct = page.locator('article').first();
  await firstProduct.click();
  await expect(page.getByRole('heading')).toContainText('iPhone');
  await page.getByRole('button', { name: 'Add to cart' }).click();
  await page.goto('/cart');
  await expect(page.getByRole('button', { name: 'Proceed to checkout' })).toBeVisible();
});
