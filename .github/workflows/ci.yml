name: CI
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:16
        ports: ['5432:5432']
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: electrohub
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with: { node-version: '20' }
      - run: npm ci
      - run: echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/electrohub?schema=public" >> .env
      - run: npm run db:generate
      - run: npm run db:migrate
      - run: npm run db:seed
      - run: npm run typecheck
      - run: npm run test
