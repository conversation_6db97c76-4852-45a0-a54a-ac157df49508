!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom/test-utils"),require("react-dom"),require("react-dom/client")):"function"==typeof define&&define.amd?define(["exports","react","react-dom/test-utils","react-dom","react-dom/client"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryReact={},e.<PERSON>act,e.ReactTestUtils,e.ReactDOM,e.ReactDOMClient)}(this,(function(e,t,r,n,a){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}function i(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var u=l(t),s=l(r),d=o(n),c=l(a);const p="function"==typeof u.act?u.act:s.act;function m(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}function f(e){m().IS_REACT_ACT_ENVIRONMENT=e}function b(){return m().IS_REACT_ACT_ENVIRONMENT}const v=(y=p,e=>{const t=b();f(!0);try{let r=!1;const n=y((()=>{const t=e();return null!==t&&"object"==typeof t&&"function"==typeof t.then&&(r=!0),t}));return r?{then:(e,r)=>{n.then((r=>{f(t),e(r)}),(e=>{f(t),r(e)}))}}:(f(t),n)}catch(e){throw f(t),e}});var y,h={},g={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>`[${38+e};5;${t}m`},r=function(e){return void 0===e&&(e=0),(t,r,n)=>`[${38+e};2;${t};${r};${n}m`};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,a]of Object.entries(r))n[t]={open:`[${a[0]}m`,close:`[${a[1]}m`},r[t]=n[t],e.set(a[0],a[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(g);var C={};Object.defineProperty(C,"__esModule",{value:!0}),C.printIteratorEntries=function(e,t,r,n,a,o,l){void 0===l&&(l=": ");let i="",u=e.next();if(!u.done){i+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){i+=s+o(u.value[0],t,s,n,a)+l+o(u.value[1],t,s,n,a),u=e.next(),u.done?t.min||(i+=","):i+=","+t.spacingInner}i+=t.spacingOuter+r}return i},C.printIteratorValues=function(e,t,r,n,a,o){let l="",i=e.next();if(!i.done){l+=t.spacingOuter;const u=r+t.indent;for(;!i.done;)l+=u+o(i.value,t,u,n,a),i=e.next(),i.done?t.min||(l+=","):l+=","+t.spacingInner;l+=t.spacingOuter+r}return l},C.printListItems=function(e,t,r,n,a,o){let l="";if(e.length){l+=t.spacingOuter;const i=r+t.indent;for(let r=0;r<e.length;r++)l+=i,r in e&&(l+=o(e[r],t,i,n,a)),r<e.length-1?l+=","+t.spacingInner:t.min||(l+=",");l+=t.spacingOuter+r}return l},C.printObjectProperties=function(e,t,r,n,a,o){let l="";const i=P(e,t.compareKeys);if(i.length){l+=t.spacingOuter;const u=r+t.indent;for(let r=0;r<i.length;r++){const s=i[r];l+=u+o(s,t,u,n,a)+": "+o(e[s],t,u,n,a),r<i.length-1?l+=","+t.spacingInner:t.min||(l+=",")}l+=t.spacingOuter+r}return l};const P=(e,t)=>{const r=Object.keys(e).sort(t);return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)})),r};var q={};Object.defineProperty(q,"__esModule",{value:!0}),q.test=q.serialize=q.default=void 0;var E=C,w="undefined"!=typeof globalThis?globalThis:void 0!==w?w:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),x=w["jest-symbol-do-not-touch"]||w.Symbol;const R="function"==typeof x&&x.for?x.for("jest.asymmetricMatcher"):1267621,O=" ",T=(e,t,r,n,a,o)=>{const l=e.toString();return"ArrayContaining"===l||"ArrayNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+O+"["+(0,E.printListItems)(e.sample,t,r,n,a,o)+"]":"ObjectContaining"===l||"ObjectNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+O+"{"+(0,E.printObjectProperties)(e.sample,t,r,n,a,o)+"}":"StringMatching"===l||"StringNotMatching"===l||"StringContaining"===l||"StringNotContaining"===l?l+O+o(e.sample,t,r,n,a):e.toAsymmetricMatcher()};q.serialize=T;const _=e=>e&&e.$$typeof===R;q.test=_;var M={serialize:T,test:_};q.default=M;var A={};Object.defineProperty(A,"__esModule",{value:!0}),A.test=A.serialize=A.default=void 0;var j=I((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),S=I(g.exports);function I(e){return e&&e.__esModule?e:{default:e}}const B=e=>"string"==typeof e&&!!e.match((0,j.default)());A.test=B;const N=(e,t,r,n,a,o)=>o(e.replace((0,j.default)(),(e=>{switch(e){case S.default.red.close:case S.default.green.close:case S.default.cyan.close:case S.default.gray.close:case S.default.white.close:case S.default.yellow.close:case S.default.bgRed.close:case S.default.bgGreen.close:case S.default.bgYellow.close:case S.default.inverse.close:case S.default.dim.close:case S.default.bold.close:case S.default.reset.open:case S.default.reset.close:return"</>";case S.default.red.open:return"<red>";case S.default.green.open:return"<green>";case S.default.cyan.open:return"<cyan>";case S.default.gray.open:return"<gray>";case S.default.white.open:return"<white>";case S.default.yellow.open:return"<yellow>";case S.default.bgRed.open:return"<bgRed>";case S.default.bgGreen.open:return"<bgGreen>";case S.default.bgYellow.open:return"<bgYellow>";case S.default.inverse.open:return"<inverse>";case S.default.dim.open:return"<dim>";case S.default.bold.open:return"<bold>";default:return""}})),t,r,n,a);A.serialize=N;var k={serialize:N,test:B};A.default=k;var F={};Object.defineProperty(F,"__esModule",{value:!0}),F.test=F.serialize=F.default=void 0;var L=C;const U=["DOMStringMap","NamedNodeMap"],H=/^(HTML\w*Collection|NodeList)$/,D=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==U.indexOf(t)||H.test(t));var t};F.test=D;const z=(e,t,r,n,a,o)=>{const l=e.constructor.name;return++n>t.maxDepth?"["+l+"]":(t.min?"":l+" ")+(-1!==U.indexOf(l)?"{"+(0,L.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,a,o)+"}":"["+(0,L.printListItems)(Array.from(e),t,r,n,a,o)+"]")};F.serialize=z;var V={serialize:z,test:D};F.default=V;var $={},W={},G={};Object.defineProperty(G,"__esModule",{value:!0}),G.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty(W,"__esModule",{value:!0}),W.printText=W.printProps=W.printElementAsLeaf=W.printElement=W.printComment=W.printChildren=void 0;var Q,K=(Q=G)&&Q.__esModule?Q:{default:Q};W.printProps=(e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")};W.printChildren=(e,t,r,n,a,o)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?X(e,t):o(e,t,r,n,a)))).join("");const X=(e,t)=>{const r=t.colors.content;return r.open+(0,K.default)(e)+r.close};W.printText=X;W.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,K.default)(e)+"--\x3e"+r.close};W.printElement=(e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close};W.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty($,"__esModule",{value:!0}),$.test=$.serialize=$.default=void 0;var J=W;const Y=/^((HTML|SVG)\w*)?Element$/,Z=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&(Y.test(t)||a)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function ee(e){return 11===e.nodeType}$.test=Z;const te=(e,t,r,n,a,o)=>{if(function(e){return 3===e.nodeType}(e))return(0,J.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,J.printComment)(e.data,t);const l=ee(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,J.printElementAsLeaf)(l,t):(0,J.printElement)(l,(0,J.printProps)(ee(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),ee(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,a,o),(0,J.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,a,o),t,r)};$.serialize=te;var re={serialize:te,test:Z};$.default=re;var ne={};Object.defineProperty(ne,"__esModule",{value:!0}),ne.test=ne.serialize=ne.default=void 0;var ae=C;const oe="@@__IMMUTABLE_ORDERED__@@",le=e=>"Immutable."+e,ie=e=>"["+e+"]",ue=" ";const se=(e,t,r,n,a,o,l)=>++n>t.maxDepth?ie(le(l)):le(l)+ue+"["+(0,ae.printIteratorValues)(e.values(),t,r,n,a,o)+"]",de=(e,t,r,n,a,o)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,a,o,l)=>++n>t.maxDepth?ie(le(l)):le(l)+ue+"{"+(0,ae.printIteratorEntries)(e.entries(),t,r,n,a,o)+"}")(e,t,r,n,a,o,e[oe]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?se(e,t,r,n,a,o,"List"):e["@@__IMMUTABLE_SET__@@"]?se(e,t,r,n,a,o,e[oe]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?se(e,t,r,n,a,o,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,a,o)=>{const l=le("Seq");return++n>t.maxDepth?ie(l):e["@@__IMMUTABLE_KEYED__@@"]?l+ue+"{"+(e._iter||e._object?(0,ae.printIteratorEntries)(e.entries(),t,r,n,a,o):"…")+"}":l+ue+"["+(e._iter||e._array||e._collection||e._iterable?(0,ae.printIteratorValues)(e.values(),t,r,n,a,o):"…")+"]"})(e,t,r,n,a,o):((e,t,r,n,a,o)=>{const l=le(e._name||"Record");return++n>t.maxDepth?ie(l):l+ue+"{"+(0,ae.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,a,o)+"}"})(e,t,r,n,a,o);ne.serialize=de;const ce=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);ne.test=ce;var pe={serialize:de,test:ce};ne.default=pe;var me,fe={},be={exports:{}},ve={};!function(e){e.exports=function(){if(me)return ve;me=1;var e=60103,t=60106,r=60107,n=60108,a=60114,o=60109,l=60110,i=60112,u=60113,s=60120,d=60115,c=60116,p=60121,m=60122,f=60117,b=60129,v=60131;if("function"==typeof Symbol&&Symbol.for){var y=Symbol.for;e=y("react.element"),t=y("react.portal"),r=y("react.fragment"),n=y("react.strict_mode"),a=y("react.profiler"),o=y("react.provider"),l=y("react.context"),i=y("react.forward_ref"),u=y("react.suspense"),s=y("react.suspense_list"),d=y("react.memo"),c=y("react.lazy"),p=y("react.block"),m=y("react.server.block"),f=y("react.fundamental"),b=y("react.debug_trace_mode"),v=y("react.legacy_hidden")}function h(p){if("object"==typeof p&&null!==p){var m=p.$$typeof;switch(m){case e:switch(p=p.type){case r:case a:case n:case u:case s:return p;default:switch(p=p&&p.$$typeof){case l:case i:case c:case d:case o:return p;default:return m}}case t:return m}}}var g=o,C=e,P=i,q=r,E=c,w=d,x=t,R=a,O=n,T=u;return ve.ContextConsumer=l,ve.ContextProvider=g,ve.Element=C,ve.ForwardRef=P,ve.Fragment=q,ve.Lazy=E,ve.Memo=w,ve.Portal=x,ve.Profiler=R,ve.StrictMode=O,ve.Suspense=T,ve.isAsyncMode=function(){return!1},ve.isConcurrentMode=function(){return!1},ve.isContextConsumer=function(e){return h(e)===l},ve.isContextProvider=function(e){return h(e)===o},ve.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===e},ve.isForwardRef=function(e){return h(e)===i},ve.isFragment=function(e){return h(e)===r},ve.isLazy=function(e){return h(e)===c},ve.isMemo=function(e){return h(e)===d},ve.isPortal=function(e){return h(e)===t},ve.isProfiler=function(e){return h(e)===a},ve.isStrictMode=function(e){return h(e)===n},ve.isSuspense=function(e){return h(e)===u},ve.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===a||e===b||e===n||e===u||e===s||e===v||"object"==typeof e&&null!==e&&(e.$$typeof===c||e.$$typeof===d||e.$$typeof===o||e.$$typeof===l||e.$$typeof===i||e.$$typeof===f||e.$$typeof===p||e[0]===m)},ve.typeOf=h,ve}()}(be),Object.defineProperty(fe,"__esModule",{value:!0}),fe.test=fe.serialize=fe.default=void 0;var ye=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=ge(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(be.exports),he=W;function ge(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(ge=function(e){return e?r:t})(e)}const Ce=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{Ce(e,t)})):null!=e&&!1!==e&&t.push(e),t},Pe=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(ye.isFragment(e))return"React.Fragment";if(ye.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(ye.isContextProvider(e))return"Context.Provider";if(ye.isContextConsumer(e))return"Context.Consumer";if(ye.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(ye.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},qe=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,he.printElementAsLeaf)(Pe(e),t):(0,he.printElement)(Pe(e),(0,he.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,a,o),(0,he.printChildren)(Ce(e.props.children),t,r+t.indent,n,a,o),t,r);fe.serialize=qe;const Ee=e=>null!=e&&ye.isElement(e);fe.test=Ee;var we={serialize:qe,test:Ee};fe.default=we;var xe={};Object.defineProperty(xe,"__esModule",{value:!0}),xe.test=xe.serialize=xe.default=void 0;var Re=W,Oe="undefined"!=typeof globalThis?globalThis:void 0!==Oe?Oe:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),Te=Oe["jest-symbol-do-not-touch"]||Oe.Symbol;const _e="function"==typeof Te&&Te.for?Te.for("react.test.json"):245830487,Me=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,Re.printElementAsLeaf)(e.type,t):(0,Re.printElement)(e.type,e.props?(0,Re.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,a,o):"",e.children?(0,Re.printChildren)(e.children,t,r+t.indent,n,a,o):"",t,r);xe.serialize=Me;const Ae=e=>e&&e.$$typeof===_e;xe.test=Ae;var je={serialize:Me,test:Ae};xe.default=je,Object.defineProperty(h,"__esModule",{value:!0});var Se=h.default=h.DEFAULT_OPTIONS=void 0,Ie=h.format=ht,Be=h.plugins=void 0,Ne=$e(g.exports),ke=C,Fe=$e(q),Le=$e(A),Ue=$e(F),He=$e($),De=$e(ne),ze=$e(fe),Ve=$e(xe);function $e(e){return e&&e.__esModule?e:{default:e}}const We=Object.prototype.toString,Ge=Date.prototype.toISOString,Qe=Error.prototype.toString,Ke=RegExp.prototype.toString,Xe=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",Je=e=>"undefined"!=typeof window&&e===window,Ye=/^Symbol\((.*)\)(.*)$/,Ze=/\n/gi;class et extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function tt(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function rt(e){return String(e).replace(Ye,"Symbol($1)")}function nt(e){return"["+Qe.call(e)+"]"}function at(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const a=typeof e;if("number"===a)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===a)return function(e){return String(`${e}n`)}(e);if("string"===a)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===a)return tt(e,t);if("symbol"===a)return rt(e);const o=We.call(e);return"[object WeakMap]"===o?"WeakMap {}":"[object WeakSet]"===o?"WeakSet {}":"[object Function]"===o||"[object GeneratorFunction]"===o?tt(e,t):"[object Symbol]"===o?rt(e):"[object Date]"===o?isNaN(+e)?"Date { NaN }":Ge.call(e):"[object Error]"===o?nt(e):"[object RegExp]"===o?r?Ke.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):Ke.call(e):e instanceof Error?nt(e):null}function ot(e,t,r,n,a,o){if(-1!==a.indexOf(e))return"[Circular]";(a=a.slice()).push(e);const l=++n>t.maxDepth,i=t.min;if(t.callToJSON&&!l&&e.toJSON&&"function"==typeof e.toJSON&&!o)return ut(e.toJSON(),t,r,n,a,!0);const u=We.call(e);return"[object Arguments]"===u?l?"[Arguments]":(i?"":"Arguments ")+"["+(0,ke.printListItems)(e,t,r,n,a,ut)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?l?"["+e.constructor.name+"]":(i?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,ke.printListItems)(e,t,r,n,a,ut)+"]":"[object Map]"===u?l?"[Map]":"Map {"+(0,ke.printIteratorEntries)(e.entries(),t,r,n,a,ut," => ")+"}":"[object Set]"===u?l?"[Set]":"Set {"+(0,ke.printIteratorValues)(e.values(),t,r,n,a,ut)+"}":l||Je(e)?"["+Xe(e)+"]":(i?"":t.printBasicPrototype||"Object"!==Xe(e)?Xe(e)+" ":"")+"{"+(0,ke.printObjectProperties)(e,t,r,n,a,ut)+"}"}function lt(e,t,r,n,a,o){let l;try{l=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,a,o,ut):e.print(t,(e=>ut(e,r,n,a,o)),(e=>{const t=n+r.indent;return t+e.replace(Ze,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new et(e.message,e.stack)}if("string"!=typeof l)throw new Error(`pretty-format: Plugin must return type "string" but instead returned "${typeof l}".`);return l}function it(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new et(e.message,e.stack)}return null}function ut(e,t,r,n,a,o){const l=it(t.plugins,e);if(null!==l)return lt(l,e,t,r,n,a);const i=at(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==i?i:ot(e,t,r,n,a,o)}const st={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},dt=Object.keys(st),ct={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:st};var pt=h.DEFAULT_OPTIONS=ct;const mt=e=>dt.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:st[r],a=n&&Ne.default[n];if(!a||"string"!=typeof a.close||"string"!=typeof a.open)throw new Error(`pretty-format: Option "theme" has a key "${r}" whose value "${n}" is undefined in ansi-styles.`);return t[r]=a,t}),Object.create(null)),ft=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:ct.printFunctionName,bt=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:ct.escapeRegex,vt=e=>e&&void 0!==e.escapeString?e.escapeString:ct.escapeString,yt=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:ct.callToJSON,colors:e&&e.highlight?mt(e):dt.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:ct.compareKeys,escapeRegex:bt(e),escapeString:vt(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:ct.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:ct.maxDepth,min:e&&void 0!==e.min?e.min:ct.min,plugins:e&&void 0!==e.plugins?e.plugins:ct.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:ft(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function ht(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!ct.hasOwnProperty(e))throw new Error(`pretty-format: Unknown option "${e}".`)})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error(`pretty-format: Option "theme" must be of type "object" but instead received "${typeof e.theme}".`)}}(t),t.plugins)){const r=it(t.plugins,e);if(null!==r)return lt(r,e,yt(t),"",0,[])}const r=at(e,ft(t),bt(t),vt(t));return null!==r?r:ot(e,yt(t),"",0,[])}const gt={AsymmetricMatcher:Fe.default,ConvertAnsi:Le.default,DOMCollection:Ue.default,DOMElement:He.default,Immutable:De.default,ReactElement:ze.default,ReactTestComponent:Ve.default};Be=h.plugins=gt;var Ct=ht;Se=h.default=Ct;var Pt=i({__proto__:null,get DEFAULT_OPTIONS(){return pt},format:Ie,get plugins(){return Be},get default(){return Se}},[h]),qt=Object.prototype.toString;function Et(e){return"function"==typeof e||"[object Function]"===qt.call(e)}var wt=Math.pow(2,53)-1;function xt(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),wt)}function Rt(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!Et(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var a,o=xt(n.length),l=Et(r)?Object(new r(o)):new Array(o),i=0;i<o;)a=n[i],l[i]=t?t(a,i):a,i+=1;return l.length=o,l}function Ot(e){return Ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ot(e)}function Tt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_t(n.key),n)}}function _t(e){var t=function(e,t){if("object"!==Ot(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ot(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ot(t)?t:String(t)}var Mt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t,r){(t=_t(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&Tt(t.prototype,r),n&&Tt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),At="undefined"==typeof Set?Set:Mt;function jt(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var St={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},It={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function Bt(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=It[t])&&void 0!==n&&n.has(r))}))}(e,t)}function Nt(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=St[jt(e)];if(void 0!==t)return t;switch(jt(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||Bt(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||Bt(e,r||""))return r}return t}function kt(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function Ft(e){return kt(e)&&"caption"===jt(e)}function Lt(e){return kt(e)&&"input"===jt(e)}function Ut(e){return kt(e)&&"legend"===jt(e)}function Ht(e){return function(e){return kt(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===jt(e)}function Dt(e,t){if(kt(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function zt(e,t){return!!kt(e)&&-1!==t.indexOf(Nt(e))}function Vt(e,t){if(!kt(e))return!1;if("range"===t)return zt(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function $t(e,t){var r=Rt(e.querySelectorAll(t));return Dt(e,"aria-owns").forEach((function(e){r.push.apply(r,Rt(e.querySelectorAll(t)))})),r}function Wt(e){return kt(t=e)&&"select"===jt(t)?e.selectedOptions||$t(e,"[selected]"):$t(e,'[aria-selected="true"]');var t}function Gt(e){return Lt(e)||kt(t=e)&&"textarea"===jt(t)?e.value:e.textContent||"";var t}function Qt(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function Kt(e){var t=jt(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function Xt(e){if(Kt(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&kt(e)){var r=Xt(e);null!==r&&(t=r)}})),t}function Jt(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):Xt(e)}function Yt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new At,n=function(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}(e),a=t.compute,o=void 0===a?"name":a,l=t.computedStyleSupportsPseudoElements,i=void 0===l?void 0!==t.getComputedStyle:l,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,d=t.hidden,c=void 0!==d&&d;function p(e,t){var r,n,a="";if(kt(e)&&i){var o=Qt(s(e,"::before"));a="".concat(o," ").concat(a)}if((function(e){return kt(e)&&"slot"===jt(e)}(e)?0===(n=(r=e).assignedNodes()).length?Rt(r.childNodes):n:Rt(e.childNodes).concat(Dt(e,"aria-owns"))).forEach((function(e){var r=b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),n="inline"!==(kt(e)?s(e).getPropertyValue("display"):"inline")?" ":"";a+="".concat(n).concat(r).concat(n)})),kt(e)&&i){var l=Qt(s(e,"::after"));a="".concat(a," ").concat(l)}return a.trim()}function m(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}function f(e){if(!kt(e))return null;if(function(e){return kt(e)&&"fieldset"===jt(e)}(e)){r.add(e);for(var t=Rt(e.childNodes),n=0;n<t.length;n+=1){var a=t[n];if(Ut(a))return b(a,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(function(e){return kt(e)&&"table"===jt(e)}(e)){r.add(e);for(var o=Rt(e.childNodes),l=0;l<o.length;l+=1){var i=o[l];if(Ft(i))return b(i,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(function(e){return kt(e)&&"svg"===jt(e)}(e)){r.add(e);for(var u=Rt(e.childNodes),s=0;s<u.length;s+=1){var d=u[s];if(Ht(d))return d.textContent}return null}if("img"===jt(e)||"area"===jt(e)){var c=m(e,"alt");if(null!==c)return c}else if(function(e){return kt(e)&&"optgroup"===jt(e)}(e)){var f=m(e,"label");if(null!==f)return f}}if(Lt(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var v=m(e,"value");if(null!==v)return v;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var y,h,g=null===(h=(y=e).labels)?h:void 0!==h?Rt(h):Kt(y)?Rt(y.ownerDocument.querySelectorAll("label")).filter((function(e){return Jt(e)===y})):null;if(null!==g&&0!==g.length)return r.add(e),Rt(g).map((function(e){return b(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(Lt(e)&&"image"===e.type){var C=m(e,"alt");if(null!==C)return C;var P=m(e,"title");return null!==P?P:"Submit Query"}if(zt(e,["button"])){var q=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});if(""!==q)return q}return null}function b(e,t){if(r.has(e))return"";if(!c&&function(e,t){if(!kt(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}(e,s)&&!t.isReferenced)return r.add(e),"";var n=kt(e)?e.getAttributeNode("aria-labelledby"):null,a=null===n||r.has(n)?[]:Dt(e,"aria-labelledby");if("name"===o&&!t.isReferenced&&a.length>0)return r.add(n),a.map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var l,i=t.recursion&&(zt(l=e,["button","combobox","listbox","textbox"])||Vt(l,"range"))&&"name"===o;if(!i){var u=(kt(e)&&e.getAttribute("aria-label")||"").trim();if(""!==u&&"name"===o)return r.add(e),u;if(!function(e){return zt(e,["none","presentation"])}(e)){var d=f(e);if(null!==d)return r.add(e),d}}if(zt(e,["menu"]))return r.add(e),"";if(i||t.isEmbeddedInLabel||t.isReferenced){if(zt(e,["combobox","listbox"])){r.add(e);var v=Wt(e);return 0===v.length?Lt(e)?e.value:"":Rt(v).map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(Vt(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(zt(e,["textbox"]))return r.add(e),Gt(e)}if(function(e){return zt(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}(e)||kt(e)&&t.isReferenced||function(e){return Ft(e)}(e)){var y=p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});if(""!==y)return r.add(e),y}if(e.nodeType===e.TEXT_NODE)return r.add(e),e.textContent||"";if(t.recursion)return r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});var h=function(e){return kt(e)?m(e,"title"):null}(e);return null!==h?(r.add(e),h):(r.add(e),"")}return b(e,{isEmbeddedInLabel:!1,isReferenced:"description"===o,recursion:!1}).trim().replace(/\s\s+/g," ")}function Zt(e){return Zt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Zt(e)}function er(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function tr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?er(Object(r),!0).forEach((function(t){rr(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):er(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function rr(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Zt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Zt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Zt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Dt(e,"aria-describedby").map((function(e){return Yt(e,tr(tr({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function ar(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return zt(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])?"":Yt(e,t)}var or={},lr={},ir={},ur={};Object.defineProperty(ur,"__esModule",{value:!0}),ur.default=void 0;var sr=function(){var e=this,t=0,r={"@@iterator":function(){return r},next:function(){if(t<e.length){var r=e[t];return t+=1,{done:!1,value:r}}return{done:!0}}};return r};ur.default=sr,Object.defineProperty(ir,"__esModule",{value:!0}),ir.default=function(e,t){"function"==typeof Symbol&&"symbol"===cr(Symbol.iterator)&&Object.defineProperty(e,Symbol.iterator,{value:dr.default.bind(t)});return e};var dr=function(e){return e&&e.__esModule?e:{default:e}}(ur);function cr(e){return cr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cr(e)}Object.defineProperty(lr,"__esModule",{value:!0}),lr.default=void 0;var pr=function(e){return e&&e.__esModule?e:{default:e}}(ir);function mr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||fr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fr(e,t){if(e){if("string"==typeof e)return br(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?br(e,t):void 0}}function br(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var vr=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-braillelabel",{type:"string"}],["aria-brailleroledescription",{type:"string"}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-description",{type:"string"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],yr={entries:function(){return vr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=fr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(vr);try{for(n.s();!(t=n.n()).done;){var a=mr(t.value,2),o=a[0],l=a[1];e.call(r,l,o,vr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=vr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!yr.get(e)},keys:function(){return vr.map((function(e){return mr(e,1)[0]}))},values:function(){return vr.map((function(e){return mr(e,2)[1]}))}},hr=(0,pr.default)(yr,yr.entries());lr.default=hr;var gr={};Object.defineProperty(gr,"__esModule",{value:!0}),gr.default=void 0;var Cr=function(e){return e&&e.__esModule?e:{default:e}}(ir);function Pr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||qr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qr(e,t){if(e){if("string"==typeof e)return Er(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Er(e,t):void 0}}function Er(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var wr=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],xr={entries:function(){return wr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=qr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(wr);try{for(n.s();!(t=n.n()).done;){var a=Pr(t.value,2),o=a[0],l=a[1];e.call(r,l,o,wr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=wr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!xr.get(e)},keys:function(){return wr.map((function(e){return Pr(e,1)[0]}))},values:function(){return wr.map((function(e){return Pr(e,2)[1]}))}},Rr=(0,Cr.default)(xr,xr.entries());gr.default=Rr;var Or={},Tr={},_r={};Object.defineProperty(_r,"__esModule",{value:!0}),_r.default=void 0;var Mr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};_r.default=Mr;var Ar={};Object.defineProperty(Ar,"__esModule",{value:!0}),Ar.default=void 0;var jr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Ar.default=jr;var Sr={};Object.defineProperty(Sr,"__esModule",{value:!0}),Sr.default=void 0;var Ir={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Sr.default=Ir;var Br={};Object.defineProperty(Br,"__esModule",{value:!0}),Br.default=void 0;var Nr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Br.default=Nr;var kr={};Object.defineProperty(kr,"__esModule",{value:!0}),kr.default=void 0;var Fr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};kr.default=Fr;var Lr={};Object.defineProperty(Lr,"__esModule",{value:!0}),Lr.default=void 0;var Ur={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Lr.default=Ur;var Hr={};Object.defineProperty(Hr,"__esModule",{value:!0}),Hr.default=void 0;var Dr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Hr.default=Dr;var zr={};Object.defineProperty(zr,"__esModule",{value:!0}),zr.default=void 0;var Vr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};zr.default=Vr;var $r={};Object.defineProperty($r,"__esModule",{value:!0}),$r.default=void 0;var Wr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};$r.default=Wr;var Gr={};Object.defineProperty(Gr,"__esModule",{value:!0}),Gr.default=void 0;var Qr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Gr.default=Qr;var Kr={};Object.defineProperty(Kr,"__esModule",{value:!0}),Kr.default=void 0;var Xr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Kr.default=Xr;var Jr={};Object.defineProperty(Jr,"__esModule",{value:!0}),Jr.default=void 0;var Yr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Jr.default=Yr,Object.defineProperty(Tr,"__esModule",{value:!0}),Tr.default=void 0;var Zr=pn(_r),en=pn(Ar),tn=pn(Sr),rn=pn(Br),nn=pn(kr),an=pn(Lr),on=pn(Hr),ln=pn(zr),un=pn($r),sn=pn(Gr),dn=pn(Kr),cn=pn(Jr);function pn(e){return e&&e.__esModule?e:{default:e}}var mn=[["command",Zr.default],["composite",en.default],["input",tn.default],["landmark",rn.default],["range",nn.default],["roletype",an.default],["section",on.default],["sectionhead",ln.default],["select",un.default],["structure",sn.default],["widget",dn.default],["window",cn.default]];Tr.default=mn;var fn={},bn={};Object.defineProperty(bn,"__esModule",{value:!0}),bn.default=void 0;var vn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};bn.default=vn;var yn={};Object.defineProperty(yn,"__esModule",{value:!0}),yn.default=void 0;var hn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};yn.default=hn;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;var Cn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};gn.default=Cn;var Pn={};Object.defineProperty(Pn,"__esModule",{value:!0}),Pn.default=void 0;var qn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};Pn.default=qn;var En={};Object.defineProperty(En,"__esModule",{value:!0}),En.default=void 0;var wn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["scoped to the body element"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};En.default=wn;var xn={};Object.defineProperty(xn,"__esModule",{value:!0}),xn.default=void 0;var Rn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"blockquote"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};xn.default=Rn;var On={};Object.defineProperty(On,"__esModule",{value:!0}),On.default=void 0;var Tn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};On.default=Tn;var _n={};Object.defineProperty(_n,"__esModule",{value:!0}),_n.default=void 0;var Mn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"caption"},module:"HTML"}],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_n.default=Mn;var An={};Object.defineProperty(An,"__esModule",{value:!0}),An.default=void 0;var jn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["ancestor table element has table role"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};An.default=jn;var Sn={};Object.defineProperty(Sn,"__esModule",{value:!0}),Sn.default=void 0;var In={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};Sn.default=In;var Bn={};Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.default=void 0;var Nn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"code"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Bn.default=Nn;var kn={};Object.defineProperty(kn,"__esModule",{value:!0}),kn.default=void 0;var Fn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"col"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"colgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};kn.default=Fn;var Ln={};Object.defineProperty(Ln,"__esModule",{value:!0}),Ln.default=void 0;var Un={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],constraints:["the multiple attribute is not set and the size attribute does not have a value greater than 1"],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};Ln.default=Un;var Hn={};Object.defineProperty(Hn,"__esModule",{value:!0}),Hn.default=void 0;var Dn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-label"}],constraints:["scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"aside"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],constraints:["scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Hn.default=Dn;var zn={};Object.defineProperty(zn,"__esModule",{value:!0}),zn.default=void 0;var Vn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["scoped to the body element"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};zn.default=Vn;var $n={};Object.defineProperty($n,"__esModule",{value:!0}),$n.default=void 0;var Wn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};$n.default=Wn;var Gn={};Object.defineProperty(Gn,"__esModule",{value:!0}),Gn.default=void 0;var Qn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"del"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Gn.default=Qn;var Kn={};Object.defineProperty(Kn,"__esModule",{value:!0}),Kn.default=void 0;var Xn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};Kn.default=Xn;var Jn={};Object.defineProperty(Jn,"__esModule",{value:!0}),Jn.default=void 0;var Yn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Jn.default=Yn;var Zn={};Object.defineProperty(Zn,"__esModule",{value:!0}),Zn.default=void 0;var ea={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"html"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Zn.default=ea;var ta={};Object.defineProperty(ta,"__esModule",{value:!0}),ta.default=void 0;var ra={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"em"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ta.default=ra;var na={};Object.defineProperty(na,"__esModule",{value:!0}),na.default=void 0;var aa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};na.default=aa;var oa={};Object.defineProperty(oa,"__esModule",{value:!0}),oa.default=void 0;var la={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};oa.default=la;var ia={};Object.defineProperty(ia,"__esModule",{value:!0}),ia.default=void 0;var ua={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ia.default=ua;var sa={};Object.defineProperty(sa,"__esModule",{value:!0}),sa.default=void 0;var da={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"a"},module:"HTML"},{concept:{name:"area"},module:"HTML"},{concept:{name:"aside"},module:"HTML"},{concept:{name:"b"},module:"HTML"},{concept:{name:"bdo"},module:"HTML"},{concept:{name:"body"},module:"HTML"},{concept:{name:"data"},module:"HTML"},{concept:{name:"div"},module:"HTML"},{concept:{constraints:["scoped to the main element","scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"footer"},module:"HTML"},{concept:{constraints:["scoped to the main element","scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"header"},module:"HTML"},{concept:{name:"hgroup"},module:"HTML"},{concept:{name:"i"},module:"HTML"},{concept:{name:"pre"},module:"HTML"},{concept:{name:"q"},module:"HTML"},{concept:{name:"samp"},module:"HTML"},{concept:{name:"section"},module:"HTML"},{concept:{name:"small"},module:"HTML"},{concept:{name:"span"},module:"HTML"},{concept:{name:"u"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};sa.default=da;var ca={};Object.defineProperty(ca,"__esModule",{value:!0}),ca.default=void 0;var pa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};ca.default=pa;var ma={};Object.defineProperty(ma,"__esModule",{value:!0}),ma.default=void 0;var fa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{constraints:["ancestor table element has grid role","ancestor table element has treegrid role"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};ma.default=fa;var ba={};Object.defineProperty(ba,"__esModule",{value:!0}),ba.default=void 0;var va={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"},{concept:{name:"address"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ba.default=va;var ya={};Object.defineProperty(ya,"__esModule",{value:!0}),ya.default=void 0;var ha={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};ya.default=ha;var ga={};Object.defineProperty(ga,"__esModule",{value:!0}),ga.default=void 0;var Ca={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ga.default=Ca;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.default=void 0;var qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"ins"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Pa.default=qa;var Ea={};Object.defineProperty(Ea,"__esModule",{value:!0}),Ea.default=void 0;var wa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"href"}],name:"area"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Ea.default=wa;var xa={};Object.defineProperty(xa,"__esModule",{value:!0}),xa.default=void 0;var Ra={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};xa.default=Ra;var Oa={};Object.defineProperty(Oa,"__esModule",{value:!0}),Oa.default=void 0;var Ta={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"}],constraints:["the size attribute value is greater than 1"],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Oa.default=Ta;var _a={};Object.defineProperty(_a,"__esModule",{value:!0}),_a.default=void 0;var Ma={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol","direct descendant of ul","direct descendant of menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_a.default=Ma;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0}),Aa.default=void 0;var ja={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Aa.default=ja;var Sa={};Object.defineProperty(Sa,"__esModule",{value:!0}),Sa.default=void 0;var Ia={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Sa.default=Ia;var Ba={};Object.defineProperty(Ba,"__esModule",{value:!0}),Ba.default=void 0;var Na={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:[],props:{"aria-braillelabel":null,"aria-brailleroledescription":null,"aria-description":null},relatedConcepts:[{concept:{name:"mark"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ba.default=Na;var ka={};Object.defineProperty(ka,"__esModule",{value:!0}),ka.default=void 0;var Fa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ka.default=Fa;var La={};Object.defineProperty(La,"__esModule",{value:!0}),La.default=void 0;var Ua={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};La.default=Ua;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0}),Ha.default=void 0;var Da={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Ha.default=Da;var za={};Object.defineProperty(za,"__esModule",{value:!0}),za.default=void 0;var Va={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};za.default=Va;var $a={};Object.defineProperty($a,"__esModule",{value:!0}),$a.default=void 0;var Wa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};$a.default=Wa;var Ga={};Object.defineProperty(Ga,"__esModule",{value:!0}),Ga.default=void 0;var Qa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};Ga.default=Qa;var Ka={};Object.defineProperty(Ka,"__esModule",{value:!0}),Ka.default=void 0;var Xa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};Ka.default=Xa;var Ja={};Object.defineProperty(Ja,"__esModule",{value:!0}),Ja.default=void 0;var Ya={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{name:"meter"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};Ja.default=Ya;var Za={};Object.defineProperty(Za,"__esModule",{value:!0}),Za.default=void 0;var eo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Za.default=eo;var to={};Object.defineProperty(to,"__esModule",{value:!0}),to.default=void 0;var ro={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};to.default=ro;var no={};Object.defineProperty(no,"__esModule",{value:!0}),no.default=void 0;var ao={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};no.default=ao;var oo={};Object.defineProperty(oo,"__esModule",{value:!0}),oo.default=void 0;var lo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};oo.default=lo;var io={};Object.defineProperty(io,"__esModule",{value:!0}),io.default=void 0;var uo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"p"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};io.default=uo;var so={};Object.defineProperty(so,"__esModule",{value:!0}),so.default=void 0;var co={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{attributes:[{name:"alt",value:""}],name:"img"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};so.default=co;var po={};Object.defineProperty(po,"__esModule",{value:!0}),po.default=void 0;var mo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};po.default=mo;var fo={};Object.defineProperty(fo,"__esModule",{value:!0}),fo.default=void 0;var bo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};fo.default=bo;var vo={};Object.defineProperty(vo,"__esModule",{value:!0}),vo.default=void 0;var yo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};vo.default=yo;var ho={};Object.defineProperty(ho,"__esModule",{value:!0}),ho.default=void 0;var go={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ho.default=go;var Co={};Object.defineProperty(Co,"__esModule",{value:!0}),Co.default=void 0;var Po={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};Co.default=Po;var qo={};Object.defineProperty(qo,"__esModule",{value:!0}),qo.default=void 0;var Eo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};qo.default=Eo;var wo={};Object.defineProperty(wo,"__esModule",{value:!0}),wo.default=void 0;var xo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"rowgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row","rowgroup"],requiredContextRole:["row","rowgroup"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};wo.default=xo;var Ro={};Object.defineProperty(Ro,"__esModule",{value:!0}),Ro.default=void 0;var Oo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};Ro.default=Oo;var To={};Object.defineProperty(To,"__esModule",{value:!0}),To.default=void 0;var _o={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};To.default=_o;var Mo={};Object.defineProperty(Mo,"__esModule",{value:!0}),Mo.default=void 0;var Ao={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};Mo.default=Ao;var jo={};Object.defineProperty(jo,"__esModule",{value:!0}),jo.default=void 0;var So={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};jo.default=So;var Io={};Object.defineProperty(Io,"__esModule",{value:!0}),Io.default=void 0;var Bo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};Io.default=Bo;var No={};Object.defineProperty(No,"__esModule",{value:!0}),No.default=void 0;var ko={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};No.default=ko;var Fo={};Object.defineProperty(Fo,"__esModule",{value:!0}),Fo.default=void 0;var Lo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fo.default=Lo;var Uo={};Object.defineProperty(Uo,"__esModule",{value:!0}),Uo.default=void 0;var Ho={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"strong"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Uo.default=Ho;var Do={};Object.defineProperty(Do,"__esModule",{value:!0}),Do.default=void 0;var zo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"sub"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Do.default=zo;var Vo={};Object.defineProperty(Vo,"__esModule",{value:!0}),Vo.default=void 0;var $o={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"sup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Vo.default=$o;var Wo={};Object.defineProperty(Wo,"__esModule",{value:!0}),Wo.default=void 0;var Go={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};Wo.default=Go;var Qo={};Object.defineProperty(Qo,"__esModule",{value:!0}),Qo.default=void 0;var Ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};Qo.default=Ko;var Xo={};Object.defineProperty(Xo,"__esModule",{value:!0}),Xo.default=void 0;var Jo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};Xo.default=Jo;var Yo={};Object.defineProperty(Yo,"__esModule",{value:!0}),Yo.default=void 0;var Zo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};Yo.default=Zo;var el={};Object.defineProperty(el,"__esModule",{value:!0}),el.default=void 0;var tl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};el.default=tl;var rl={};Object.defineProperty(rl,"__esModule",{value:!0}),rl.default=void 0;var nl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};rl.default=nl;var al={};Object.defineProperty(al,"__esModule",{value:!0}),al.default=void 0;var ol={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};al.default=ol;var ll={};Object.defineProperty(ll,"__esModule",{value:!0}),ll.default=void 0;var il={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"time"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ll.default=il;var ul={};Object.defineProperty(ul,"__esModule",{value:!0}),ul.default=void 0;var sl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};ul.default=sl;var dl={};Object.defineProperty(dl,"__esModule",{value:!0}),dl.default=void 0;var cl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};dl.default=cl;var pl={};Object.defineProperty(pl,"__esModule",{value:!0}),pl.default=void 0;var ml={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};pl.default=ml;var fl={};Object.defineProperty(fl,"__esModule",{value:!0}),fl.default=void 0;var bl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};fl.default=bl;var vl={};Object.defineProperty(vl,"__esModule",{value:!0}),vl.default=void 0;var yl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};vl.default=yl;var hl={};Object.defineProperty(hl,"__esModule",{value:!0}),hl.default=void 0;var gl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};hl.default=gl,Object.defineProperty(fn,"__esModule",{value:!0}),fn.default=void 0;var Cl=Ki(bn),Pl=Ki(yn),ql=Ki(gn),El=Ki(Pn),wl=Ki(En),xl=Ki(xn),Rl=Ki(On),Ol=Ki(_n),Tl=Ki(An),_l=Ki(Sn),Ml=Ki(Bn),Al=Ki(kn),jl=Ki(Ln),Sl=Ki(Hn),Il=Ki(zn),Bl=Ki($n),Nl=Ki(Gn),kl=Ki(Kn),Fl=Ki(Jn),Ll=Ki(Zn),Ul=Ki(ta),Hl=Ki(na),Dl=Ki(oa),zl=Ki(ia),Vl=Ki(sa),$l=Ki(ca),Wl=Ki(ma),Gl=Ki(ba),Ql=Ki(ya),Kl=Ki(ga),Xl=Ki(Pa),Jl=Ki(Ea),Yl=Ki(xa),Zl=Ki(Oa),ei=Ki(_a),ti=Ki(Aa),ri=Ki(Sa),ni=Ki(Ba),ai=Ki(ka),oi=Ki(La),li=Ki(Ha),ii=Ki(za),ui=Ki($a),si=Ki(Ga),di=Ki(Ka),ci=Ki(Ja),pi=Ki(Za),mi=Ki(to),fi=Ki(no),bi=Ki(oo),vi=Ki(io),yi=Ki(so),hi=Ki(po),gi=Ki(fo),Ci=Ki(vo),Pi=Ki(ho),qi=Ki(Co),Ei=Ki(qo),wi=Ki(wo),xi=Ki(Ro),Ri=Ki(To),Oi=Ki(Mo),Ti=Ki(jo),_i=Ki(Io),Mi=Ki(No),Ai=Ki(Fo),ji=Ki(Uo),Si=Ki(Do),Ii=Ki(Vo),Bi=Ki(Wo),Ni=Ki(Qo),ki=Ki(Xo),Fi=Ki(Yo),Li=Ki(el),Ui=Ki(rl),Hi=Ki(al),Di=Ki(ll),zi=Ki(ul),Vi=Ki(dl),$i=Ki(pl),Wi=Ki(fl),Gi=Ki(vl),Qi=Ki(hl);function Ki(e){return e&&e.__esModule?e:{default:e}}var Xi=[["alert",Cl.default],["alertdialog",Pl.default],["application",ql.default],["article",El.default],["banner",wl.default],["blockquote",xl.default],["button",Rl.default],["caption",Ol.default],["cell",Tl.default],["checkbox",_l.default],["code",Ml.default],["columnheader",Al.default],["combobox",jl.default],["complementary",Sl.default],["contentinfo",Il.default],["definition",Bl.default],["deletion",Nl.default],["dialog",kl.default],["directory",Fl.default],["document",Ll.default],["emphasis",Ul.default],["feed",Hl.default],["figure",Dl.default],["form",zl.default],["generic",Vl.default],["grid",$l.default],["gridcell",Wl.default],["group",Gl.default],["heading",Ql.default],["img",Kl.default],["insertion",Xl.default],["link",Jl.default],["list",Yl.default],["listbox",Zl.default],["listitem",ei.default],["log",ti.default],["main",ri.default],["mark",ni.default],["marquee",ai.default],["math",oi.default],["menu",li.default],["menubar",ii.default],["menuitem",ui.default],["menuitemcheckbox",si.default],["menuitemradio",di.default],["meter",ci.default],["navigation",pi.default],["none",mi.default],["note",fi.default],["option",bi.default],["paragraph",vi.default],["presentation",yi.default],["progressbar",hi.default],["radio",gi.default],["radiogroup",Ci.default],["region",Pi.default],["row",qi.default],["rowgroup",Ei.default],["rowheader",wi.default],["scrollbar",xi.default],["search",Ri.default],["searchbox",Oi.default],["separator",Ti.default],["slider",_i.default],["spinbutton",Mi.default],["status",Ai.default],["strong",ji.default],["subscript",Si.default],["superscript",Ii.default],["switch",Bi.default],["tab",Ni.default],["table",ki.default],["tablist",Fi.default],["tabpanel",Li.default],["term",Ui.default],["textbox",Hi.default],["time",Di.default],["timer",zi.default],["toolbar",Vi.default],["tooltip",$i.default],["tree",Wi.default],["treegrid",Gi.default],["treeitem",Qi.default]];fn.default=Xi;var Ji={},Yi={};Object.defineProperty(Yi,"__esModule",{value:!0}),Yi.default=void 0;var Zi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Yi.default=Zi;var eu={};Object.defineProperty(eu,"__esModule",{value:!0}),eu.default=void 0;var tu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};eu.default=tu;var ru={};Object.defineProperty(ru,"__esModule",{value:!0}),ru.default=void 0;var nu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ru.default=nu;var au={};Object.defineProperty(au,"__esModule",{value:!0}),au.default=void 0;var ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};au.default=ou;var lu={};Object.defineProperty(lu,"__esModule",{value:!0}),lu.default=void 0;var iu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};lu.default=iu;var uu={};Object.defineProperty(uu,"__esModule",{value:!0}),uu.default=void 0;var su={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};uu.default=su;var du={};Object.defineProperty(du,"__esModule",{value:!0}),du.default=void 0;var cu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};du.default=cu;var pu={};Object.defineProperty(pu,"__esModule",{value:!0}),pu.default=void 0;var mu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};pu.default=mu;var fu={};Object.defineProperty(fu,"__esModule",{value:!0}),fu.default=void 0;var bu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};fu.default=bu;var vu={};Object.defineProperty(vu,"__esModule",{value:!0}),vu.default=void 0;var yu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};vu.default=yu;var hu={};Object.defineProperty(hu,"__esModule",{value:!0}),hu.default=void 0;var gu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};hu.default=gu;var Cu={};Object.defineProperty(Cu,"__esModule",{value:!0}),Cu.default=void 0;var Pu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};Cu.default=Pu;var qu={};Object.defineProperty(qu,"__esModule",{value:!0}),qu.default=void 0;var Eu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};qu.default=Eu;var wu={};Object.defineProperty(wu,"__esModule",{value:!0}),wu.default=void 0;var xu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};wu.default=xu;var Ru={};Object.defineProperty(Ru,"__esModule",{value:!0}),Ru.default=void 0;var Ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ru.default=Ou;var Tu={};Object.defineProperty(Tu,"__esModule",{value:!0}),Tu.default=void 0;var _u={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Tu.default=_u;var Mu={};Object.defineProperty(Mu,"__esModule",{value:!0}),Mu.default=void 0;var Au={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Mu.default=Au;var ju={};Object.defineProperty(ju,"__esModule",{value:!0}),ju.default=void 0;var Su={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ju.default=Su;var Iu={};Object.defineProperty(Iu,"__esModule",{value:!0}),Iu.default=void 0;var Bu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Iu.default=Bu;var Nu={};Object.defineProperty(Nu,"__esModule",{value:!0}),Nu.default=void 0;var ku={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Nu.default=ku;var Fu={};Object.defineProperty(Fu,"__esModule",{value:!0}),Fu.default=void 0;var Lu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fu.default=Lu;var Uu={};Object.defineProperty(Uu,"__esModule",{value:!0}),Uu.default=void 0;var Hu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Uu.default=Hu;var Du={};Object.defineProperty(Du,"__esModule",{value:!0}),Du.default=void 0;var zu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Du.default=zu;var Vu={};Object.defineProperty(Vu,"__esModule",{value:!0}),Vu.default=void 0;var $u={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Vu.default=$u;var Wu={};Object.defineProperty(Wu,"__esModule",{value:!0}),Wu.default=void 0;var Gu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Wu.default=Gu;var Qu={};Object.defineProperty(Qu,"__esModule",{value:!0}),Qu.default=void 0;var Ku={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Qu.default=Ku;var Xu={};Object.defineProperty(Xu,"__esModule",{value:!0}),Xu.default=void 0;var Ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Xu.default=Ju;var Yu={};Object.defineProperty(Yu,"__esModule",{value:!0}),Yu.default=void 0;var Zu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Yu.default=Zu;var es={};Object.defineProperty(es,"__esModule",{value:!0}),es.default=void 0;var ts={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};es.default=ts;var rs={};Object.defineProperty(rs,"__esModule",{value:!0}),rs.default=void 0;var ns={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};rs.default=ns;var as={};Object.defineProperty(as,"__esModule",{value:!0}),as.default=void 0;var os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};as.default=os;var ls={};Object.defineProperty(ls,"__esModule",{value:!0}),ls.default=void 0;var is={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ls.default=is;var us={};Object.defineProperty(us,"__esModule",{value:!0}),us.default=void 0;var ss={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};us.default=ss;var ds={};Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var cs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ds.default=cs;var ps={};Object.defineProperty(ps,"__esModule",{value:!0}),ps.default=void 0;var ms={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};ps.default=ms;var fs={};Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var bs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};fs.default=bs;var vs={};Object.defineProperty(vs,"__esModule",{value:!0}),vs.default=void 0;var ys={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};vs.default=ys;var hs={};Object.defineProperty(hs,"__esModule",{value:!0}),hs.default=void 0;var gs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};hs.default=gs;var Cs={};Object.defineProperty(Cs,"__esModule",{value:!0}),Cs.default=void 0;var Ps={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Cs.default=Ps,Object.defineProperty(Ji,"__esModule",{value:!0}),Ji.default=void 0;var qs=id(Yi),Es=id(eu),ws=id(ru),xs=id(au),Rs=id(lu),Os=id(uu),Ts=id(du),_s=id(pu),Ms=id(fu),As=id(vu),js=id(hu),Ss=id(Cu),Is=id(qu),Bs=id(wu),Ns=id(Ru),ks=id(Tu),Fs=id(Mu),Ls=id(ju),Us=id(Iu),Hs=id(Nu),Ds=id(Fu),zs=id(Uu),Vs=id(Du),$s=id(Vu),Ws=id(Wu),Gs=id(Qu),Qs=id(Xu),Ks=id(Yu),Xs=id(es),Js=id(rs),Ys=id(as),Zs=id(ls),ed=id(us),td=id(ds),rd=id(ps),nd=id(fs),ad=id(vs),od=id(hs),ld=id(Cs);function id(e){return e&&e.__esModule?e:{default:e}}var ud=[["doc-abstract",qs.default],["doc-acknowledgments",Es.default],["doc-afterword",ws.default],["doc-appendix",xs.default],["doc-backlink",Rs.default],["doc-biblioentry",Os.default],["doc-bibliography",Ts.default],["doc-biblioref",_s.default],["doc-chapter",Ms.default],["doc-colophon",As.default],["doc-conclusion",js.default],["doc-cover",Ss.default],["doc-credit",Is.default],["doc-credits",Bs.default],["doc-dedication",Ns.default],["doc-endnote",ks.default],["doc-endnotes",Fs.default],["doc-epigraph",Ls.default],["doc-epilogue",Us.default],["doc-errata",Hs.default],["doc-example",Ds.default],["doc-footnote",zs.default],["doc-foreword",Vs.default],["doc-glossary",$s.default],["doc-glossref",Ws.default],["doc-index",Gs.default],["doc-introduction",Qs.default],["doc-noteref",Ks.default],["doc-notice",Xs.default],["doc-pagebreak",Js.default],["doc-pagelist",Ys.default],["doc-part",Zs.default],["doc-preface",ed.default],["doc-prologue",td.default],["doc-pullquote",rd.default],["doc-qna",nd.default],["doc-subtitle",ad.default],["doc-tip",od.default],["doc-toc",ld.default]];Ji.default=ud;var sd={},dd={};Object.defineProperty(dd,"__esModule",{value:!0}),dd.default=void 0;var cd={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-object"}},{module:"ARIA",concept:{name:"img"}},{module:"ARIA",concept:{name:"article"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};dd.default=cd;var pd={};Object.defineProperty(pd,"__esModule",{value:!0}),pd.default=void 0;var md={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-document"}},{module:"ARIA",concept:{name:"group"}},{module:"ARIA",concept:{name:"img"}},{module:"GRAPHICS",concept:{name:"graphics-symbol"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};pd.default=md;var fd={};Object.defineProperty(fd,"__esModule",{value:!0}),fd.default=void 0;var bd={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};fd.default=bd,Object.defineProperty(sd,"__esModule",{value:!0}),sd.default=void 0;var vd=gd(dd),yd=gd(pd),hd=gd(fd);function gd(e){return e&&e.__esModule?e:{default:e}}var Cd=[["graphics-document",vd.default],["graphics-object",yd.default],["graphics-symbol",hd.default]];sd.default=Cd,Object.defineProperty(Or,"__esModule",{value:!0}),Or.default=void 0;var Pd=Rd(Tr),qd=Rd(fn),Ed=Rd(Ji),wd=Rd(sd),xd=Rd(ir);function Rd(e){return e&&e.__esModule?e:{default:e}}function Od(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Td(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Md(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}function _d(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||Md(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Md(e,t){if(e){if("string"==typeof e)return Ad(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ad(e,t):void 0}}function Ad(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var jd=[].concat(Pd.default,qd.default,Ed.default,wd.default);jd.forEach((function(e){var t,r=_d(e,2)[1],n=Td(r.superClass);try{for(n.s();!(t=n.n()).done;){var a,o=Td(t.value);try{var l=function(){var e=a.value,t=jd.find((function(t){return _d(t,1)[0]===e}));if(t)for(var n=t[1],o=0,l=Object.keys(n.props);o<l.length;o++){var i=l[o];Object.prototype.hasOwnProperty.call(r.props,i)||Object.assign(r.props,Od({},i,n.props[i]))}};for(o.s();!(a=o.n()).done;)l()}catch(e){o.e(e)}finally{o.f()}}}catch(e){n.e(e)}finally{n.f()}}));var Sd={entries:function(){return jd},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=Td(jd);try{for(n.s();!(t=n.n()).done;){var a=_d(t.value,2),o=a[0],l=a[1];e.call(r,l,o,jd)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=jd.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Sd.get(e)},keys:function(){return jd.map((function(e){return _d(e,1)[0]}))},values:function(){return jd.map((function(e){return _d(e,2)[1]}))}},Id=(0,xd.default)(Sd,Sd.entries());Or.default=Id;var Bd={},Nd={},kd=Object.prototype.hasOwnProperty;Nd.dequal=function e(t,r){var n,a;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((a=t.length)===r.length)for(;a--&&e(t[a],r[a]););return-1===a}if(!n||"object"==typeof t){for(n in a=0,t){if(kd.call(t,n)&&++a&&!kd.call(r,n))return!1;if(!(n in r)||!e(t[n],r[n]))return!1}return Object.keys(r).length===a}}return t!=t&&r!=r},Object.defineProperty(Bd,"__esModule",{value:!0}),Bd.default=void 0;var Fd=Nd,Ld=Hd(ir),Ud=Hd(Or);function Hd(e){return e&&e.__esModule?e:{default:e}}function Dd(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||zd(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zd(e,t){if(e){if("string"==typeof e)return Vd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Vd(e,t):void 0}}function Vd(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var $d=[],Wd=Ud.default.keys(),Gd=0;Gd<Wd.length;Gd++){var Qd=Wd[Gd],Kd=Ud.default.get(Qd);if(Kd)for(var Xd=[].concat(Kd.baseConcepts,Kd.relatedConcepts),Jd=0;Jd<Xd.length;Jd++){var Yd=Xd[Jd];"HTML"===Yd.module&&function(){var e=Yd.concept;if(e){var t,r=$d.find((function(t){return(0,Fd.dequal)(t,e)}));t=r?r[1]:[];for(var n=!0,a=0;a<t.length;a++)if(t[a]===Qd){n=!1;break}n&&t.push(Qd),$d.push([e,t])}}()}}var Zd={entries:function(){return $d},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=zd(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}($d);try{for(n.s();!(t=n.n()).done;){var a=Dd(t.value,2),o=a[0],l=a[1];e.call(r,l,o,$d)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=$d.find((function(t){return e.name===t[0].name&&(0,Fd.dequal)(e.attributes,t[0].attributes)}));return t&&t[1]},has:function(e){return!!Zd.get(e)},keys:function(){return $d.map((function(e){return Dd(e,1)[0]}))},values:function(){return $d.map((function(e){return Dd(e,2)[1]}))}},ec=(0,Ld.default)(Zd,Zd.entries());Bd.default=ec;var tc={};Object.defineProperty(tc,"__esModule",{value:!0}),tc.default=void 0;var rc=ac(ir),nc=ac(Or);function ac(e){return e&&e.__esModule?e:{default:e}}function oc(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||lc(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lc(e,t){if(e){if("string"==typeof e)return ic(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ic(e,t):void 0}}function ic(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var uc=[],sc=nc.default.keys(),dc=0;dc<sc.length;dc++){var cc=sc[dc],pc=nc.default.get(cc),mc=[];if(pc){for(var fc=[].concat(pc.baseConcepts,pc.relatedConcepts),bc=0;bc<fc.length;bc++){var vc=fc[bc];if("HTML"===vc.module){var yc=vc.concept;null!=yc&&mc.push(yc)}}mc.length>0&&uc.push([cc,mc])}}var hc={entries:function(){return uc},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=lc(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(uc);try{for(n.s();!(t=n.n()).done;){var a=oc(t.value,2),o=a[0],l=a[1];e.call(r,l,o,uc)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=uc.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!hc.get(e)},keys:function(){return uc.map((function(e){return oc(e,1)[0]}))},values:function(){return uc.map((function(e){return oc(e,2)[1]}))}},gc=(0,rc.default)(hc,hc.entries());tc.default=gc,Object.defineProperty(or,"__esModule",{value:!0});var Cc=or.roles=Sc=or.roleElements=or.elementRoles=or.dom=or.aria=void 0,Pc=Rc(lr),qc=Rc(gr),Ec=Rc(Or),wc=Rc(Bd),xc=Rc(tc);function Rc(e){return e&&e.__esModule?e:{default:e}}var Oc=Pc.default;or.aria=Oc;var Tc=qc.default;or.dom=Tc;var _c=Ec.default;Cc=or.roles=_c;var Mc=wc.default,Ac=or.elementRoles=Mc,jc=xc.default,Sc=or.roleElements=jc,Ic={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function a(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var o={compressToBase64:function(e){if(null==e)return"";var r=o._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:o._decompress(e.length,32,(function(r){return a(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":o._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:o._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=o.compress(e),r=new Uint8Array(2*t.length),n=0,a=t.length;n<a;n++){var l=t.charCodeAt(n);r[2*n]=l>>>8,r[2*n+1]=l%256}return r},decompressFromUint8Array:function(t){if(null==t)return o.decompress(t);for(var r=new Array(t.length/2),n=0,a=r.length;n<a;n++)r[n]=256*t[2*n]+t[2*n+1];var l=[];return r.forEach((function(t){l.push(e(t))})),o.decompress(l.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":o._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),o._decompress(e.length,32,(function(t){return a(r,e.charAt(t))})))},compress:function(t){return o._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,a,o,l={},i={},u="",s="",d="",c=2,p=3,m=2,f=[],b=0,v=0;for(o=0;o<e.length;o+=1)if(u=e.charAt(o),Object.prototype.hasOwnProperty.call(l,u)||(l[u]=p++,i[u]=!0),s=d+u,Object.prototype.hasOwnProperty.call(l,s))d=s;else{if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++),l[s]=p++,d=String(u)}if(""!==d){if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++)}for(a=2,n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;for(;;){if(b<<=1,v==t-1){f.push(r(b));break}v++}return f.join("")},decompress:function(e){return null==e?"":""==e?null:o._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var a,o,l,i,u,s,d,c=[],p=4,m=4,f=3,b="",v=[],y={val:n(0),position:r,index:1};for(a=0;a<3;a+=1)c[a]=a;for(l=0,u=Math.pow(2,2),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 2:return""}for(c[3]=d,o=d,v.push(d);;){if(y.index>t)return"";for(l=0,u=Math.pow(2,f),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(d=l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 2:return v.join("")}if(0==p&&(p=Math.pow(2,f),f++),c[d])b=c[d];else{if(d!==m)return null;b=o+o.charAt(0)}v.push(b),c[m++]=o+b.charAt(0),o=b,0==--p&&(p=Math.pow(2,f),f++)}}};return o}();null!=e?e.exports=t:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",(function(){return t}))}(Ic);var Bc=Ic.exports;function Nc(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const kc=(e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")},Fc=(e,t,r,n,a,o)=>e.map((e=>{const l="string"==typeof e?Lc(e,t):o(e,t,r,n,a);return""===l&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+l})).join(""),Lc=(e,t)=>{const r=t.colors.content;return r.open+Nc(e)+r.close},Uc=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+Nc(e)+"--\x3e"+r.close},Hc=(e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close},Dc=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},zc=3,Vc=8,$c=11,Wc=/^((HTML|SVG)\w*)?Element$/,Gc=e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is");return 1===r&&(Wc.test(t)||a)||r===zc&&"Text"===t||r===Vc&&"Comment"===t||r===$c&&"DocumentFragment"===t};function Qc(e){return e.nodeType===$c}function Kc(e){return{test:e=>{var t;return(null==e||null==(t=e.constructor)?void 0:t.name)&&Gc(e)},serialize:(t,r,n,a,o,l)=>{if(function(e){return e.nodeType===zc}(t))return Lc(t.data,r);if(function(e){return e.nodeType===Vc}(t))return Uc(t.data,r);const i=Qc(t)?"DocumentFragment":t.tagName.toLowerCase();return++a>r.maxDepth?Dc(i,r):Hc(i,kc(Qc(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),Qc(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,a,o,l),Fc(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,a,o,l),r,n)}}}let Xc=null,Jc=null,Yc=null;try{const e=module&&module.require;Jc=e.call(module,"fs").readFileSync,Yc=e.call(module,"@babel/code-frame").codeFrameColumns,Xc=e.call(module,"chalk")}catch{}function Zc(){if(!Jc||!Yc)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),a=n.split(":"),[o,l,i]=[a[0],parseInt(a[1],10),parseInt(a[2],10)];let u="";try{u=Jc(o,"utf-8")}catch{return""}const s=Yc(u,{start:{line:l,column:i}},{highlightCode:!0,linesBelow:0});return Xc.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}const ep=3;function tp(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function rp(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function np(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function ap(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const op=()=>{let e;try{var t;e=JSON.parse(null==(t=process)||null==(t=t.env)?void 0:t.COLORS)}catch(e){}return"boolean"==typeof e?e:"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node},{DOMCollection:lp}=Be,ip=1,up=8;function sp(e){return e.nodeType!==up&&(e.nodeType!==ip||!e.matches(fp().defaultIgnore))}function dp(e,t,r){if(void 0===r&&(r={}),e||(e=rp().body),"number"!=typeof t&&(t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:a=sp,...o}=r,l=Ie(e,{plugins:[Kc(a),lp],printFunctionName:!1,highlight:op(),...o});return void 0!==t&&e.outerHTML.length>t?l.slice(0,t)+"...":l}const cp=function(){const e=Zc();e?console.log(dp(...arguments)+"\n\n"+e):console.log(dp(...arguments))};let pp={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=dp(t),n=new Error([e,"Ignored nodes: comments, "+pp.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function mp(e){"function"==typeof e&&(e=e(pp)),pp={...pp,...e}}function fp(){return pp}const bp=["button","meter","output","progress","select","textarea","input"];function vp(e){return bp.includes(e.nodeName.toLowerCase())?"":e.nodeType===ep?e.textContent:Array.from(e.childNodes).map((e=>vp(e))).join("")}function yp(e){let t;return t="label"===e.tagName.toLowerCase()?vp(e):e.value||e.textContent,t}function hp(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function gp(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const a=t.getAttribute("aria-labelledby"),o=a?a.split(" "):[];return o.length?o.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:yp(r),formControl:null}:{content:"",formControl:null}})):Array.from(hp(t)).map((e=>({content:yp(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function Cp(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function Pp(e,t,r,n){if("string"!=typeof e)return!1;Cp(r);const a=n(e);return"string"==typeof r||"number"==typeof r?a.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(a,t):xp(r,a)}function qp(e,t,r,n){if("string"!=typeof e)return!1;Cp(r);const a=n(e);return r instanceof Function?r(a,t):r instanceof RegExp?xp(r,a):a===String(r)}function Ep(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function wp(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return Ep({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function xp(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function Rp(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>e.nodeType===ep&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}const Op=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;const a=-1!==n.indexOf("undefined"),o=-1!==n.indexOf("set");return void 0!==r?"["+t+'="'+r+'"]':a?":not(["+t+"])":o?"["+t+"]:not(["+t+'=""])':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[a,o]of e.entries())n=[...n,{match:r(a),roles:Array.from(o),specificity:t(a)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(Ac);function Tp(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function _p(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=Tp}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function Mp(e){for(const{match:t,roles:r}of Op)if(t(e))return[...r];return[]}function Ap(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===_p(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):Mp(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function jp(e,t){let{hidden:r,includeDescription:n}=t;const a=Ap(e,{hidden:r});return Object.entries(a).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const a="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+ar(e,{computedStyleSupportsPseudoElements:fp().computedStyleSupportsPseudoElements})+'":\n',r=dp(e.cloneNode(!1));if(n){return""+t+('Description "'+nr(e,{computedStyleSupportsPseudoElements:fp().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+a})).join("\n")}function Sp(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const Ip=Ep();function Bp(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function Np(e,t,r,n){let{variant:a,name:o}=n,l="";const i={},u=[["Role","TestId"].includes(e)?r:Bp(r)];o&&(i.name=Bp(o)),"Role"===e&&_p(t)&&(i.hidden=!0,l="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(i).length>0&&u.push(i);const s=a+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:a,warning:l,toString(){l&&console.warn(l);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function kp(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function Fp(e,t,r){var n,a;if(void 0===t&&(t="get"),e.matches(fp().defaultIgnore))return;const o=null!=(n=e.getAttribute("role"))?n:null==(a=Mp(e))?void 0:a[0];if("generic"!==o&&kp("Role",r,o))return Np("Role",e,o,{variant:t,name:ar(e,{computedStyleSupportsPseudoElements:fp().computedStyleSupportsPseudoElements})});const l=gp(document,e).map((e=>e.content)).join(" ");if(kp("LabelText",r,l))return Np("LabelText",e,l,{variant:t});const i=e.getAttribute("placeholder");if(kp("PlaceholderText",r,i))return Np("PlaceholderText",e,i,{variant:t});const u=Ip(Rp(e));if(kp("Text",r,u))return Np("Text",e,u,{variant:t});if(kp("DisplayValue",r,e.value))return Np("DisplayValue",e,Ip(e.value),{variant:t});const s=e.getAttribute("alt");if(kp("AltText",r,s))return Np("AltText",e,s,{variant:t});const d=e.getAttribute("title");if(kp("Title",r,d))return Np("Title",e,d,{variant:t});const c=e.getAttribute(fp().testIdAttribute);return kp("TestId",r,c)?Np("TestId",e,c,{variant:t}):void 0}function Lp(e,t){e.stack=t.stack.replace(t.message,e.message)}function Up(e,t){let{container:r=rp(),timeout:n=fp().asyncUtilTimeout,showOriginalStackTrace:a=fp().showOriginalStackTrace,stackTraceError:o,interval:l=50,onTimeout:i=(e=>(Object.defineProperty(e,"message",{value:fp().getElementError(e.message,r).message}),e)),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let d,c,p,m=!1,f="idle";const b=setTimeout((function(){let e;d?(e=d,a||"TestingLibraryElementError"!==e.name||Lp(e,o)):(e=new Error("Timed out in waitFor."),a||Lp(e,o)),y(i(e),null)}),n),v=tp();if(v){const{unstable_advanceTimersWrapper:e}=fp();for(g();!m;){if(!tp()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||Lp(e,o),void s(e)}if(await e((async()=>{jest.advanceTimersByTime(l)})),m)break;g()}}else{try{ap(r)}catch(e){return void s(e)}c=setInterval(h,l);const{MutationObserver:e}=np(r);p=new e(h),p.observe(r,u),g()}function y(e,r){m=!0,clearTimeout(b),v||(clearInterval(c),p.disconnect()),e?s(e):t(r)}function h(){if(tp()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||Lp(e,o),s(e)}return g()}function g(){if("pending"!==f)try{const t=function(e){try{return pp._disableExpensiveErrorDiagnostics=!0,e()}finally{pp._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(f="pending",t.then((e=>{f="resolved",y(null,e)}),(e=>{f="rejected",d=e}))):y(null,t)}catch(e){d=e}}}))}function Hp(e,t){const r=new Error("STACK_TRACE_MESSAGE");return fp().asyncWrapper((()=>Up(e,{stackTraceError:r,...t})))}function Dp(e,t){return fp().getElementError(e,t)}function zp(e,t){return Dp(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function Vp(e,t,r,n){let{exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===n?{}:n;const u=a?qp:Pp,s=wp({collapseWhitespace:o,trim:l,normalizer:i});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function $p(e,t,r,n){const a=Vp(e,t,r,n);if(a.length>1)throw zp("Found multiple elements by ["+e+"="+r+"]",t);return a[0]||null}function Wp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(l.length>1){const e=l.map((e=>Dp(null,e).message)).join("\n\n");throw zp(t(r,...a)+"\n\nHere are the matching elements:\n\n"+e,r)}return l[0]||null}}function Gp(e,t){return fp().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function Qp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(!l.length)throw fp().getElementError(t(r,...a),r);return l}}function Kp(e){return(t,r,n,a)=>Hp((()=>e(t,r,n)),{container:t,...a})}const Xp=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=fp().throwSuggestions}={}]=o.slice(-1);if(i&&u){const e=Fp(i,r);if(e&&!t.endsWith(e.queryName))throw Gp(e.toString(),n)}return i},Jp=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=fp().throwSuggestions}={}]=o.slice(-1);if(i.length&&u){const e=[...new Set(i.map((e=>{var t;return null==(t=Fp(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(Fp(i[0],r).queryName))throw Gp(e[0],n)}return i};function Yp(e,t,r){const n=Xp(Wp(e,t),e.name,"query"),a=Qp(e,r),o=Wp(a,t),l=Xp(o,e.name,"get");return[n,Jp(a,e.name.replace("query","get"),"getAll"),l,Kp(Jp(a,e.name,"findAll")),Kp(Xp(o,e.name,"find"))]}var Zp=Object.freeze({__proto__:null,getElementError:Dp,wrapAllByQueryWithSuggestion:Jp,wrapSingleQueryWithSuggestion:Xp,getMultipleElementsFoundError:zp,queryAllByAttribute:Vp,queryByAttribute:$p,makeSingleQuery:Wp,makeGetAllQuery:Qp,makeFindQuery:Kp,buildQueries:Yp});const em=function(e,t,r){let{exact:n=!0,trim:a,collapseWhitespace:o,normalizer:l}=void 0===r?{}:r;const i=n?qp:Pp,u=wp({collapseWhitespace:o,trim:a,normalizer:l}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:yp(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return i(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},tm=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===r?{}:r;ap(e);const u=a?qp:Pp,s=wp({collapseWhitespace:o,trim:l,normalizer:i}),d=Array.from(e.querySelectorAll("*")).filter((e=>hp(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,a)=>{const o=gp(e,a,{selector:n});o.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const l=o.filter((e=>Boolean(e.content))).map((e=>e.content));return u(l.join(" "),a,t,s)&&r.push(a),l.length>1&&l.forEach(((e,n)=>{u(e,a,t,s)&&r.push(a);const o=[...l];o.splice(n,1),o.length>1&&u(o.join(" "),a,t,s)&&r.push(a)})),r}),[]).concat(Vp("aria-label",e,t,{exact:a,normalizer:s}));return Array.from(new Set(d)).filter((e=>e.matches(n)))},rm=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),a=2;a<r;a++)n[a-2]=arguments[a];const o=tm(e,t,...n);if(!o.length){const r=em(e,t,...n);if(r.length){const n=r.map((t=>function(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}(e,t))).filter((e=>!!e));throw n.length?fp().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):fp().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw fp().getElementError("Unable to find a label with the text of: "+t,e)}return o};const nm=(e,t)=>"Found multiple elements with the text of: "+t,am=Xp(Wp(tm,nm),tm.name,"query"),om=Wp(rm,nm),lm=Kp(Jp(rm,rm.name,"findAll")),im=Kp(Xp(om,rm.name,"find")),um=Jp(rm,rm.name,"getAll"),sm=Xp(om,rm.name,"get"),dm=Jp(tm,tm.name,"queryAll"),cm=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return ap(t[0]),Vp("placeholder",...t)},pm=Jp(cm,cm.name,"queryAll"),[mm,fm,bm,vm,ym]=Yp(cm,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),hm=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,ignore:i=fp().defaultIgnore,normalizer:u}=void 0===r?{}:r;ap(e);const s=a?qp:Pp,d=wp({collapseWhitespace:o,trim:l,normalizer:u});let c=[];return"function"==typeof e.matches&&e.matches(n)&&(c=[e]),[...c,...Array.from(e.querySelectorAll(n))].filter((e=>!i||!e.matches(i))).filter((e=>s(Rp(e),e,t,d)))},gm=Jp(hm,hm.name,"queryAll"),[Cm,Pm,qm,Em,wm]=Yp(hm,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:a,normalizer:o,selector:l}=r,i=wp({collapseWhitespace:n,trim:a,normalizer:o})(t.toString());return"Unable to find an element with the text: "+(i!==t.toString()?i+" (normalized from '"+t+"')":t)+("*"!==(null!=l?l:"*")?", which matches selector '"+l+"'":"")+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),xm=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;ap(e);const i=n?qp:Pp,u=wp({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>i(Rp(e),e,t,u)))}return i(e.value,e,t,u)}))},Rm=Jp(xm,xm.name,"queryAll"),[Om,Tm,_m,Mm,Am]=Yp(xm,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),jm=/^(img|input|area|.+-.+)$/i,Sm=function(e,t,r){return void 0===r&&(r={}),ap(e),Vp("alt",e,t,r).filter((e=>jm.test(e.tagName)))},Im=Jp(Sm,Sm.name,"queryAll"),[Bm,Nm,km,Fm,Lm]=Yp(Sm,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),Um=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;ap(e);const i=n?qp:Pp,u=wp({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>i(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&i(Rp(e),e,t,u)))},Hm=Jp(Um,Um.name,"queryAll"),[Dm,zm,Vm,$m,Wm]=Yp(Um,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+".")),Gm=function(e,t,r){let{hidden:n=fp().defaultHidden,name:a,description:o,queryFallbacks:l=!1,selected:i,busy:u,checked:s,pressed:d,current:c,level:p,expanded:m,value:{now:f,min:b,max:v,text:y}={}}=void 0===r?{}:r;var h,g,C,P,q,E,w,x,R,O;if((ap(e),void 0!==i)&&void 0===(null==(h=Cc.get(t))?void 0:h.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==u&&void 0===(null==(g=Cc.get(t))?void 0:g.props["aria-busy"]))throw new Error('"aria-busy" is not supported on role "'+t+'".');if(void 0!==s&&void 0===(null==(C=Cc.get(t))?void 0:C.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==d&&void 0===(null==(P=Cc.get(t))?void 0:P.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==c&&void 0===(null==(q=Cc.get(t))?void 0:q.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==p&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==f&&void 0===(null==(E=Cc.get(t))?void 0:E.props["aria-valuenow"]))throw new Error('"aria-valuenow" is not supported on role "'+t+'".');if(void 0!==v&&void 0===(null==(w=Cc.get(t))?void 0:w.props["aria-valuemax"]))throw new Error('"aria-valuemax" is not supported on role "'+t+'".');if(void 0!==b&&void 0===(null==(x=Cc.get(t))?void 0:x.props["aria-valuemin"]))throw new Error('"aria-valuemin" is not supported on role "'+t+'".');if(void 0!==y&&void 0===(null==(R=Cc.get(t))?void 0:R.props["aria-valuetext"]))throw new Error('"aria-valuetext" is not supported on role "'+t+'".');if(void 0!==m&&void 0===(null==(O=Cc.get(t))?void 0:O.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const T=new WeakMap;function _(e){return T.has(e)||T.set(e,Tp(e)),T.get(e)}return Array.from(e.querySelectorAll(function(e){var t;const r='*[role~="'+e+'"]',n=null!=(t=Sc.get(e))?t:new Set,a=new Set(Array.from(n).map((e=>{let{name:t}=e;return t})));return[r].concat(Array.from(a)).join(",")}(t))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(l)return r.split(" ").filter(Boolean).some((e=>e===t));const[n]=r.split(" ");return n===t}return Mp(e).some((e=>e===t))})).filter((e=>{if(void 0!==i)return i===function(e){return"OPTION"===e.tagName?e.selected:Sp(e,"aria-selected")}(e);if(void 0!==u)return u===function(e){return"true"===e.getAttribute("aria-busy")}(e);if(void 0!==s)return s===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:Sp(e,"aria-checked")}(e);if(void 0!==d)return d===function(e){return Sp(e,"aria-pressed")}(e);if(void 0!==c)return c===function(e){var t,r;return null!=(t=null!=(r=Sp(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e);if(void 0!==m)return m===function(e){return Sp(e,"aria-expanded")}(e);if(void 0!==p)return p===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e);if(void 0!==f||void 0!==v||void 0!==b||void 0!==y){let r=!0;var t;if(void 0!==f&&r&&(r=f===function(e){const t=e.getAttribute("aria-valuenow");return null===t?void 0:+t}(e)),void 0!==v&&r&&(r=v===function(e){const t=e.getAttribute("aria-valuemax");return null===t?void 0:+t}(e)),void 0!==b&&r&&(r=b===function(e){const t=e.getAttribute("aria-valuemin");return null===t?void 0:+t}(e)),void 0!==y)r&&(r=qp(null!=(t=function(e){const t=e.getAttribute("aria-valuetext");return null===t?void 0:t}(e))?t:null,e,y,(e=>e)));return r}return!0})).filter((e=>void 0===a||qp(ar(e,{computedStyleSupportsPseudoElements:fp().computedStyleSupportsPseudoElements}),e,a,(e=>e)))).filter((e=>void 0===o||qp(nr(e,{computedStyleSupportsPseudoElements:fp().computedStyleSupportsPseudoElements}),e,o,(e=>e)))).filter((e=>!1!==n||!1===_p(e,{isSubtreeInaccessible:_})))};const Qm=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},Km=Jp(Gm,Gm.name,"queryAll"),[Xm,Jm,Ym,Zm,ef]=Yp(Gm,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+Qm(n)}),(function(e,t,r){let{hidden:n=fp().defaultHidden,name:a,description:o}=void 0===r?{}:r;if(fp()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+Qm(a);let l,i="";Array.from(e.children).forEach((e=>{i+=jp(e,{hidden:n,includeDescription:void 0!==o})})),l=0===i.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+i.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===a?"":"string"==typeof a?' and name "'+a+'"':" and name `"+a+"`";let s="";return s=void 0===o?"":"string"==typeof o?' and description "'+o+'"':" and description `"+o+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+l).trim()})),tf=()=>fp().testIdAttribute,rf=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return ap(t[0]),Vp(tf(),...t)},nf=Jp(rf,rf.name,"queryAll"),[af,of,lf,uf,sf]=Yp(rf,((e,t)=>"Found multiple elements by: ["+tf()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+tf()+'="'+t+'"]'));var df=Object.freeze({__proto__:null,queryAllByLabelText:dm,queryByLabelText:am,getAllByLabelText:um,getByLabelText:sm,findAllByLabelText:lm,findByLabelText:im,queryByPlaceholderText:mm,queryAllByPlaceholderText:pm,getByPlaceholderText:bm,getAllByPlaceholderText:fm,findAllByPlaceholderText:vm,findByPlaceholderText:ym,queryByText:Cm,queryAllByText:gm,getByText:qm,getAllByText:Pm,findAllByText:Em,findByText:wm,queryByDisplayValue:Om,queryAllByDisplayValue:Rm,getByDisplayValue:_m,getAllByDisplayValue:Tm,findAllByDisplayValue:Mm,findByDisplayValue:Am,queryByAltText:Bm,queryAllByAltText:Im,getByAltText:km,getAllByAltText:Nm,findAllByAltText:Fm,findByAltText:Lm,queryByTitle:Dm,queryAllByTitle:Hm,getByTitle:Vm,getAllByTitle:zm,findAllByTitle:$m,findByTitle:Wm,queryByRole:Xm,queryAllByRole:Km,getAllByRole:Jm,getByRole:Ym,findAllByRole:Zm,findByRole:ef,queryByTestId:af,queryAllByTestId:nf,getByTestId:lf,getAllByTestId:of,findAllByTestId:uf,findByTestId:sf});function cf(e,t,r){return void 0===t&&(t=df),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const a=t[n];return r[n]=a.bind(null,e),r}),r)}const pf=e=>!e||Array.isArray(e)&&!e.length;function mf(e){if(pf(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const ff={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}},offline:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},online:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},pageHide:{EventType:"PageTransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},pageShow:{EventType:"PageTransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}}},bf={doubleClick:"dblClick"};function vf(e,t){return fp().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function yf(e,t,r,n){let{EventType:a="Event",defaultInit:o={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const l={...o,...r},{target:{value:i,files:u,...s}={}}=l;void 0!==i&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:a}=Object.getOwnPropertyDescriptor(n,"value")||{};if(a&&r!==a)a.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,i),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const d=np(t),c=d[a]||d.Event;let p;if("function"==typeof c)p=new c(e,l);else{p=d.document.createEvent(a);const{bubbles:t,cancelable:r,detail:n,...o}=l;p.initEvent(e,t,r,n),Object.keys(o).forEach((e=>{p[e]=o[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=l[e];"object"==typeof t&&("function"==typeof d.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new d.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}function hf(e){return"https://testing-playground.com/#markup="+(t=e,Bc.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}Object.keys(ff).forEach((e=>{const{EventType:t,defaultInit:r}=ff[e],n=e.toLowerCase();yf[e]=(e,a)=>yf(n,e,a,{EventType:t,defaultInit:r}),vf[e]=(t,r)=>vf(t,yf[e](t,r))})),Object.keys(bf).forEach((e=>{const t=bf[e];vf[e]=function(){return vf[t](...arguments)}}));const gf={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>cp(e,t,r))):cp(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=rp().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=hf(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},Cf="undefined"!=typeof document&&document.body?cf(document.body,df,gf):Object.keys(df).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),gf),Pf=function(){return vf(...arguments)};Object.keys(vf).forEach((e=>{Pf[e]=function(){return vf[e](...arguments)}}));const qf=Pf.mouseEnter,Ef=Pf.mouseLeave;Pf.mouseEnter=function(){return qf(...arguments),Pf.mouseOver(...arguments)},Pf.mouseLeave=function(){return Ef(...arguments),Pf.mouseOut(...arguments)};const wf=Pf.pointerEnter,xf=Pf.pointerLeave;Pf.pointerEnter=function(){return wf(...arguments),Pf.pointerOver(...arguments)},Pf.pointerLeave=function(){return xf(...arguments),Pf.pointerOut(...arguments)};const Rf=Pf.select;Pf.select=(e,t)=>{Rf(e,t),e.focus(),Pf.keyUp(e,t)};const Of=Pf.blur,Tf=Pf.focus;Pf.blur=function(){return Pf.focusOut(...arguments),Of(...arguments)},Pf.focus=function(){return Pf.focusIn(...arguments),Tf(...arguments)};let _f={reactStrictMode:!1};function Mf(){return{...fp(),..._f}}mp({unstable_advanceTimersWrapper:e=>v(e),asyncWrapper:async e=>{const t=b();f(!1);try{const t=await e();return await new Promise((e=>{setTimeout((()=>{e()}),0),"undefined"==typeof jest||null===jest||!0!==setTimeout._isMockFunction&&!Object.prototype.hasOwnProperty.call(setTimeout,"clock")||jest.advanceTimersByTime(0)})),t}finally{f(t)}},eventWrapper:e=>{let t;return v((()=>{t=e()})),t}});const Af=new Set,jf=[];function Sf(e){return Mf().reactStrictMode?u.createElement(u.StrictMode,null,e):e}function If(e,t){return t?u.createElement(t,null,e):e}function Bf(e,t){let r,{hydrate:n,ui:a,wrapper:o}=t;return n?v((()=>{r=c.hydrateRoot(e,Sf(If(a,o)))})):r=c.createRoot(e),{hydrate(){if(!n)throw new Error("Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.")},render(e){r.render(e)},unmount(){r.unmount()}}}function Nf(e){return{hydrate(t){d.default.hydrate(t,e)},render(t){d.default.render(t,e)},unmount(){d.default.unmountComponentAtNode(e)}}}function kf(e,t){let{baseElement:r,container:n,hydrate:a,queries:o,root:l,wrapper:i}=t;return v((()=>{a?l.hydrate(Sf(If(e,i)),n):l.render(Sf(If(e,i)),n)})),{container:n,baseElement:r,debug:function(e,t,n){return void 0===e&&(e=r),Array.isArray(e)?e.forEach((e=>console.log(dp(e,t,n)))):console.log(dp(e,t,n))},unmount:()=>{v((()=>{l.unmount()}))},rerender:e=>{kf(e,{container:n,baseElement:r,root:l,wrapper:i})},asFragment:()=>{if("function"==typeof document.createRange)return document.createRange().createContextualFragment(n.innerHTML);{const e=document.createElement("template");return e.innerHTML=n.innerHTML,e.content}},...cf(r,o)}}function Ff(e,t){let r,{container:n,baseElement:a=n,legacyRoot:o=!1,queries:l,hydrate:i=!1,wrapper:u}=void 0===t?{}:t;if(o&&"function"!=typeof d.default.render){const e=new Error("`legacyRoot: true` is not supported in this version of React. If your app runs React 19 or later, you should remove this flag. If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.");throw Error.captureStackTrace(e,Ff),e}if(a||(a=document.body),n||(n=a.appendChild(document.createElement("div"))),Af.has(n))jf.forEach((e=>{e.container===n&&(r=e.root)}));else{r=(o?Nf:Bf)(n,{hydrate:i,ui:e,wrapper:u}),jf.push({container:n,root:r}),Af.add(n)}return kf(e,{container:n,baseElement:a,queries:l,hydrate:i,wrapper:u,root:r})}function Lf(){jf.forEach((e=>{let{root:t,container:r}=e;v((()=>{t.unmount()})),r.parentNode===document.body&&document.body.removeChild(r)})),jf.length=0,Af.clear()}if(("undefined"==typeof process||!process.env?.RTL_SKIP_AUTO_CLEANUP)&&("function"==typeof afterEach?afterEach((()=>{Lf()})):"function"==typeof teardown&&teardown((()=>{Lf()})),"function"==typeof beforeAll&&"function"==typeof afterAll)){let e=b();beforeAll((()=>{e=b(),f(!0)})),afterAll((()=>{f(e)}))}e.act=v,e.buildQueries=Yp,e.cleanup=Lf,e.configure=function(e){"function"==typeof e&&(e=e(Mf()));const{reactStrictMode:t,...r}=e;mp(r),_f={..._f,reactStrictMode:t}},e.createEvent=yf,e.findAllByAltText=Fm,e.findAllByDisplayValue=Mm,e.findAllByLabelText=lm,e.findAllByPlaceholderText=vm,e.findAllByRole=Zm,e.findAllByTestId=uf,e.findAllByText=Em,e.findAllByTitle=$m,e.findByAltText=Lm,e.findByDisplayValue=Am,e.findByLabelText=im,e.findByPlaceholderText=ym,e.findByRole=ef,e.findByTestId=sf,e.findByText=wm,e.findByTitle=Wm,e.fireEvent=Pf,e.getAllByAltText=Nm,e.getAllByDisplayValue=Tm,e.getAllByLabelText=um,e.getAllByPlaceholderText=fm,e.getAllByRole=Jm,e.getAllByTestId=of,e.getAllByText=Pm,e.getAllByTitle=zm,e.getByAltText=km,e.getByDisplayValue=_m,e.getByLabelText=sm,e.getByPlaceholderText=bm,e.getByRole=Ym,e.getByTestId=lf,e.getByText=qm,e.getByTitle=Vm,e.getConfig=Mf,e.getDefaultNormalizer=Ep,e.getElementError=Dp,e.getMultipleElementsFoundError=zp,e.getNodeText=Rp,e.getQueriesForElement=cf,e.getRoles=Ap,e.getSuggestedQuery=Fp,e.isInaccessible=_p,e.logDOM=cp,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(jp(e,{hidden:r}))},e.makeFindQuery=Kp,e.makeGetAllQuery=Qp,e.makeSingleQuery=Wp,e.prettyDOM=dp,e.prettyFormat=Pt,e.queries=df,e.queryAllByAltText=Im,e.queryAllByAttribute=Vp,e.queryAllByDisplayValue=Rm,e.queryAllByLabelText=dm,e.queryAllByPlaceholderText=pm,e.queryAllByRole=Km,e.queryAllByTestId=nf,e.queryAllByText=gm,e.queryAllByTitle=Hm,e.queryByAltText=Bm,e.queryByAttribute=$p,e.queryByDisplayValue=Om,e.queryByLabelText=am,e.queryByPlaceholderText=mm,e.queryByRole=Xm,e.queryByTestId=af,e.queryByText=Cm,e.queryByTitle=Dm,e.queryHelpers=Zp,e.render=Ff,e.renderHook=function e(t,r){void 0===r&&(r={});const{initialProps:n,...a}=r;if(a.legacyRoot&&"function"!=typeof d.default.render){const t=new Error("`legacyRoot: true` is not supported in this version of React. If your app runs React 19 or later, you should remove this flag. If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.");throw Error.captureStackTrace(t,e),t}const o=u.createRef();function l(e){let{renderCallbackProps:r}=e;const n=t(r);return u.useEffect((()=>{o.current=n})),null}const{rerender:i,unmount:s}=Ff(u.createElement(l,{renderCallbackProps:n}),a);return{result:o,rerender:function(e){return i(u.createElement(l,{renderCallbackProps:e}))},unmount:s}},e.screen=Cf,e.waitFor=Hp,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){mf(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return mf(e()),Hp((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!pf(t))throw r}),t)},e.within=cf,e.wrapAllByQueryWithSuggestion=Jp,e.wrapSingleQueryWithSuggestion=Xp,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react.umd.min.js.map
