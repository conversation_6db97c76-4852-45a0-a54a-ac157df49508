!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("react-dom/client"),require("react-dom/test-utils")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","react-dom/client","react-dom/test-utils"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryReact={},e.React,e.ReactDOM,e.ReactDOMClient,e.ReactTestUtils)}(this,(function(e,t,r,n,a){"use strict";function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}function i(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var u=l(t),s=o(r),d=l(n),c=l(a),p={},m={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>`[${38+e};5;${t}m`},r=function(e){return void 0===e&&(e=0),(t,r,n)=>`[${38+e};2;${t};${r};${n}m`};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,a]of Object.entries(r))n[t]={open:`[${a[0]}m`,close:`[${a[1]}m`},r[t]=n[t],e.set(a[0],a[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(m);var f={};Object.defineProperty(f,"__esModule",{value:!0}),f.printIteratorEntries=function(e,t,r,n,a,o,l){void 0===l&&(l=": ");let i="",u=e.next();if(!u.done){i+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){i+=s+o(u.value[0],t,s,n,a)+l+o(u.value[1],t,s,n,a),u=e.next(),u.done?t.min||(i+=","):i+=","+t.spacingInner}i+=t.spacingOuter+r}return i},f.printIteratorValues=function(e,t,r,n,a,o){let l="",i=e.next();if(!i.done){l+=t.spacingOuter;const u=r+t.indent;for(;!i.done;)l+=u+o(i.value,t,u,n,a),i=e.next(),i.done?t.min||(l+=","):l+=","+t.spacingInner;l+=t.spacingOuter+r}return l},f.printListItems=function(e,t,r,n,a,o){let l="";if(e.length){l+=t.spacingOuter;const i=r+t.indent;for(let r=0;r<e.length;r++)l+=i,r in e&&(l+=o(e[r],t,i,n,a)),r<e.length-1?l+=","+t.spacingInner:t.min||(l+=",");l+=t.spacingOuter+r}return l},f.printObjectProperties=function(e,t,r,n,a,o){let l="";const i=b(e,t.compareKeys);if(i.length){l+=t.spacingOuter;const u=r+t.indent;for(let r=0;r<i.length;r++){const s=i[r];l+=u+o(s,t,u,n,a)+": "+o(e[s],t,u,n,a),r<i.length-1?l+=","+t.spacingInner:t.min||(l+=",")}l+=t.spacingOuter+r}return l};const b=(e,t)=>{const r=Object.keys(e).sort(t);return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)})),r};var v={};Object.defineProperty(v,"__esModule",{value:!0}),v.test=v.serialize=v.default=void 0;var y=f,h="undefined"!=typeof globalThis?globalThis:void 0!==h?h:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),g=h["jest-symbol-do-not-touch"]||h.Symbol;const C="function"==typeof g&&g.for?g.for("jest.asymmetricMatcher"):1267621,q=" ",P=(e,t,r,n,a,o)=>{const l=e.toString();return"ArrayContaining"===l||"ArrayNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+q+"["+(0,y.printListItems)(e.sample,t,r,n,a,o)+"]":"ObjectContaining"===l||"ObjectNotContaining"===l?++n>t.maxDepth?"["+l+"]":l+q+"{"+(0,y.printObjectProperties)(e.sample,t,r,n,a,o)+"}":"StringMatching"===l||"StringNotMatching"===l||"StringContaining"===l||"StringNotContaining"===l?l+q+o(e.sample,t,r,n,a):e.toAsymmetricMatcher()};v.serialize=P;const E=e=>e&&e.$$typeof===C;v.test=E;var w={serialize:P,test:E};v.default=w;var x={};Object.defineProperty(x,"__esModule",{value:!0}),x.test=x.serialize=x.default=void 0;var R=T((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),O=T(m.exports);function T(e){return e&&e.__esModule?e:{default:e}}const _=e=>"string"==typeof e&&!!e.match((0,R.default)());x.test=_;const M=(e,t,r,n,a,o)=>o(e.replace((0,R.default)(),(e=>{switch(e){case O.default.red.close:case O.default.green.close:case O.default.cyan.close:case O.default.gray.close:case O.default.white.close:case O.default.yellow.close:case O.default.bgRed.close:case O.default.bgGreen.close:case O.default.bgYellow.close:case O.default.inverse.close:case O.default.dim.close:case O.default.bold.close:case O.default.reset.open:case O.default.reset.close:return"</>";case O.default.red.open:return"<red>";case O.default.green.open:return"<green>";case O.default.cyan.open:return"<cyan>";case O.default.gray.open:return"<gray>";case O.default.white.open:return"<white>";case O.default.yellow.open:return"<yellow>";case O.default.bgRed.open:return"<bgRed>";case O.default.bgGreen.open:return"<bgGreen>";case O.default.bgYellow.open:return"<bgYellow>";case O.default.inverse.open:return"<inverse>";case O.default.dim.open:return"<dim>";case O.default.bold.open:return"<bold>";default:return""}})),t,r,n,a);x.serialize=M;var A={serialize:M,test:_};x.default=A;var j={};Object.defineProperty(j,"__esModule",{value:!0}),j.test=j.serialize=j.default=void 0;var S=f;const I=["DOMStringMap","NamedNodeMap"],B=/^(HTML\w*Collection|NodeList)$/,N=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==I.indexOf(t)||B.test(t));var t};j.test=N;const k=(e,t,r,n,a,o)=>{const l=e.constructor.name;return++n>t.maxDepth?"["+l+"]":(t.min?"":l+" ")+(-1!==I.indexOf(l)?"{"+(0,S.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,a,o)+"}":"["+(0,S.printListItems)(Array.from(e),t,r,n,a,o)+"]")};j.serialize=k;var F={serialize:k,test:N};j.default=F;var L={},H={},U={};Object.defineProperty(U,"__esModule",{value:!0}),U.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty(H,"__esModule",{value:!0}),H.printText=H.printProps=H.printElementAsLeaf=H.printElement=H.printComment=H.printChildren=void 0;var D,z=(D=U)&&D.__esModule?D:{default:D};H.printProps=(e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")};H.printChildren=(e,t,r,n,a,o)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?V(e,t):o(e,t,r,n,a)))).join("");const V=(e,t)=>{const r=t.colors.content;return r.open+(0,z.default)(e)+r.close};H.printText=V;H.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,z.default)(e)+"--\x3e"+r.close};H.printElement=(e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close};H.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty(L,"__esModule",{value:!0}),L.test=L.serialize=L.default=void 0;var $=H;const W=/^((HTML|SVG)\w*)?Element$/,G=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&(W.test(t)||a)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function Q(e){return 11===e.nodeType}L.test=G;const X=(e,t,r,n,a,o)=>{if(function(e){return 3===e.nodeType}(e))return(0,$.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,$.printComment)(e.data,t);const l=Q(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,$.printElementAsLeaf)(l,t):(0,$.printElement)(l,(0,$.printProps)(Q(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),Q(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,a,o),(0,$.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,a,o),t,r)};L.serialize=X;var J={serialize:X,test:G};L.default=J;var K={};Object.defineProperty(K,"__esModule",{value:!0}),K.test=K.serialize=K.default=void 0;var Y=f;const Z="@@__IMMUTABLE_ORDERED__@@",ee=e=>"Immutable."+e,te=e=>"["+e+"]",re=" ";const ne=(e,t,r,n,a,o,l)=>++n>t.maxDepth?te(ee(l)):ee(l)+re+"["+(0,Y.printIteratorValues)(e.values(),t,r,n,a,o)+"]",ae=(e,t,r,n,a,o)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,a,o,l)=>++n>t.maxDepth?te(ee(l)):ee(l)+re+"{"+(0,Y.printIteratorEntries)(e.entries(),t,r,n,a,o)+"}")(e,t,r,n,a,o,e[Z]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?ne(e,t,r,n,a,o,"List"):e["@@__IMMUTABLE_SET__@@"]?ne(e,t,r,n,a,o,e[Z]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?ne(e,t,r,n,a,o,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,a,o)=>{const l=ee("Seq");return++n>t.maxDepth?te(l):e["@@__IMMUTABLE_KEYED__@@"]?l+re+"{"+(e._iter||e._object?(0,Y.printIteratorEntries)(e.entries(),t,r,n,a,o):"…")+"}":l+re+"["+(e._iter||e._array||e._collection||e._iterable?(0,Y.printIteratorValues)(e.values(),t,r,n,a,o):"…")+"]"})(e,t,r,n,a,o):((e,t,r,n,a,o)=>{const l=ee(e._name||"Record");return++n>t.maxDepth?te(l):l+re+"{"+(0,Y.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,a,o)+"}"})(e,t,r,n,a,o);K.serialize=ae;const oe=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);K.test=oe;var le={serialize:ae,test:oe};K.default=le;var ie,ue={},se={exports:{}},de={};!function(e){e.exports=function(){if(ie)return de;ie=1;var e=60103,t=60106,r=60107,n=60108,a=60114,o=60109,l=60110,i=60112,u=60113,s=60120,d=60115,c=60116,p=60121,m=60122,f=60117,b=60129,v=60131;if("function"==typeof Symbol&&Symbol.for){var y=Symbol.for;e=y("react.element"),t=y("react.portal"),r=y("react.fragment"),n=y("react.strict_mode"),a=y("react.profiler"),o=y("react.provider"),l=y("react.context"),i=y("react.forward_ref"),u=y("react.suspense"),s=y("react.suspense_list"),d=y("react.memo"),c=y("react.lazy"),p=y("react.block"),m=y("react.server.block"),f=y("react.fundamental"),b=y("react.debug_trace_mode"),v=y("react.legacy_hidden")}function h(p){if("object"==typeof p&&null!==p){var m=p.$$typeof;switch(m){case e:switch(p=p.type){case r:case a:case n:case u:case s:return p;default:switch(p=p&&p.$$typeof){case l:case i:case c:case d:case o:return p;default:return m}}case t:return m}}}var g=o,C=e,q=i,P=r,E=c,w=d,x=t,R=a,O=n,T=u;return de.ContextConsumer=l,de.ContextProvider=g,de.Element=C,de.ForwardRef=q,de.Fragment=P,de.Lazy=E,de.Memo=w,de.Portal=x,de.Profiler=R,de.StrictMode=O,de.Suspense=T,de.isAsyncMode=function(){return!1},de.isConcurrentMode=function(){return!1},de.isContextConsumer=function(e){return h(e)===l},de.isContextProvider=function(e){return h(e)===o},de.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===e},de.isForwardRef=function(e){return h(e)===i},de.isFragment=function(e){return h(e)===r},de.isLazy=function(e){return h(e)===c},de.isMemo=function(e){return h(e)===d},de.isPortal=function(e){return h(e)===t},de.isProfiler=function(e){return h(e)===a},de.isStrictMode=function(e){return h(e)===n},de.isSuspense=function(e){return h(e)===u},de.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===a||e===b||e===n||e===u||e===s||e===v||"object"==typeof e&&null!==e&&(e.$$typeof===c||e.$$typeof===d||e.$$typeof===o||e.$$typeof===l||e.$$typeof===i||e.$$typeof===f||e.$$typeof===p||e[0]===m)},de.typeOf=h,de}()}(se),Object.defineProperty(ue,"__esModule",{value:!0}),ue.test=ue.serialize=ue.default=void 0;var ce=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=me(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var l=a?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(n,o,l):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(se.exports),pe=H;function me(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(me=function(e){return e?r:t})(e)}const fe=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{fe(e,t)})):null!=e&&!1!==e&&t.push(e),t},be=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(ce.isFragment(e))return"React.Fragment";if(ce.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(ce.isContextProvider(e))return"Context.Provider";if(ce.isContextConsumer(e))return"Context.Consumer";if(ce.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(ce.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},ve=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,pe.printElementAsLeaf)(be(e),t):(0,pe.printElement)(be(e),(0,pe.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,a,o),(0,pe.printChildren)(fe(e.props.children),t,r+t.indent,n,a,o),t,r);ue.serialize=ve;const ye=e=>null!=e&&ce.isElement(e);ue.test=ye;var he={serialize:ve,test:ye};ue.default=he;var ge={};Object.defineProperty(ge,"__esModule",{value:!0}),ge.test=ge.serialize=ge.default=void 0;var Ce=H,qe="undefined"!=typeof globalThis?globalThis:void 0!==qe?qe:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),Pe=qe["jest-symbol-do-not-touch"]||qe.Symbol;const Ee="function"==typeof Pe&&Pe.for?Pe.for("react.test.json"):245830487,we=(e,t,r,n,a,o)=>++n>t.maxDepth?(0,Ce.printElementAsLeaf)(e.type,t):(0,Ce.printElement)(e.type,e.props?(0,Ce.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,a,o):"",e.children?(0,Ce.printChildren)(e.children,t,r+t.indent,n,a,o):"",t,r);ge.serialize=we;const xe=e=>e&&e.$$typeof===Ee;ge.test=xe;var Re={serialize:we,test:xe};ge.default=Re,Object.defineProperty(p,"__esModule",{value:!0});var Oe=p.default=p.DEFAULT_OPTIONS=void 0,Te=p.format=pt,_e=p.plugins=void 0,Me=Le(m.exports),Ae=f,je=Le(v),Se=Le(x),Ie=Le(j),Be=Le(L),Ne=Le(K),ke=Le(ue),Fe=Le(ge);function Le(e){return e&&e.__esModule?e:{default:e}}const He=Object.prototype.toString,Ue=Date.prototype.toISOString,De=Error.prototype.toString,ze=RegExp.prototype.toString,Ve=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",$e=e=>"undefined"!=typeof window&&e===window,We=/^Symbol\((.*)\)(.*)$/,Ge=/\n/gi;class Qe extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function Xe(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function Je(e){return String(e).replace(We,"Symbol($1)")}function Ke(e){return"["+De.call(e)+"]"}function Ye(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const a=typeof e;if("number"===a)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===a)return function(e){return String(`${e}n`)}(e);if("string"===a)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===a)return Xe(e,t);if("symbol"===a)return Je(e);const o=He.call(e);return"[object WeakMap]"===o?"WeakMap {}":"[object WeakSet]"===o?"WeakSet {}":"[object Function]"===o||"[object GeneratorFunction]"===o?Xe(e,t):"[object Symbol]"===o?Je(e):"[object Date]"===o?isNaN(+e)?"Date { NaN }":Ue.call(e):"[object Error]"===o?Ke(e):"[object RegExp]"===o?r?ze.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):ze.call(e):e instanceof Error?Ke(e):null}function Ze(e,t,r,n,a,o){if(-1!==a.indexOf(e))return"[Circular]";(a=a.slice()).push(e);const l=++n>t.maxDepth,i=t.min;if(t.callToJSON&&!l&&e.toJSON&&"function"==typeof e.toJSON&&!o)return rt(e.toJSON(),t,r,n,a,!0);const u=He.call(e);return"[object Arguments]"===u?l?"[Arguments]":(i?"":"Arguments ")+"["+(0,Ae.printListItems)(e,t,r,n,a,rt)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?l?"["+e.constructor.name+"]":(i?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,Ae.printListItems)(e,t,r,n,a,rt)+"]":"[object Map]"===u?l?"[Map]":"Map {"+(0,Ae.printIteratorEntries)(e.entries(),t,r,n,a,rt," => ")+"}":"[object Set]"===u?l?"[Set]":"Set {"+(0,Ae.printIteratorValues)(e.values(),t,r,n,a,rt)+"}":l||$e(e)?"["+Ve(e)+"]":(i?"":t.printBasicPrototype||"Object"!==Ve(e)?Ve(e)+" ":"")+"{"+(0,Ae.printObjectProperties)(e,t,r,n,a,rt)+"}"}function et(e,t,r,n,a,o){let l;try{l=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,a,o,rt):e.print(t,(e=>rt(e,r,n,a,o)),(e=>{const t=n+r.indent;return t+e.replace(Ge,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new Qe(e.message,e.stack)}if("string"!=typeof l)throw new Error(`pretty-format: Plugin must return type "string" but instead returned "${typeof l}".`);return l}function tt(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new Qe(e.message,e.stack)}return null}function rt(e,t,r,n,a,o){const l=tt(t.plugins,e);if(null!==l)return et(l,e,t,r,n,a);const i=Ye(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==i?i:Ze(e,t,r,n,a,o)}const nt={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},at=Object.keys(nt),ot={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:nt};var lt=p.DEFAULT_OPTIONS=ot;const it=e=>at.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:nt[r],a=n&&Me.default[n];if(!a||"string"!=typeof a.close||"string"!=typeof a.open)throw new Error(`pretty-format: Option "theme" has a key "${r}" whose value "${n}" is undefined in ansi-styles.`);return t[r]=a,t}),Object.create(null)),ut=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:ot.printFunctionName,st=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:ot.escapeRegex,dt=e=>e&&void 0!==e.escapeString?e.escapeString:ot.escapeString,ct=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:ot.callToJSON,colors:e&&e.highlight?it(e):at.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:ot.compareKeys,escapeRegex:st(e),escapeString:dt(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:ot.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:ot.maxDepth,min:e&&void 0!==e.min?e.min:ot.min,plugins:e&&void 0!==e.plugins?e.plugins:ot.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:ut(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function pt(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!ot.hasOwnProperty(e))throw new Error(`pretty-format: Unknown option "${e}".`)})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error(`pretty-format: Option "theme" must be of type "object" but instead received "${typeof e.theme}".`)}}(t),t.plugins)){const r=tt(t.plugins,e);if(null!==r)return et(r,e,ct(t),"",0,[])}const r=Ye(e,ut(t),st(t),dt(t));return null!==r?r:Ze(e,ct(t),"",0,[])}const mt={AsymmetricMatcher:je.default,ConvertAnsi:Se.default,DOMCollection:Ie.default,DOMElement:Be.default,Immutable:Ne.default,ReactElement:ke.default,ReactTestComponent:Fe.default};_e=p.plugins=mt;var ft=pt;Oe=p.default=ft;var bt=i({__proto__:null,get DEFAULT_OPTIONS(){return lt},format:Te,get plugins(){return _e},get default(){return Oe}},[p]),vt=Object.prototype.toString;function yt(e){return"function"==typeof e||"[object Function]"===vt.call(e)}var ht=Math.pow(2,53)-1;function gt(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),ht)}function Ct(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!yt(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var a,o=gt(n.length),l=yt(r)?Object(new r(o)):new Array(o),i=0;i<o;)a=n[i],l[i]=t?t(a,i):a,i+=1;return l.length=o,l}function qt(e){return qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qt(e)}function Pt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Et(n.key),n)}}function Et(e){var t=function(e,t){if("object"!==qt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==qt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===qt(t)?t:String(t)}var wt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t,r){(t=Et(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&Pt(t.prototype,r),n&&Pt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),xt="undefined"==typeof Set?Set:wt;function Rt(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var Ot={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},Tt={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function _t(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=Tt[t])&&void 0!==n&&n.has(r))}))}(e,t)}function Mt(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=Ot[Rt(e)];if(void 0!==t)return t;switch(Rt(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||_t(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||_t(e,r||""))return r}return t}function At(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function jt(e){return At(e)&&"caption"===Rt(e)}function St(e){return At(e)&&"input"===Rt(e)}function It(e){return At(e)&&"legend"===Rt(e)}function Bt(e){return function(e){return At(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===Rt(e)}function Nt(e,t){if(At(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function kt(e,t){return!!At(e)&&-1!==t.indexOf(Mt(e))}function Ft(e,t){if(!At(e))return!1;if("range"===t)return kt(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function Lt(e,t){var r=Ct(e.querySelectorAll(t));return Nt(e,"aria-owns").forEach((function(e){r.push.apply(r,Ct(e.querySelectorAll(t)))})),r}function Ht(e){return At(t=e)&&"select"===Rt(t)?e.selectedOptions||Lt(e,"[selected]"):Lt(e,'[aria-selected="true"]');var t}function Ut(e){return St(e)||At(t=e)&&"textarea"===Rt(t)?e.value:e.textContent||"";var t}function Dt(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function zt(e){var t=Rt(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function Vt(e){if(zt(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&At(e)){var r=Vt(e);null!==r&&(t=r)}})),t}function $t(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):Vt(e)}function Wt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new xt,n=function(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}(e),a=t.compute,o=void 0===a?"name":a,l=t.computedStyleSupportsPseudoElements,i=void 0===l?void 0!==t.getComputedStyle:l,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,d=t.hidden,c=void 0!==d&&d;function p(e,t){var r,n,a="";if(At(e)&&i){var o=Dt(s(e,"::before"));a="".concat(o," ").concat(a)}if((function(e){return At(e)&&"slot"===Rt(e)}(e)?0===(n=(r=e).assignedNodes()).length?Ct(r.childNodes):n:Ct(e.childNodes).concat(Nt(e,"aria-owns"))).forEach((function(e){var r=b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),n="inline"!==(At(e)?s(e).getPropertyValue("display"):"inline")?" ":"";a+="".concat(n).concat(r).concat(n)})),At(e)&&i){var l=Dt(s(e,"::after"));a="".concat(a," ").concat(l)}return a.trim()}function m(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}function f(e){if(!At(e))return null;if(function(e){return At(e)&&"fieldset"===Rt(e)}(e)){r.add(e);for(var t=Ct(e.childNodes),n=0;n<t.length;n+=1){var a=t[n];if(It(a))return b(a,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(function(e){return At(e)&&"table"===Rt(e)}(e)){r.add(e);for(var o=Ct(e.childNodes),l=0;l<o.length;l+=1){var i=o[l];if(jt(i))return b(i,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(function(e){return At(e)&&"svg"===Rt(e)}(e)){r.add(e);for(var u=Ct(e.childNodes),s=0;s<u.length;s+=1){var d=u[s];if(Bt(d))return d.textContent}return null}if("img"===Rt(e)||"area"===Rt(e)){var c=m(e,"alt");if(null!==c)return c}else if(function(e){return At(e)&&"optgroup"===Rt(e)}(e)){var f=m(e,"label");if(null!==f)return f}}if(St(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var v=m(e,"value");if(null!==v)return v;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var y,h,g=null===(h=(y=e).labels)?h:void 0!==h?Ct(h):zt(y)?Ct(y.ownerDocument.querySelectorAll("label")).filter((function(e){return $t(e)===y})):null;if(null!==g&&0!==g.length)return r.add(e),Ct(g).map((function(e){return b(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(St(e)&&"image"===e.type){var C=m(e,"alt");if(null!==C)return C;var q=m(e,"title");return null!==q?q:"Submit Query"}if(kt(e,["button"])){var P=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});if(""!==P)return P}return null}function b(e,t){if(r.has(e))return"";if(!c&&function(e,t){if(!At(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}(e,s)&&!t.isReferenced)return r.add(e),"";var n=At(e)?e.getAttributeNode("aria-labelledby"):null,a=null===n||r.has(n)?[]:Nt(e,"aria-labelledby");if("name"===o&&!t.isReferenced&&a.length>0)return r.add(n),a.map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var l,i=t.recursion&&(kt(l=e,["button","combobox","listbox","textbox"])||Ft(l,"range"))&&"name"===o;if(!i){var u=(At(e)&&e.getAttribute("aria-label")||"").trim();if(""!==u&&"name"===o)return r.add(e),u;if(!function(e){return kt(e,["none","presentation"])}(e)){var d=f(e);if(null!==d)return r.add(e),d}}if(kt(e,["menu"]))return r.add(e),"";if(i||t.isEmbeddedInLabel||t.isReferenced){if(kt(e,["combobox","listbox"])){r.add(e);var v=Ht(e);return 0===v.length?St(e)?e.value:"":Ct(v).map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(Ft(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(kt(e,["textbox"]))return r.add(e),Ut(e)}if(function(e){return kt(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}(e)||At(e)&&t.isReferenced||function(e){return jt(e)}(e)){var y=p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});if(""!==y)return r.add(e),y}if(e.nodeType===e.TEXT_NODE)return r.add(e),e.textContent||"";if(t.recursion)return r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});var h=function(e){return At(e)?m(e,"title"):null}(e);return null!==h?(r.add(e),h):(r.add(e),"")}return b(e,{isEmbeddedInLabel:!1,isReferenced:"description"===o,recursion:!1}).trim().replace(/\s\s+/g," ")}function Gt(e){return Gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Gt(e)}function Qt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Xt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Qt(Object(r),!0).forEach((function(t){Jt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Jt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Gt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Gt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Gt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Nt(e,"aria-describedby").map((function(e){return Wt(e,Xt(Xt({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function Yt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return kt(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])?"":Wt(e,t)}var Zt={},er={},tr={},rr={};Object.defineProperty(rr,"__esModule",{value:!0}),rr.default=void 0;var nr=function(){var e=this,t=0,r={"@@iterator":function(){return r},next:function(){if(t<e.length){var r=e[t];return t+=1,{done:!1,value:r}}return{done:!0}}};return r};rr.default=nr,Object.defineProperty(tr,"__esModule",{value:!0}),tr.default=function(e,t){"function"==typeof Symbol&&"symbol"===or(Symbol.iterator)&&Object.defineProperty(e,Symbol.iterator,{value:ar.default.bind(t)});return e};var ar=function(e){return e&&e.__esModule?e:{default:e}}(rr);function or(e){return or="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},or(e)}Object.defineProperty(er,"__esModule",{value:!0}),er.default=void 0;var lr=function(e){return e&&e.__esModule?e:{default:e}}(tr);function ir(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||ur(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ur(e,t){if(e){if("string"==typeof e)return sr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?sr(e,t):void 0}}function sr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var dr=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-braillelabel",{type:"string"}],["aria-brailleroledescription",{type:"string"}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-description",{type:"string"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],cr={entries:function(){return dr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=ur(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(dr);try{for(n.s();!(t=n.n()).done;){var a=ir(t.value,2),o=a[0],l=a[1];e.call(r,l,o,dr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=dr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!cr.get(e)},keys:function(){return dr.map((function(e){return ir(e,1)[0]}))},values:function(){return dr.map((function(e){return ir(e,2)[1]}))}},pr=(0,lr.default)(cr,cr.entries());er.default=pr;var mr={};Object.defineProperty(mr,"__esModule",{value:!0}),mr.default=void 0;var fr=function(e){return e&&e.__esModule?e:{default:e}}(tr);function br(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||vr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vr(e,t){if(e){if("string"==typeof e)return yr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?yr(e,t):void 0}}function yr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var hr=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],gr={entries:function(){return hr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=vr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(hr);try{for(n.s();!(t=n.n()).done;){var a=br(t.value,2),o=a[0],l=a[1];e.call(r,l,o,hr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=hr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!gr.get(e)},keys:function(){return hr.map((function(e){return br(e,1)[0]}))},values:function(){return hr.map((function(e){return br(e,2)[1]}))}},Cr=(0,fr.default)(gr,gr.entries());mr.default=Cr;var qr={},Pr={},Er={};Object.defineProperty(Er,"__esModule",{value:!0}),Er.default=void 0;var wr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Er.default=wr;var xr={};Object.defineProperty(xr,"__esModule",{value:!0}),xr.default=void 0;var Rr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};xr.default=Rr;var Or={};Object.defineProperty(Or,"__esModule",{value:!0}),Or.default=void 0;var Tr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Or.default=Tr;var _r={};Object.defineProperty(_r,"__esModule",{value:!0}),_r.default=void 0;var Mr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_r.default=Mr;var Ar={};Object.defineProperty(Ar,"__esModule",{value:!0}),Ar.default=void 0;var jr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Ar.default=jr;var Sr={};Object.defineProperty(Sr,"__esModule",{value:!0}),Sr.default=void 0;var Ir={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Sr.default=Ir;var Br={};Object.defineProperty(Br,"__esModule",{value:!0}),Br.default=void 0;var Nr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Br.default=Nr;var kr={};Object.defineProperty(kr,"__esModule",{value:!0}),kr.default=void 0;var Fr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};kr.default=Fr;var Lr={};Object.defineProperty(Lr,"__esModule",{value:!0}),Lr.default=void 0;var Hr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};Lr.default=Hr;var Ur={};Object.defineProperty(Ur,"__esModule",{value:!0}),Ur.default=void 0;var Dr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Ur.default=Dr;var zr={};Object.defineProperty(zr,"__esModule",{value:!0}),zr.default=void 0;var Vr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};zr.default=Vr;var $r={};Object.defineProperty($r,"__esModule",{value:!0}),$r.default=void 0;var Wr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};$r.default=Wr,Object.defineProperty(Pr,"__esModule",{value:!0}),Pr.default=void 0;var Gr=on(Er),Qr=on(xr),Xr=on(Or),Jr=on(_r),Kr=on(Ar),Yr=on(Sr),Zr=on(Br),en=on(kr),tn=on(Lr),rn=on(Ur),nn=on(zr),an=on($r);function on(e){return e&&e.__esModule?e:{default:e}}var ln=[["command",Gr.default],["composite",Qr.default],["input",Xr.default],["landmark",Jr.default],["range",Kr.default],["roletype",Yr.default],["section",Zr.default],["sectionhead",en.default],["select",tn.default],["structure",rn.default],["widget",nn.default],["window",an.default]];Pr.default=ln;var un={},sn={};Object.defineProperty(sn,"__esModule",{value:!0}),sn.default=void 0;var dn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};sn.default=dn;var cn={};Object.defineProperty(cn,"__esModule",{value:!0}),cn.default=void 0;var pn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};cn.default=pn;var mn={};Object.defineProperty(mn,"__esModule",{value:!0}),mn.default=void 0;var fn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};mn.default=fn;var bn={};Object.defineProperty(bn,"__esModule",{value:!0}),bn.default=void 0;var vn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};bn.default=vn;var yn={};Object.defineProperty(yn,"__esModule",{value:!0}),yn.default=void 0;var hn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["scoped to the body element"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};yn.default=hn;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;var Cn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"blockquote"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};gn.default=Cn;var qn={};Object.defineProperty(qn,"__esModule",{value:!0}),qn.default=void 0;var Pn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};qn.default=Pn;var En={};Object.defineProperty(En,"__esModule",{value:!0}),En.default=void 0;var wn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"caption"},module:"HTML"}],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};En.default=wn;var xn={};Object.defineProperty(xn,"__esModule",{value:!0}),xn.default=void 0;var Rn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["ancestor table element has table role"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};xn.default=Rn;var On={};Object.defineProperty(On,"__esModule",{value:!0}),On.default=void 0;var Tn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};On.default=Tn;var _n={};Object.defineProperty(_n,"__esModule",{value:!0}),_n.default=void 0;var Mn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"code"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_n.default=Mn;var An={};Object.defineProperty(An,"__esModule",{value:!0}),An.default=void 0;var jn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"col"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"colgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};An.default=jn;var Sn={};Object.defineProperty(Sn,"__esModule",{value:!0}),Sn.default=void 0;var In={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],constraints:["the multiple attribute is not set and the size attribute does not have a value greater than 1"],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};Sn.default=In;var Bn={};Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.default=void 0;var Nn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-label"}],constraints:["scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"aside"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],constraints:["scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Bn.default=Nn;var kn={};Object.defineProperty(kn,"__esModule",{value:!0}),kn.default=void 0;var Fn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["scoped to the body element"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};kn.default=Fn;var Ln={};Object.defineProperty(Ln,"__esModule",{value:!0}),Ln.default=void 0;var Hn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ln.default=Hn;var Un={};Object.defineProperty(Un,"__esModule",{value:!0}),Un.default=void 0;var Dn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"del"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Un.default=Dn;var zn={};Object.defineProperty(zn,"__esModule",{value:!0}),zn.default=void 0;var Vn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};zn.default=Vn;var $n={};Object.defineProperty($n,"__esModule",{value:!0}),$n.default=void 0;var Wn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};$n.default=Wn;var Gn={};Object.defineProperty(Gn,"__esModule",{value:!0}),Gn.default=void 0;var Qn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"html"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Gn.default=Qn;var Xn={};Object.defineProperty(Xn,"__esModule",{value:!0}),Xn.default=void 0;var Jn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"em"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Xn.default=Jn;var Kn={};Object.defineProperty(Kn,"__esModule",{value:!0}),Kn.default=void 0;var Yn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Kn.default=Yn;var Zn={};Object.defineProperty(Zn,"__esModule",{value:!0}),Zn.default=void 0;var ea={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Zn.default=ea;var ta={};Object.defineProperty(ta,"__esModule",{value:!0}),ta.default=void 0;var ra={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ta.default=ra;var na={};Object.defineProperty(na,"__esModule",{value:!0}),na.default=void 0;var aa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"a"},module:"HTML"},{concept:{name:"area"},module:"HTML"},{concept:{name:"aside"},module:"HTML"},{concept:{name:"b"},module:"HTML"},{concept:{name:"bdo"},module:"HTML"},{concept:{name:"body"},module:"HTML"},{concept:{name:"data"},module:"HTML"},{concept:{name:"div"},module:"HTML"},{concept:{constraints:["scoped to the main element","scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"footer"},module:"HTML"},{concept:{constraints:["scoped to the main element","scoped to a sectioning content element","scoped to a sectioning root element other than body"],name:"header"},module:"HTML"},{concept:{name:"hgroup"},module:"HTML"},{concept:{name:"i"},module:"HTML"},{concept:{name:"pre"},module:"HTML"},{concept:{name:"q"},module:"HTML"},{concept:{name:"samp"},module:"HTML"},{concept:{name:"section"},module:"HTML"},{concept:{name:"small"},module:"HTML"},{concept:{name:"span"},module:"HTML"},{concept:{name:"u"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};na.default=aa;var oa={};Object.defineProperty(oa,"__esModule",{value:!0}),oa.default=void 0;var la={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};oa.default=la;var ia={};Object.defineProperty(ia,"__esModule",{value:!0}),ia.default=void 0;var ua={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{constraints:["ancestor table element has grid role","ancestor table element has treegrid role"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};ia.default=ua;var sa={};Object.defineProperty(sa,"__esModule",{value:!0}),sa.default=void 0;var da={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"},{concept:{name:"address"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};sa.default=da;var ca={};Object.defineProperty(ca,"__esModule",{value:!0}),ca.default=void 0;var pa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};ca.default=pa;var ma={};Object.defineProperty(ma,"__esModule",{value:!0}),ma.default=void 0;var fa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ma.default=fa;var ba={};Object.defineProperty(ba,"__esModule",{value:!0}),ba.default=void 0;var va={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"ins"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ba.default=va;var ya={};Object.defineProperty(ya,"__esModule",{value:!0}),ya.default=void 0;var ha={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"href"}],name:"area"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};ya.default=ha;var ga={};Object.defineProperty(ga,"__esModule",{value:!0}),ga.default=void 0;var Ca={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};ga.default=Ca;var qa={};Object.defineProperty(qa,"__esModule",{value:!0}),qa.default=void 0;var Pa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"}],constraints:["the size attribute value is greater than 1"],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};qa.default=Pa;var Ea={};Object.defineProperty(Ea,"__esModule",{value:!0}),Ea.default=void 0;var wa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol","direct descendant of ul","direct descendant of menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ea.default=wa;var xa={};Object.defineProperty(xa,"__esModule",{value:!0}),xa.default=void 0;var Ra={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};xa.default=Ra;var Oa={};Object.defineProperty(Oa,"__esModule",{value:!0}),Oa.default=void 0;var Ta={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Oa.default=Ta;var _a={};Object.defineProperty(_a,"__esModule",{value:!0}),_a.default=void 0;var Ma={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:[],props:{"aria-braillelabel":null,"aria-brailleroledescription":null,"aria-description":null},relatedConcepts:[{concept:{name:"mark"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_a.default=Ma;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0}),Aa.default=void 0;var ja={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Aa.default=ja;var Sa={};Object.defineProperty(Sa,"__esModule",{value:!0}),Sa.default=void 0;var Ia={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Sa.default=Ia;var Ba={};Object.defineProperty(Ba,"__esModule",{value:!0}),Ba.default=void 0;var Na={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Ba.default=Na;var ka={};Object.defineProperty(ka,"__esModule",{value:!0}),ka.default=void 0;var Fa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};ka.default=Fa;var La={};Object.defineProperty(La,"__esModule",{value:!0}),La.default=void 0;var Ha={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};La.default=Ha;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0}),Ua.default=void 0;var Da={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};Ua.default=Da;var za={};Object.defineProperty(za,"__esModule",{value:!0}),za.default=void 0;var Va={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};za.default=Va;var $a={};Object.defineProperty($a,"__esModule",{value:!0}),$a.default=void 0;var Wa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{name:"meter"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};$a.default=Wa;var Ga={};Object.defineProperty(Ga,"__esModule",{value:!0}),Ga.default=void 0;var Qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ga.default=Qa;var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0}),Xa.default=void 0;var Ja={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Xa.default=Ja;var Ka={};Object.defineProperty(Ka,"__esModule",{value:!0}),Ka.default=void 0;var Ya={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ka.default=Ya;var Za={};Object.defineProperty(Za,"__esModule",{value:!0}),Za.default=void 0;var eo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};Za.default=eo;var to={};Object.defineProperty(to,"__esModule",{value:!0}),to.default=void 0;var ro={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"p"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};to.default=ro;var no={};Object.defineProperty(no,"__esModule",{value:!0}),no.default=void 0;var ao={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{attributes:[{name:"alt",value:""}],name:"img"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};no.default=ao;var oo={};Object.defineProperty(oo,"__esModule",{value:!0}),oo.default=void 0;var lo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};oo.default=lo;var io={};Object.defineProperty(io,"__esModule",{value:!0}),io.default=void 0;var uo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};io.default=uo;var so={};Object.defineProperty(so,"__esModule",{value:!0}),so.default=void 0;var co={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};so.default=co;var po={};Object.defineProperty(po,"__esModule",{value:!0}),po.default=void 0;var mo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};po.default=mo;var fo={};Object.defineProperty(fo,"__esModule",{value:!0}),fo.default=void 0;var bo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};fo.default=bo;var vo={};Object.defineProperty(vo,"__esModule",{value:!0}),vo.default=void 0;var yo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};vo.default=yo;var ho={};Object.defineProperty(ho,"__esModule",{value:!0}),ho.default=void 0;var go={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"rowgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row","rowgroup"],requiredContextRole:["row","rowgroup"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};ho.default=go;var Co={};Object.defineProperty(Co,"__esModule",{value:!0}),Co.default=void 0;var qo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};Co.default=qo;var Po={};Object.defineProperty(Po,"__esModule",{value:!0}),Po.default=void 0;var Eo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Po.default=Eo;var wo={};Object.defineProperty(wo,"__esModule",{value:!0}),wo.default=void 0;var xo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};wo.default=xo;var Ro={};Object.defineProperty(Ro,"__esModule",{value:!0}),Ro.default=void 0;var Oo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Ro.default=Oo;var To={};Object.defineProperty(To,"__esModule",{value:!0}),To.default=void 0;var _o={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};To.default=_o;var Mo={};Object.defineProperty(Mo,"__esModule",{value:!0}),Mo.default=void 0;var Ao={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};Mo.default=Ao;var jo={};Object.defineProperty(jo,"__esModule",{value:!0}),jo.default=void 0;var So={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};jo.default=So;var Io={};Object.defineProperty(Io,"__esModule",{value:!0}),Io.default=void 0;var Bo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"strong"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Io.default=Bo;var No={};Object.defineProperty(No,"__esModule",{value:!0}),No.default=void 0;var ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"sub"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};No.default=ko;var Fo={};Object.defineProperty(Fo,"__esModule",{value:!0}),Fo.default=void 0;var Lo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"sup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fo.default=Lo;var Ho={};Object.defineProperty(Ho,"__esModule",{value:!0}),Ho.default=void 0;var Uo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};Ho.default=Uo;var Do={};Object.defineProperty(Do,"__esModule",{value:!0}),Do.default=void 0;var zo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};Do.default=zo;var Vo={};Object.defineProperty(Vo,"__esModule",{value:!0}),Vo.default=void 0;var $o={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};Vo.default=$o;var Wo={};Object.defineProperty(Wo,"__esModule",{value:!0}),Wo.default=void 0;var Go={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};Wo.default=Go;var Qo={};Object.defineProperty(Qo,"__esModule",{value:!0}),Qo.default=void 0;var Xo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Qo.default=Xo;var Jo={};Object.defineProperty(Jo,"__esModule",{value:!0}),Jo.default=void 0;var Ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Jo.default=Ko;var Yo={};Object.defineProperty(Yo,"__esModule",{value:!0}),Yo.default=void 0;var Zo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],constraints:["the list attribute is not set"],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};Yo.default=Zo;var el={};Object.defineProperty(el,"__esModule",{value:!0}),el.default=void 0;var tl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"time"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};el.default=tl;var rl={};Object.defineProperty(rl,"__esModule",{value:!0}),rl.default=void 0;var nl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};rl.default=nl;var al={};Object.defineProperty(al,"__esModule",{value:!0}),al.default=void 0;var ol={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};al.default=ol;var ll={};Object.defineProperty(ll,"__esModule",{value:!0}),ll.default=void 0;var il={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ll.default=il;var ul={};Object.defineProperty(ul,"__esModule",{value:!0}),ul.default=void 0;var sl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ul.default=sl;var dl={};Object.defineProperty(dl,"__esModule",{value:!0}),dl.default=void 0;var cl={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};dl.default=cl;var pl={};Object.defineProperty(pl,"__esModule",{value:!0}),pl.default=void 0;var ml={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};pl.default=ml,Object.defineProperty(un,"__esModule",{value:!0}),un.default=void 0;var fl=zi(sn),bl=zi(cn),vl=zi(mn),yl=zi(bn),hl=zi(yn),gl=zi(gn),Cl=zi(qn),ql=zi(En),Pl=zi(xn),El=zi(On),wl=zi(_n),xl=zi(An),Rl=zi(Sn),Ol=zi(Bn),Tl=zi(kn),_l=zi(Ln),Ml=zi(Un),Al=zi(zn),jl=zi($n),Sl=zi(Gn),Il=zi(Xn),Bl=zi(Kn),Nl=zi(Zn),kl=zi(ta),Fl=zi(na),Ll=zi(oa),Hl=zi(ia),Ul=zi(sa),Dl=zi(ca),zl=zi(ma),Vl=zi(ba),$l=zi(ya),Wl=zi(ga),Gl=zi(qa),Ql=zi(Ea),Xl=zi(xa),Jl=zi(Oa),Kl=zi(_a),Yl=zi(Aa),Zl=zi(Sa),ei=zi(Ba),ti=zi(ka),ri=zi(La),ni=zi(Ua),ai=zi(za),oi=zi($a),li=zi(Ga),ii=zi(Xa),ui=zi(Ka),si=zi(Za),di=zi(to),ci=zi(no),pi=zi(oo),mi=zi(io),fi=zi(so),bi=zi(po),vi=zi(fo),yi=zi(vo),hi=zi(ho),gi=zi(Co),Ci=zi(Po),qi=zi(wo),Pi=zi(Ro),Ei=zi(To),wi=zi(Mo),xi=zi(jo),Ri=zi(Io),Oi=zi(No),Ti=zi(Fo),_i=zi(Ho),Mi=zi(Do),Ai=zi(Vo),ji=zi(Wo),Si=zi(Qo),Ii=zi(Jo),Bi=zi(Yo),Ni=zi(el),ki=zi(rl),Fi=zi(al),Li=zi(ll),Hi=zi(ul),Ui=zi(dl),Di=zi(pl);function zi(e){return e&&e.__esModule?e:{default:e}}var Vi=[["alert",fl.default],["alertdialog",bl.default],["application",vl.default],["article",yl.default],["banner",hl.default],["blockquote",gl.default],["button",Cl.default],["caption",ql.default],["cell",Pl.default],["checkbox",El.default],["code",wl.default],["columnheader",xl.default],["combobox",Rl.default],["complementary",Ol.default],["contentinfo",Tl.default],["definition",_l.default],["deletion",Ml.default],["dialog",Al.default],["directory",jl.default],["document",Sl.default],["emphasis",Il.default],["feed",Bl.default],["figure",Nl.default],["form",kl.default],["generic",Fl.default],["grid",Ll.default],["gridcell",Hl.default],["group",Ul.default],["heading",Dl.default],["img",zl.default],["insertion",Vl.default],["link",$l.default],["list",Wl.default],["listbox",Gl.default],["listitem",Ql.default],["log",Xl.default],["main",Jl.default],["mark",Kl.default],["marquee",Yl.default],["math",Zl.default],["menu",ei.default],["menubar",ti.default],["menuitem",ri.default],["menuitemcheckbox",ni.default],["menuitemradio",ai.default],["meter",oi.default],["navigation",li.default],["none",ii.default],["note",ui.default],["option",si.default],["paragraph",di.default],["presentation",ci.default],["progressbar",pi.default],["radio",mi.default],["radiogroup",fi.default],["region",bi.default],["row",vi.default],["rowgroup",yi.default],["rowheader",hi.default],["scrollbar",gi.default],["search",Ci.default],["searchbox",qi.default],["separator",Pi.default],["slider",Ei.default],["spinbutton",wi.default],["status",xi.default],["strong",Ri.default],["subscript",Oi.default],["superscript",Ti.default],["switch",_i.default],["tab",Mi.default],["table",Ai.default],["tablist",ji.default],["tabpanel",Si.default],["term",Ii.default],["textbox",Bi.default],["time",Ni.default],["timer",ki.default],["toolbar",Fi.default],["tooltip",Li.default],["tree",Hi.default],["treegrid",Ui.default],["treeitem",Di.default]];un.default=Vi;var $i={},Wi={};Object.defineProperty(Wi,"__esModule",{value:!0}),Wi.default=void 0;var Gi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Wi.default=Gi;var Qi={};Object.defineProperty(Qi,"__esModule",{value:!0}),Qi.default=void 0;var Xi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Qi.default=Xi;var Ji={};Object.defineProperty(Ji,"__esModule",{value:!0}),Ji.default=void 0;var Ki={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ji.default=Ki;var Yi={};Object.defineProperty(Yi,"__esModule",{value:!0}),Yi.default=void 0;var Zi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Yi.default=Zi;var eu={};Object.defineProperty(eu,"__esModule",{value:!0}),eu.default=void 0;var tu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};eu.default=tu;var ru={};Object.defineProperty(ru,"__esModule",{value:!0}),ru.default=void 0;var nu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};ru.default=nu;var au={};Object.defineProperty(au,"__esModule",{value:!0}),au.default=void 0;var ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};au.default=ou;var lu={};Object.defineProperty(lu,"__esModule",{value:!0}),lu.default=void 0;var iu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};lu.default=iu;var uu={};Object.defineProperty(uu,"__esModule",{value:!0}),uu.default=void 0;var su={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};uu.default=su;var du={};Object.defineProperty(du,"__esModule",{value:!0}),du.default=void 0;var cu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};du.default=cu;var pu={};Object.defineProperty(pu,"__esModule",{value:!0}),pu.default=void 0;var mu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};pu.default=mu;var fu={};Object.defineProperty(fu,"__esModule",{value:!0}),fu.default=void 0;var bu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};fu.default=bu;var vu={};Object.defineProperty(vu,"__esModule",{value:!0}),vu.default=void 0;var yu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};vu.default=yu;var hu={};Object.defineProperty(hu,"__esModule",{value:!0}),hu.default=void 0;var gu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};hu.default=gu;var Cu={};Object.defineProperty(Cu,"__esModule",{value:!0}),Cu.default=void 0;var qu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Cu.default=qu;var Pu={};Object.defineProperty(Pu,"__esModule",{value:!0}),Pu.default=void 0;var Eu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Pu.default=Eu;var wu={};Object.defineProperty(wu,"__esModule",{value:!0}),wu.default=void 0;var xu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};wu.default=xu;var Ru={};Object.defineProperty(Ru,"__esModule",{value:!0}),Ru.default=void 0;var Ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ru.default=Ou;var Tu={};Object.defineProperty(Tu,"__esModule",{value:!0}),Tu.default=void 0;var _u={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Tu.default=_u;var Mu={};Object.defineProperty(Mu,"__esModule",{value:!0}),Mu.default=void 0;var Au={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Mu.default=Au;var ju={};Object.defineProperty(ju,"__esModule",{value:!0}),ju.default=void 0;var Su={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ju.default=Su;var Iu={};Object.defineProperty(Iu,"__esModule",{value:!0}),Iu.default=void 0;var Bu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Iu.default=Bu;var Nu={};Object.defineProperty(Nu,"__esModule",{value:!0}),Nu.default=void 0;var ku={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Nu.default=ku;var Fu={};Object.defineProperty(Fu,"__esModule",{value:!0}),Fu.default=void 0;var Lu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Fu.default=Lu;var Hu={};Object.defineProperty(Hu,"__esModule",{value:!0}),Hu.default=void 0;var Uu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Hu.default=Uu;var Du={};Object.defineProperty(Du,"__esModule",{value:!0}),Du.default=void 0;var zu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Du.default=zu;var Vu={};Object.defineProperty(Vu,"__esModule",{value:!0}),Vu.default=void 0;var $u={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Vu.default=$u;var Wu={};Object.defineProperty(Wu,"__esModule",{value:!0}),Wu.default=void 0;var Gu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Wu.default=Gu;var Qu={};Object.defineProperty(Qu,"__esModule",{value:!0}),Qu.default=void 0;var Xu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};Qu.default=Xu;var Ju={};Object.defineProperty(Ju,"__esModule",{value:!0}),Ju.default=void 0;var Ku={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};Ju.default=Ku;var Yu={};Object.defineProperty(Yu,"__esModule",{value:!0}),Yu.default=void 0;var Zu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Yu.default=Zu;var es={};Object.defineProperty(es,"__esModule",{value:!0}),es.default=void 0;var ts={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};es.default=ts;var rs={};Object.defineProperty(rs,"__esModule",{value:!0}),rs.default=void 0;var ns={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};rs.default=ns;var as={};Object.defineProperty(as,"__esModule",{value:!0}),as.default=void 0;var os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};as.default=os;var ls={};Object.defineProperty(ls,"__esModule",{value:!0}),ls.default=void 0;var is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};ls.default=is;var us={};Object.defineProperty(us,"__esModule",{value:!0}),us.default=void 0;var ss={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};us.default=ss;var ds={};Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var cs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};ds.default=cs;var ps={};Object.defineProperty(ps,"__esModule",{value:!0}),ps.default=void 0;var ms={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};ps.default=ms;var fs={};Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var bs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};fs.default=bs,Object.defineProperty($i,"__esModule",{value:!0}),$i.default=void 0;var vs=td(Wi),ys=td(Qi),hs=td(Ji),gs=td(Yi),Cs=td(eu),qs=td(ru),Ps=td(au),Es=td(lu),ws=td(uu),xs=td(du),Rs=td(pu),Os=td(fu),Ts=td(vu),_s=td(hu),Ms=td(Cu),As=td(Pu),js=td(wu),Ss=td(Ru),Is=td(Tu),Bs=td(Mu),Ns=td(ju),ks=td(Iu),Fs=td(Nu),Ls=td(Fu),Hs=td(Hu),Us=td(Du),Ds=td(Vu),zs=td(Wu),Vs=td(Qu),$s=td(Ju),Ws=td(Yu),Gs=td(es),Qs=td(rs),Xs=td(as),Js=td(ls),Ks=td(us),Ys=td(ds),Zs=td(ps),ed=td(fs);function td(e){return e&&e.__esModule?e:{default:e}}var rd=[["doc-abstract",vs.default],["doc-acknowledgments",ys.default],["doc-afterword",hs.default],["doc-appendix",gs.default],["doc-backlink",Cs.default],["doc-biblioentry",qs.default],["doc-bibliography",Ps.default],["doc-biblioref",Es.default],["doc-chapter",ws.default],["doc-colophon",xs.default],["doc-conclusion",Rs.default],["doc-cover",Os.default],["doc-credit",Ts.default],["doc-credits",_s.default],["doc-dedication",Ms.default],["doc-endnote",As.default],["doc-endnotes",js.default],["doc-epigraph",Ss.default],["doc-epilogue",Is.default],["doc-errata",Bs.default],["doc-example",Ns.default],["doc-footnote",ks.default],["doc-foreword",Fs.default],["doc-glossary",Ls.default],["doc-glossref",Hs.default],["doc-index",Us.default],["doc-introduction",Ds.default],["doc-noteref",zs.default],["doc-notice",Vs.default],["doc-pagebreak",$s.default],["doc-pagelist",Ws.default],["doc-part",Gs.default],["doc-preface",Qs.default],["doc-prologue",Xs.default],["doc-pullquote",Js.default],["doc-qna",Ks.default],["doc-subtitle",Ys.default],["doc-tip",Zs.default],["doc-toc",ed.default]];$i.default=rd;var nd={},ad={};Object.defineProperty(ad,"__esModule",{value:!0}),ad.default=void 0;var od={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-object"}},{module:"ARIA",concept:{name:"img"}},{module:"ARIA",concept:{name:"article"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};ad.default=od;var ld={};Object.defineProperty(ld,"__esModule",{value:!0}),ld.default=void 0;var id={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-document"}},{module:"ARIA",concept:{name:"group"}},{module:"ARIA",concept:{name:"img"}},{module:"GRAPHICS",concept:{name:"graphics-symbol"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};ld.default=id;var ud={};Object.defineProperty(ud,"__esModule",{value:!0}),ud.default=void 0;var sd={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};ud.default=sd,Object.defineProperty(nd,"__esModule",{value:!0}),nd.default=void 0;var dd=md(ad),cd=md(ld),pd=md(ud);function md(e){return e&&e.__esModule?e:{default:e}}var fd=[["graphics-document",dd.default],["graphics-object",cd.default],["graphics-symbol",pd.default]];nd.default=fd,Object.defineProperty(qr,"__esModule",{value:!0}),qr.default=void 0;var bd=Cd(Pr),vd=Cd(un),yd=Cd($i),hd=Cd(nd),gd=Cd(tr);function Cd(e){return e&&e.__esModule?e:{default:e}}function qd(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Pd(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=wd(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}function Ed(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||wd(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wd(e,t){if(e){if("string"==typeof e)return xd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?xd(e,t):void 0}}function xd(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Rd=[].concat(bd.default,vd.default,yd.default,hd.default);Rd.forEach((function(e){var t,r=Ed(e,2)[1],n=Pd(r.superClass);try{for(n.s();!(t=n.n()).done;){var a,o=Pd(t.value);try{var l=function(){var e=a.value,t=Rd.find((function(t){return Ed(t,1)[0]===e}));if(t)for(var n=t[1],o=0,l=Object.keys(n.props);o<l.length;o++){var i=l[o];Object.prototype.hasOwnProperty.call(r.props,i)||Object.assign(r.props,qd({},i,n.props[i]))}};for(o.s();!(a=o.n()).done;)l()}catch(e){o.e(e)}finally{o.f()}}}catch(e){n.e(e)}finally{n.f()}}));var Od={entries:function(){return Rd},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=Pd(Rd);try{for(n.s();!(t=n.n()).done;){var a=Ed(t.value,2),o=a[0],l=a[1];e.call(r,l,o,Rd)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Rd.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Od.get(e)},keys:function(){return Rd.map((function(e){return Ed(e,1)[0]}))},values:function(){return Rd.map((function(e){return Ed(e,2)[1]}))}},Td=(0,gd.default)(Od,Od.entries());qr.default=Td;var _d={},Md={},Ad=Object.prototype.hasOwnProperty;Md.dequal=function e(t,r){var n,a;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((a=t.length)===r.length)for(;a--&&e(t[a],r[a]););return-1===a}if(!n||"object"==typeof t){for(n in a=0,t){if(Ad.call(t,n)&&++a&&!Ad.call(r,n))return!1;if(!(n in r)||!e(t[n],r[n]))return!1}return Object.keys(r).length===a}}return t!=t&&r!=r},Object.defineProperty(_d,"__esModule",{value:!0}),_d.default=void 0;var jd=Md,Sd=Bd(tr),Id=Bd(qr);function Bd(e){return e&&e.__esModule?e:{default:e}}function Nd(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||kd(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kd(e,t){if(e){if("string"==typeof e)return Fd(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Fd(e,t):void 0}}function Fd(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Ld=[],Hd=Id.default.keys(),Ud=0;Ud<Hd.length;Ud++){var Dd=Hd[Ud],zd=Id.default.get(Dd);if(zd)for(var Vd=[].concat(zd.baseConcepts,zd.relatedConcepts),$d=0;$d<Vd.length;$d++){var Wd=Vd[$d];"HTML"===Wd.module&&function(){var e=Wd.concept;if(e){var t,r=Ld.find((function(t){return(0,jd.dequal)(t,e)}));t=r?r[1]:[];for(var n=!0,a=0;a<t.length;a++)if(t[a]===Dd){n=!1;break}n&&t.push(Dd),Ld.push([e,t])}}()}}var Gd={entries:function(){return Ld},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=kd(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(Ld);try{for(n.s();!(t=n.n()).done;){var a=Nd(t.value,2),o=a[0],l=a[1];e.call(r,l,o,Ld)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Ld.find((function(t){return e.name===t[0].name&&(0,jd.dequal)(e.attributes,t[0].attributes)}));return t&&t[1]},has:function(e){return!!Gd.get(e)},keys:function(){return Ld.map((function(e){return Nd(e,1)[0]}))},values:function(){return Ld.map((function(e){return Nd(e,2)[1]}))}},Qd=(0,Sd.default)(Gd,Gd.entries());_d.default=Qd;var Xd={};Object.defineProperty(Xd,"__esModule",{value:!0}),Xd.default=void 0;var Jd=Yd(tr),Kd=Yd(qr);function Yd(e){return e&&e.__esModule?e:{default:e}}function Zd(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,a,o=[],l=!0,i=!1;try{for(r=r.call(e);!(l=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);l=!0);}catch(e){i=!0,a=e}finally{try{l||null==r.return||r.return()}finally{if(i)throw a}}return o}(e,t)||ec(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ec(e,t){if(e){if("string"==typeof e)return tc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?tc(e,t):void 0}}function tc(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var rc=[],nc=Kd.default.keys(),ac=0;ac<nc.length;ac++){var oc=nc[ac],lc=Kd.default.get(oc),ic=[];if(lc){for(var uc=[].concat(lc.baseConcepts,lc.relatedConcepts),sc=0;sc<uc.length;sc++){var dc=uc[sc];if("HTML"===dc.module){var cc=dc.concept;null!=cc&&ic.push(cc)}}ic.length>0&&rc.push([oc,ic])}}var pc={entries:function(){return rc},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=ec(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,i=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return l=e.done,e},e:function(e){i=!0,o=e},f:function(){try{l||null==r.return||r.return()}finally{if(i)throw o}}}}(rc);try{for(n.s();!(t=n.n()).done;){var a=Zd(t.value,2),o=a[0],l=a[1];e.call(r,l,o,rc)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=rc.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!pc.get(e)},keys:function(){return rc.map((function(e){return Zd(e,1)[0]}))},values:function(){return rc.map((function(e){return Zd(e,2)[1]}))}},mc=(0,Jd.default)(pc,pc.entries());Xd.default=mc,Object.defineProperty(Zt,"__esModule",{value:!0});var fc=Zt.roles=Oc=Zt.roleElements=Zt.elementRoles=Zt.dom=Zt.aria=void 0,bc=Cc(er),vc=Cc(mr),yc=Cc(qr),hc=Cc(_d),gc=Cc(Xd);function Cc(e){return e&&e.__esModule?e:{default:e}}var qc=bc.default;Zt.aria=qc;var Pc=vc.default;Zt.dom=Pc;var Ec=yc.default;fc=Zt.roles=Ec;var wc=hc.default,xc=Zt.elementRoles=wc,Rc=gc.default,Oc=Zt.roleElements=Rc,Tc={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function a(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var o={compressToBase64:function(e){if(null==e)return"";var r=o._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:o._decompress(e.length,32,(function(r){return a(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":o._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:o._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=o.compress(e),r=new Uint8Array(2*t.length),n=0,a=t.length;n<a;n++){var l=t.charCodeAt(n);r[2*n]=l>>>8,r[2*n+1]=l%256}return r},decompressFromUint8Array:function(t){if(null==t)return o.decompress(t);for(var r=new Array(t.length/2),n=0,a=r.length;n<a;n++)r[n]=256*t[2*n]+t[2*n+1];var l=[];return r.forEach((function(t){l.push(e(t))})),o.decompress(l.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":o._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),o._decompress(e.length,32,(function(t){return a(r,e.charAt(t))})))},compress:function(t){return o._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,a,o,l={},i={},u="",s="",d="",c=2,p=3,m=2,f=[],b=0,v=0;for(o=0;o<e.length;o+=1)if(u=e.charAt(o),Object.prototype.hasOwnProperty.call(l,u)||(l[u]=p++,i[u]=!0),s=d+u,Object.prototype.hasOwnProperty.call(l,s))d=s;else{if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++),l[s]=p++,d=String(u)}if(""!==d){if(Object.prototype.hasOwnProperty.call(i,d)){if(d.charCodeAt(0)<256){for(n=0;n<m;n++)b<<=1,v==t-1?(v=0,f.push(r(b)),b=0):v++;for(a=d.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}else{for(a=1,n=0;n<m;n++)b=b<<1|a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a=0;for(a=d.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1}0==--c&&(c=Math.pow(2,m),m++),delete i[d]}else for(a=l[d],n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;0==--c&&(c=Math.pow(2,m),m++)}for(a=2,n=0;n<m;n++)b=b<<1|1&a,v==t-1?(v=0,f.push(r(b)),b=0):v++,a>>=1;for(;;){if(b<<=1,v==t-1){f.push(r(b));break}v++}return f.join("")},decompress:function(e){return null==e?"":""==e?null:o._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var a,o,l,i,u,s,d,c=[],p=4,m=4,f=3,b="",v=[],y={val:n(0),position:r,index:1};for(a=0;a<3;a+=1)c[a]=a;for(l=0,u=Math.pow(2,2),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;d=e(l);break;case 2:return""}for(c[3]=d,o=d,v.push(d);;){if(y.index>t)return"";for(l=0,u=Math.pow(2,f),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;switch(d=l){case 0:for(l=0,u=Math.pow(2,8),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 1:for(l=0,u=Math.pow(2,16),s=1;s!=u;)i=y.val&y.position,y.position>>=1,0==y.position&&(y.position=r,y.val=n(y.index++)),l|=(i>0?1:0)*s,s<<=1;c[m++]=e(l),d=m-1,p--;break;case 2:return v.join("")}if(0==p&&(p=Math.pow(2,f),f++),c[d])b=c[d];else{if(d!==m)return null;b=o+o.charAt(0)}v.push(b),c[m++]=o+b.charAt(0),o=b,0==--p&&(p=Math.pow(2,f),f++)}}};return o}();null!=e?e.exports=t:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",(function(){return t}))}(Tc);var _c=Tc.exports;function Mc(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const Ac=(e,t,r,n,a,o,l)=>{const i=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let d=l(s,r,i,a,o);return"string"!=typeof s&&(-1!==d.indexOf("\n")&&(d=r.spacingOuter+i+d+r.spacingOuter+n),d="{"+d+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+d+u.value.close})).join("")},jc=(e,t,r,n,a,o)=>e.map((e=>{const l="string"==typeof e?Sc(e,t):o(e,t,r,n,a);return""===l&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+l})).join(""),Sc=(e,t)=>{const r=t.colors.content;return r.open+Mc(e)+r.close},Ic=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+Mc(e)+"--\x3e"+r.close},Bc=(e,t,r,n,a)=>{const o=n.colors.tag;return o.open+"<"+e+(t&&o.close+t+n.spacingOuter+a+o.open)+(r?">"+o.close+r+n.spacingOuter+a+o.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+o.close},Nc=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},kc=3,Fc=8,Lc=11,Hc=/^((HTML|SVG)\w*)?Element$/,Uc=e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,a="string"==typeof n&&n.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is");return 1===r&&(Hc.test(t)||a)||r===kc&&"Text"===t||r===Fc&&"Comment"===t||r===Lc&&"DocumentFragment"===t};function Dc(e){return e.nodeType===Lc}function zc(e){return{test:e=>{var t;return(null==e||null==(t=e.constructor)?void 0:t.name)&&Uc(e)},serialize:(t,r,n,a,o,l)=>{if(function(e){return e.nodeType===kc}(t))return Sc(t.data,r);if(function(e){return e.nodeType===Fc}(t))return Ic(t.data,r);const i=Dc(t)?"DocumentFragment":t.tagName.toLowerCase();return++a>r.maxDepth?Nc(i,r):Bc(i,Ac(Dc(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),Dc(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,a,o,l),jc(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,a,o,l),r,n)}}}let Vc=null,$c=null,Wc=null;try{const e=module&&module.require;$c=e.call(module,"fs").readFileSync,Wc=e.call(module,"@babel/code-frame").codeFrameColumns,Vc=e.call(module,"chalk")}catch{}function Gc(){if(!$c||!Wc)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),a=n.split(":"),[o,l,i]=[a[0],parseInt(a[1],10),parseInt(a[2],10)];let u="";try{u=$c(o,"utf-8")}catch{return""}const s=Wc(u,{start:{line:l,column:i}},{highlightCode:!0,linesBelow:0});return Vc.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}const Qc=3;function Xc(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function Jc(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function Kc(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function Yc(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const Zc=()=>{let e;try{var t;e=JSON.parse(null==(t=process)||null==(t=t.env)?void 0:t.COLORS)}catch(e){}return"boolean"==typeof e?e:"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node},{DOMCollection:ep}=_e,tp=1,rp=8;function np(e){return e.nodeType!==rp&&(e.nodeType!==tp||!e.matches(up().defaultIgnore))}function ap(e,t,r){if(void 0===r&&(r={}),e||(e=Jc().body),"number"!=typeof t&&(t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:a=np,...o}=r,l=Te(e,{plugins:[zc(a),ep],printFunctionName:!1,highlight:Zc(),...o});return void 0!==t&&e.outerHTML.length>t?l.slice(0,t)+"...":l}const op=function(){const e=Gc();e?console.log(ap(...arguments)+"\n\n"+e):console.log(ap(...arguments))};let lp={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=ap(t),n=new Error([e,"Ignored nodes: comments, "+lp.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function ip(e){"function"==typeof e&&(e=e(lp)),lp={...lp,...e}}function up(){return lp}const sp=["button","meter","output","progress","select","textarea","input"];function dp(e){return sp.includes(e.nodeName.toLowerCase())?"":e.nodeType===Qc?e.textContent:Array.from(e.childNodes).map((e=>dp(e))).join("")}function cp(e){let t;return t="label"===e.tagName.toLowerCase()?dp(e):e.value||e.textContent,t}function pp(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function mp(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const a=t.getAttribute("aria-labelledby"),o=a?a.split(" "):[];return o.length?o.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:cp(r),formControl:null}:{content:"",formControl:null}})):Array.from(pp(t)).map((e=>({content:cp(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function fp(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function bp(e,t,r,n){if("string"!=typeof e)return!1;fp(r);const a=n(e);return"string"==typeof r||"number"==typeof r?a.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(a,t):gp(r,a)}function vp(e,t,r,n){if("string"!=typeof e)return!1;fp(r);const a=n(e);return r instanceof Function?r(a,t):r instanceof RegExp?gp(r,a):a===String(r)}function yp(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function hp(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return yp({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function gp(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function Cp(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>e.nodeType===Qc&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}const qp=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;const a=-1!==n.indexOf("undefined"),o=-1!==n.indexOf("set");return void 0!==r?"["+t+'="'+r+'"]':a?":not(["+t+"])":o?"["+t+"]:not(["+t+'=""])':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[a,o]of e.entries())n=[...n,{match:r(a),roles:Array.from(o),specificity:t(a)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(xc);function Pp(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function Ep(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=Pp}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function wp(e){for(const{match:t,roles:r}of qp)if(t(e))return[...r];return[]}function xp(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===Ep(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):wp(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function Rp(e,t){let{hidden:r,includeDescription:n}=t;const a=xp(e,{hidden:r});return Object.entries(a).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const a="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+Yt(e,{computedStyleSupportsPseudoElements:up().computedStyleSupportsPseudoElements})+'":\n',r=ap(e.cloneNode(!1));if(n){return""+t+('Description "'+Kt(e,{computedStyleSupportsPseudoElements:up().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+a})).join("\n")}function Op(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const Tp=yp();function _p(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function Mp(e,t,r,n){let{variant:a,name:o}=n,l="";const i={},u=[["Role","TestId"].includes(e)?r:_p(r)];o&&(i.name=_p(o)),"Role"===e&&Ep(t)&&(i.hidden=!0,l="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(i).length>0&&u.push(i);const s=a+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:a,warning:l,toString(){l&&console.warn(l);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function Ap(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function jp(e,t,r){var n,a;if(void 0===t&&(t="get"),e.matches(up().defaultIgnore))return;const o=null!=(n=e.getAttribute("role"))?n:null==(a=wp(e))?void 0:a[0];if("generic"!==o&&Ap("Role",r,o))return Mp("Role",e,o,{variant:t,name:Yt(e,{computedStyleSupportsPseudoElements:up().computedStyleSupportsPseudoElements})});const l=mp(document,e).map((e=>e.content)).join(" ");if(Ap("LabelText",r,l))return Mp("LabelText",e,l,{variant:t});const i=e.getAttribute("placeholder");if(Ap("PlaceholderText",r,i))return Mp("PlaceholderText",e,i,{variant:t});const u=Tp(Cp(e));if(Ap("Text",r,u))return Mp("Text",e,u,{variant:t});if(Ap("DisplayValue",r,e.value))return Mp("DisplayValue",e,Tp(e.value),{variant:t});const s=e.getAttribute("alt");if(Ap("AltText",r,s))return Mp("AltText",e,s,{variant:t});const d=e.getAttribute("title");if(Ap("Title",r,d))return Mp("Title",e,d,{variant:t});const c=e.getAttribute(up().testIdAttribute);return Ap("TestId",r,c)?Mp("TestId",e,c,{variant:t}):void 0}function Sp(e,t){e.stack=t.stack.replace(t.message,e.message)}function Ip(e,t){let{container:r=Jc(),timeout:n=up().asyncUtilTimeout,showOriginalStackTrace:a=up().showOriginalStackTrace,stackTraceError:o,interval:l=50,onTimeout:i=(e=>(Object.defineProperty(e,"message",{value:up().getElementError(e.message,r).message}),e)),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let d,c,p,m=!1,f="idle";const b=setTimeout((function(){let e;d?(e=d,a||"TestingLibraryElementError"!==e.name||Sp(e,o)):(e=new Error("Timed out in waitFor."),a||Sp(e,o)),y(i(e),null)}),n),v=Xc();if(v){const{unstable_advanceTimersWrapper:e}=up();for(g();!m;){if(!Xc()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||Sp(e,o),void s(e)}if(await e((async()=>{jest.advanceTimersByTime(l)})),m)break;g()}}else{try{Yc(r)}catch(e){return void s(e)}c=setInterval(h,l);const{MutationObserver:e}=Kc(r);p=new e(h),p.observe(r,u),g()}function y(e,r){m=!0,clearTimeout(b),v||(clearInterval(c),p.disconnect()),e?s(e):t(r)}function h(){if(Xc()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return a||Sp(e,o),s(e)}return g()}function g(){if("pending"!==f)try{const t=function(e){try{return lp._disableExpensiveErrorDiagnostics=!0,e()}finally{lp._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(f="pending",t.then((e=>{f="resolved",y(null,e)}),(e=>{f="rejected",d=e}))):y(null,t)}catch(e){d=e}}}))}function Bp(e,t){const r=new Error("STACK_TRACE_MESSAGE");return up().asyncWrapper((()=>Ip(e,{stackTraceError:r,...t})))}function Np(e,t){return up().getElementError(e,t)}function kp(e,t){return Np(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function Fp(e,t,r,n){let{exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===n?{}:n;const u=a?vp:bp,s=hp({collapseWhitespace:o,trim:l,normalizer:i});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function Lp(e,t,r,n){const a=Fp(e,t,r,n);if(a.length>1)throw kp("Found multiple elements by ["+e+"="+r+"]",t);return a[0]||null}function Hp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(l.length>1){const e=l.map((e=>Np(null,e).message)).join("\n\n");throw kp(t(r,...a)+"\n\nHere are the matching elements:\n\n"+e,r)}return l[0]||null}}function Up(e,t){return up().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function Dp(e,t){return function(r){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];const l=e(r,...a);if(!l.length)throw up().getElementError(t(r,...a),r);return l}}function zp(e){return(t,r,n,a)=>Bp((()=>e(t,r,n)),{container:t,...a})}const Vp=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=up().throwSuggestions}={}]=o.slice(-1);if(i&&u){const e=jp(i,r);if(e&&!t.endsWith(e.queryName))throw Up(e.toString(),n)}return i},$p=(e,t,r)=>function(n){for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];const i=e(n,...o),[{suggest:u=up().throwSuggestions}={}]=o.slice(-1);if(i.length&&u){const e=[...new Set(i.map((e=>{var t;return null==(t=jp(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(jp(i[0],r).queryName))throw Up(e[0],n)}return i};function Wp(e,t,r){const n=Vp(Hp(e,t),e.name,"query"),a=Dp(e,r),o=Hp(a,t),l=Vp(o,e.name,"get");return[n,$p(a,e.name.replace("query","get"),"getAll"),l,zp($p(a,e.name,"findAll")),zp(Vp(o,e.name,"find"))]}var Gp=Object.freeze({__proto__:null,getElementError:Np,wrapAllByQueryWithSuggestion:$p,wrapSingleQueryWithSuggestion:Vp,getMultipleElementsFoundError:kp,queryAllByAttribute:Fp,queryByAttribute:Lp,makeSingleQuery:Hp,makeGetAllQuery:Dp,makeFindQuery:zp,buildQueries:Wp});const Qp=function(e,t,r){let{exact:n=!0,trim:a,collapseWhitespace:o,normalizer:l}=void 0===r?{}:r;const i=n?vp:bp,u=hp({collapseWhitespace:o,trim:a,normalizer:l}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:cp(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return i(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},Xp=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,normalizer:i}=void 0===r?{}:r;Yc(e);const u=a?vp:bp,s=hp({collapseWhitespace:o,trim:l,normalizer:i}),d=Array.from(e.querySelectorAll("*")).filter((e=>pp(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,a)=>{const o=mp(e,a,{selector:n});o.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const l=o.filter((e=>Boolean(e.content))).map((e=>e.content));return u(l.join(" "),a,t,s)&&r.push(a),l.length>1&&l.forEach(((e,n)=>{u(e,a,t,s)&&r.push(a);const o=[...l];o.splice(n,1),o.length>1&&u(o.join(" "),a,t,s)&&r.push(a)})),r}),[]).concat(Fp("aria-label",e,t,{exact:a,normalizer:s}));return Array.from(new Set(d)).filter((e=>e.matches(n)))},Jp=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),a=2;a<r;a++)n[a-2]=arguments[a];const o=Xp(e,t,...n);if(!o.length){const r=Qp(e,t,...n);if(r.length){const n=r.map((t=>function(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}(e,t))).filter((e=>!!e));throw n.length?up().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):up().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw up().getElementError("Unable to find a label with the text of: "+t,e)}return o};const Kp=(e,t)=>"Found multiple elements with the text of: "+t,Yp=Vp(Hp(Xp,Kp),Xp.name,"query"),Zp=Hp(Jp,Kp),em=zp($p(Jp,Jp.name,"findAll")),tm=zp(Vp(Zp,Jp.name,"find")),rm=$p(Jp,Jp.name,"getAll"),nm=Vp(Zp,Jp.name,"get"),am=$p(Xp,Xp.name,"queryAll"),om=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Yc(t[0]),Fp("placeholder",...t)},lm=$p(om,om.name,"queryAll"),[im,um,sm,dm,cm]=Wp(om,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),pm=function(e,t,r){let{selector:n="*",exact:a=!0,collapseWhitespace:o,trim:l,ignore:i=up().defaultIgnore,normalizer:u}=void 0===r?{}:r;Yc(e);const s=a?vp:bp,d=hp({collapseWhitespace:o,trim:l,normalizer:u});let c=[];return"function"==typeof e.matches&&e.matches(n)&&(c=[e]),[...c,...Array.from(e.querySelectorAll(n))].filter((e=>!i||!e.matches(i))).filter((e=>s(Cp(e),e,t,d)))},mm=$p(pm,pm.name,"queryAll"),[fm,bm,vm,ym,hm]=Wp(pm,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:a,normalizer:o,selector:l}=r,i=hp({collapseWhitespace:n,trim:a,normalizer:o})(t.toString());return"Unable to find an element with the text: "+(i!==t.toString()?i+" (normalized from '"+t+"')":t)+("*"!==(null!=l?l:"*")?", which matches selector '"+l+"'":"")+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),gm=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;Yc(e);const i=n?vp:bp,u=hp({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>i(Cp(e),e,t,u)))}return i(e.value,e,t,u)}))},Cm=$p(gm,gm.name,"queryAll"),[qm,Pm,Em,wm,xm]=Wp(gm,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),Rm=/^(img|input|area|.+-.+)$/i,Om=function(e,t,r){return void 0===r&&(r={}),Yc(e),Fp("alt",e,t,r).filter((e=>Rm.test(e.tagName)))},Tm=$p(Om,Om.name,"queryAll"),[_m,Mm,Am,jm,Sm]=Wp(Om,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),Im=function(e,t,r){let{exact:n=!0,collapseWhitespace:a,trim:o,normalizer:l}=void 0===r?{}:r;Yc(e);const i=n?vp:bp,u=hp({collapseWhitespace:a,trim:o,normalizer:l});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>i(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&i(Cp(e),e,t,u)))},Bm=$p(Im,Im.name,"queryAll"),[Nm,km,Fm,Lm,Hm]=Wp(Im,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+".")),Um=function(e,t,r){let{hidden:n=up().defaultHidden,name:a,description:o,queryFallbacks:l=!1,selected:i,busy:u,checked:s,pressed:d,current:c,level:p,expanded:m,value:{now:f,min:b,max:v,text:y}={}}=void 0===r?{}:r;var h,g,C,q,P,E,w,x,R,O;if((Yc(e),void 0!==i)&&void 0===(null==(h=fc.get(t))?void 0:h.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==u&&void 0===(null==(g=fc.get(t))?void 0:g.props["aria-busy"]))throw new Error('"aria-busy" is not supported on role "'+t+'".');if(void 0!==s&&void 0===(null==(C=fc.get(t))?void 0:C.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==d&&void 0===(null==(q=fc.get(t))?void 0:q.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==c&&void 0===(null==(P=fc.get(t))?void 0:P.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==p&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==f&&void 0===(null==(E=fc.get(t))?void 0:E.props["aria-valuenow"]))throw new Error('"aria-valuenow" is not supported on role "'+t+'".');if(void 0!==v&&void 0===(null==(w=fc.get(t))?void 0:w.props["aria-valuemax"]))throw new Error('"aria-valuemax" is not supported on role "'+t+'".');if(void 0!==b&&void 0===(null==(x=fc.get(t))?void 0:x.props["aria-valuemin"]))throw new Error('"aria-valuemin" is not supported on role "'+t+'".');if(void 0!==y&&void 0===(null==(R=fc.get(t))?void 0:R.props["aria-valuetext"]))throw new Error('"aria-valuetext" is not supported on role "'+t+'".');if(void 0!==m&&void 0===(null==(O=fc.get(t))?void 0:O.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const T=new WeakMap;function _(e){return T.has(e)||T.set(e,Pp(e)),T.get(e)}return Array.from(e.querySelectorAll(function(e){var t;const r='*[role~="'+e+'"]',n=null!=(t=Oc.get(e))?t:new Set,a=new Set(Array.from(n).map((e=>{let{name:t}=e;return t})));return[r].concat(Array.from(a)).join(",")}(t))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(l)return r.split(" ").filter(Boolean).some((e=>e===t));const[n]=r.split(" ");return n===t}return wp(e).some((e=>e===t))})).filter((e=>{if(void 0!==i)return i===function(e){return"OPTION"===e.tagName?e.selected:Op(e,"aria-selected")}(e);if(void 0!==u)return u===function(e){return"true"===e.getAttribute("aria-busy")}(e);if(void 0!==s)return s===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:Op(e,"aria-checked")}(e);if(void 0!==d)return d===function(e){return Op(e,"aria-pressed")}(e);if(void 0!==c)return c===function(e){var t,r;return null!=(t=null!=(r=Op(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e);if(void 0!==m)return m===function(e){return Op(e,"aria-expanded")}(e);if(void 0!==p)return p===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e);if(void 0!==f||void 0!==v||void 0!==b||void 0!==y){let r=!0;var t;if(void 0!==f&&r&&(r=f===function(e){const t=e.getAttribute("aria-valuenow");return null===t?void 0:+t}(e)),void 0!==v&&r&&(r=v===function(e){const t=e.getAttribute("aria-valuemax");return null===t?void 0:+t}(e)),void 0!==b&&r&&(r=b===function(e){const t=e.getAttribute("aria-valuemin");return null===t?void 0:+t}(e)),void 0!==y)r&&(r=vp(null!=(t=function(e){const t=e.getAttribute("aria-valuetext");return null===t?void 0:t}(e))?t:null,e,y,(e=>e)));return r}return!0})).filter((e=>void 0===a||vp(Yt(e,{computedStyleSupportsPseudoElements:up().computedStyleSupportsPseudoElements}),e,a,(e=>e)))).filter((e=>void 0===o||vp(Kt(e,{computedStyleSupportsPseudoElements:up().computedStyleSupportsPseudoElements}),e,o,(e=>e)))).filter((e=>!1!==n||!1===Ep(e,{isSubtreeInaccessible:_})))};const Dm=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},zm=$p(Um,Um.name,"queryAll"),[Vm,$m,Wm,Gm,Qm]=Wp(Um,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+Dm(n)}),(function(e,t,r){let{hidden:n=up().defaultHidden,name:a,description:o}=void 0===r?{}:r;if(up()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+Dm(a);let l,i="";Array.from(e.children).forEach((e=>{i+=Rp(e,{hidden:n,includeDescription:void 0!==o})})),l=0===i.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+i.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===a?"":"string"==typeof a?' and name "'+a+'"':" and name `"+a+"`";let s="";return s=void 0===o?"":"string"==typeof o?' and description "'+o+'"':" and description `"+o+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+l).trim()})),Xm=()=>up().testIdAttribute,Jm=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Yc(t[0]),Fp(Xm(),...t)},Km=$p(Jm,Jm.name,"queryAll"),[Ym,Zm,ef,tf,rf]=Wp(Jm,((e,t)=>"Found multiple elements by: ["+Xm()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+Xm()+'="'+t+'"]'));var nf=Object.freeze({__proto__:null,queryAllByLabelText:am,queryByLabelText:Yp,getAllByLabelText:rm,getByLabelText:nm,findAllByLabelText:em,findByLabelText:tm,queryByPlaceholderText:im,queryAllByPlaceholderText:lm,getByPlaceholderText:sm,getAllByPlaceholderText:um,findAllByPlaceholderText:dm,findByPlaceholderText:cm,queryByText:fm,queryAllByText:mm,getByText:vm,getAllByText:bm,findAllByText:ym,findByText:hm,queryByDisplayValue:qm,queryAllByDisplayValue:Cm,getByDisplayValue:Em,getAllByDisplayValue:Pm,findAllByDisplayValue:wm,findByDisplayValue:xm,queryByAltText:_m,queryAllByAltText:Tm,getByAltText:Am,getAllByAltText:Mm,findAllByAltText:jm,findByAltText:Sm,queryByTitle:Nm,queryAllByTitle:Bm,getByTitle:Fm,getAllByTitle:km,findAllByTitle:Lm,findByTitle:Hm,queryByRole:Vm,queryAllByRole:zm,getAllByRole:$m,getByRole:Wm,findAllByRole:Gm,findByRole:Qm,queryByTestId:Ym,queryAllByTestId:Km,getByTestId:ef,getAllByTestId:Zm,findAllByTestId:tf,findByTestId:rf});function af(e,t,r){return void 0===t&&(t=nf),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const a=t[n];return r[n]=a.bind(null,e),r}),r)}const of=e=>!e||Array.isArray(e)&&!e.length;function lf(e){if(of(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const uf={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}},offline:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},online:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},pageHide:{EventType:"PageTransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},pageShow:{EventType:"PageTransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}}},sf={doubleClick:"dblClick"};function df(e,t){return up().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function cf(e,t,r,n){let{EventType:a="Event",defaultInit:o={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const l={...o,...r},{target:{value:i,files:u,...s}={}}=l;void 0!==i&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:a}=Object.getOwnPropertyDescriptor(n,"value")||{};if(a&&r!==a)a.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,i),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const d=Kc(t),c=d[a]||d.Event;let p;if("function"==typeof c)p=new c(e,l);else{p=d.document.createEvent(a);const{bubbles:t,cancelable:r,detail:n,...o}=l;p.initEvent(e,t,r,n),Object.keys(o).forEach((e=>{p[e]=o[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=l[e];"object"==typeof t&&("function"==typeof d.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new d.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}function pf(e){return"https://testing-playground.com/#markup="+(t=e,_c.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}Object.keys(uf).forEach((e=>{const{EventType:t,defaultInit:r}=uf[e],n=e.toLowerCase();cf[e]=(e,a)=>cf(n,e,a,{EventType:t,defaultInit:r}),df[e]=(t,r)=>df(t,cf[e](t,r))})),Object.keys(sf).forEach((e=>{const t=sf[e];df[e]=function(){return df[t](...arguments)}}));const mf={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>op(e,t,r))):op(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=Jc().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=pf(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},ff="undefined"!=typeof document&&document.body?af(document.body,nf,mf):Object.keys(nf).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),mf),bf="function"==typeof u.act?u.act:c.act;function vf(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}function yf(e){vf().IS_REACT_ACT_ENVIRONMENT=e}function hf(){return vf().IS_REACT_ACT_ENVIRONMENT}const gf=(Cf=bf,e=>{const t=hf();yf(!0);try{let r=!1;const n=Cf((()=>{const t=e();return null!==t&&"object"==typeof t&&"function"==typeof t.then&&(r=!0),t}));return r?{then:(e,r)=>{n.then((r=>{yf(t),e(r)}),(e=>{yf(t),r(e)}))}}:(yf(t),n)}catch(e){throw yf(t),e}});var Cf;const qf=function(){return df(...arguments)};Object.keys(df).forEach((e=>{qf[e]=function(){return df[e](...arguments)}}));const Pf=qf.mouseEnter,Ef=qf.mouseLeave;qf.mouseEnter=function(){return Pf(...arguments),qf.mouseOver(...arguments)},qf.mouseLeave=function(){return Ef(...arguments),qf.mouseOut(...arguments)};const wf=qf.pointerEnter,xf=qf.pointerLeave;qf.pointerEnter=function(){return wf(...arguments),qf.pointerOver(...arguments)},qf.pointerLeave=function(){return xf(...arguments),qf.pointerOut(...arguments)};const Rf=qf.select;qf.select=(e,t)=>{Rf(e,t),e.focus(),qf.keyUp(e,t)};const Of=qf.blur,Tf=qf.focus;qf.blur=function(){return qf.focusOut(...arguments),Of(...arguments)},qf.focus=function(){return qf.focusIn(...arguments),Tf(...arguments)};let _f={reactStrictMode:!1};function Mf(){return{...up(),..._f}}ip({unstable_advanceTimersWrapper:e=>gf(e),asyncWrapper:async e=>{const t=hf();yf(!1);try{const t=await e();return await new Promise((e=>{setTimeout((()=>{e()}),0),"undefined"==typeof jest||null===jest||!0!==setTimeout._isMockFunction&&!Object.prototype.hasOwnProperty.call(setTimeout,"clock")||jest.advanceTimersByTime(0)})),t}finally{yf(t)}},eventWrapper:e=>{let t;return gf((()=>{t=e()})),t}});const Af=new Set,jf=[];function Sf(e){return Mf().reactStrictMode?u.createElement(u.StrictMode,null,e):e}function If(e,t){return t?u.createElement(t,null,e):e}function Bf(e,t){let r,{hydrate:n,ui:a,wrapper:o}=t;return n?gf((()=>{r=d.hydrateRoot(e,Sf(If(a,o)))})):r=d.createRoot(e),{hydrate(){if(!n)throw new Error("Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.")},render(e){r.render(e)},unmount(){r.unmount()}}}function Nf(e){return{hydrate(t){s.default.hydrate(t,e)},render(t){s.default.render(t,e)},unmount(){s.default.unmountComponentAtNode(e)}}}function kf(e,t){let{baseElement:r,container:n,hydrate:a,queries:o,root:l,wrapper:i}=t;return gf((()=>{a?l.hydrate(Sf(If(e,i)),n):l.render(Sf(If(e,i)),n)})),{container:n,baseElement:r,debug:function(e,t,n){return void 0===e&&(e=r),Array.isArray(e)?e.forEach((e=>console.log(ap(e,t,n)))):console.log(ap(e,t,n))},unmount:()=>{gf((()=>{l.unmount()}))},rerender:e=>{kf(e,{container:n,baseElement:r,root:l,wrapper:i})},asFragment:()=>{if("function"==typeof document.createRange)return document.createRange().createContextualFragment(n.innerHTML);{const e=document.createElement("template");return e.innerHTML=n.innerHTML,e.content}},...af(r,o)}}function Ff(e,t){let r,{container:n,baseElement:a=n,legacyRoot:o=!1,queries:l,hydrate:i=!1,wrapper:u}=void 0===t?{}:t;if(o&&"function"!=typeof s.default.render){const e=new Error("`legacyRoot: true` is not supported in this version of React. If your app runs React 19 or later, you should remove this flag. If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.");throw Error.captureStackTrace(e,Ff),e}if(a||(a=document.body),n||(n=a.appendChild(document.createElement("div"))),Af.has(n))jf.forEach((e=>{e.container===n&&(r=e.root)}));else{r=(o?Nf:Bf)(n,{hydrate:i,ui:e,wrapper:u}),jf.push({container:n,root:r}),Af.add(n)}return kf(e,{container:n,baseElement:a,queries:l,hydrate:i,wrapper:u,root:r})}e.act=gf,e.buildQueries=Wp,e.cleanup=function(){jf.forEach((e=>{let{root:t,container:r}=e;gf((()=>{t.unmount()})),r.parentNode===document.body&&document.body.removeChild(r)})),jf.length=0,Af.clear()},e.configure=function(e){"function"==typeof e&&(e=e(Mf()));const{reactStrictMode:t,...r}=e;ip(r),_f={..._f,reactStrictMode:t}},e.createEvent=cf,e.findAllByAltText=jm,e.findAllByDisplayValue=wm,e.findAllByLabelText=em,e.findAllByPlaceholderText=dm,e.findAllByRole=Gm,e.findAllByTestId=tf,e.findAllByText=ym,e.findAllByTitle=Lm,e.findByAltText=Sm,e.findByDisplayValue=xm,e.findByLabelText=tm,e.findByPlaceholderText=cm,e.findByRole=Qm,e.findByTestId=rf,e.findByText=hm,e.findByTitle=Hm,e.fireEvent=qf,e.getAllByAltText=Mm,e.getAllByDisplayValue=Pm,e.getAllByLabelText=rm,e.getAllByPlaceholderText=um,e.getAllByRole=$m,e.getAllByTestId=Zm,e.getAllByText=bm,e.getAllByTitle=km,e.getByAltText=Am,e.getByDisplayValue=Em,e.getByLabelText=nm,e.getByPlaceholderText=sm,e.getByRole=Wm,e.getByTestId=ef,e.getByText=vm,e.getByTitle=Fm,e.getConfig=Mf,e.getDefaultNormalizer=yp,e.getElementError=Np,e.getMultipleElementsFoundError=kp,e.getNodeText=Cp,e.getQueriesForElement=af,e.getRoles=xp,e.getSuggestedQuery=jp,e.isInaccessible=Ep,e.logDOM=op,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(Rp(e,{hidden:r}))},e.makeFindQuery=zp,e.makeGetAllQuery=Dp,e.makeSingleQuery=Hp,e.prettyDOM=ap,e.prettyFormat=bt,e.queries=nf,e.queryAllByAltText=Tm,e.queryAllByAttribute=Fp,e.queryAllByDisplayValue=Cm,e.queryAllByLabelText=am,e.queryAllByPlaceholderText=lm,e.queryAllByRole=zm,e.queryAllByTestId=Km,e.queryAllByText=mm,e.queryAllByTitle=Bm,e.queryByAltText=_m,e.queryByAttribute=Lp,e.queryByDisplayValue=qm,e.queryByLabelText=Yp,e.queryByPlaceholderText=im,e.queryByRole=Vm,e.queryByTestId=Ym,e.queryByText=fm,e.queryByTitle=Nm,e.queryHelpers=Gp,e.render=Ff,e.renderHook=function e(t,r){void 0===r&&(r={});const{initialProps:n,...a}=r;if(a.legacyRoot&&"function"!=typeof s.default.render){const t=new Error("`legacyRoot: true` is not supported in this version of React. If your app runs React 19 or later, you should remove this flag. If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.");throw Error.captureStackTrace(t,e),t}const o=u.createRef();function l(e){let{renderCallbackProps:r}=e;const n=t(r);return u.useEffect((()=>{o.current=n})),null}const{rerender:i,unmount:d}=Ff(u.createElement(l,{renderCallbackProps:n}),a);return{result:o,rerender:function(e){return i(u.createElement(l,{renderCallbackProps:e}))},unmount:d}},e.screen=ff,e.waitFor=Bp,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){lf(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return lf(e()),Bp((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!of(t))throw r}),t)},e.within=af,e.wrapAllByQueryWithSuggestion=$p,e.wrapSingleQueryWithSuggestion=Vp,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react.pure.umd.min.js.map
