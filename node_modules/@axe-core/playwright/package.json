{"name": "@axe-core/playwright", "version": "4.10.2", "description": "Provides a method to inject and analyze web pages using axe", "contributors": [{"name": "<PERSON> (<EMAIL>)"}], "files": ["/dist"], "keywords": ["a11y", "unit", "testing", "tdd", "bdd", "accessibility", "axe", "playwright", "axe-core"], "repository": {"type": "git", "url": "https://github.com/dequelabs/axe-core-npm.git"}, "license": "MPL-2.0", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "publishConfig": {"access": "public"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup src/index.ts --dts --format esm,cjs", "test": "mocha --timeout 60000 -r ts-node/register 'test/**.spec.ts'", "test:export": "npm run test:esm && npm run test:commonjs && npm run test:ts", "test:esm": "node test/esmTest.mjs", "test:commonjs": "node test/commonjsTest.js", "test:ts": "tsc test/tsTest.ts --noEmit --skipLib<PERSON><PERSON>ck --esModuleInterop", "coverage": "nyc npm run test", "prepare": "npx playwright install && npm run build"}, "dependencies": {"axe-core": "~4.10.3"}, "devDependencies": {"@playwright/test": "^1.44.0", "@types/chai": "^4.3.3", "@types/express": "^4.17.14", "@types/mocha": "^10.0.0", "@types/node": "^22.0.2", "async-listen": "^3.0.1", "axe-test-fixtures": "github:dequelabs/axe-test-fixtures#v1", "chai": "^4.3.6", "express": "^4.18.2", "mocha": "^10.0.0", "nyc": "^17.1.0", "rimraf": "^6.0.1", "ts-node": "^10.9.1", "tsup": "^8.0.1", "typescript": "^5.2.2"}, "peerDependencies": {"playwright-core": ">= 1.0.0"}, "nyc": {"include": ["src/index.ts"], "extension": [".ts"], "reporter": ["text-summary", "html"], "sourceMap": true, "instrument": true, "checkCoverage": true, "statements": 95, "branches": 90, "functions": 100, "lines": 95}, "gitHead": "1055c5d005e72ad190987c358ebb361631909a85"}