import { prisma } from '@/lib/prisma';

export type SearchParams = {
  q?: string;
  categorySlug?: string;
  brand?: string;
  min?: number;
  max?: number;
  condition?: string;
  take?: number;
  skip?: number;
};

export async function searchProducts(p: SearchParams) {
  const where: any = {};
  if (p.q) {
    where.OR = [
      { title: { contains: p.q, mode: 'insensitive' } },
      { description: { contains: p.q, mode: 'insensitive' } },
      { brand: { name: { contains: p.q, mode: 'insensitive' } } }
    ];
  }
  if (p.categorySlug) where.category = { slug: p.categorySlug };
  if (p.brand) where.brand = { name: { equals: p.brand, mode: 'insensitive' } };

  const products = await prisma.product.findMany({
    where: { status: 'PUBLISHED', ...where },
    include: { brand: true, category: true, media: { orderBy: { position: 'asc' } }, variants: { include: { inventory: true } } },
    orderBy: { createdAt: 'desc' },
    take: p.take ?? 24,
    skip: p.skip ?? 0
  });
  return products;
}
