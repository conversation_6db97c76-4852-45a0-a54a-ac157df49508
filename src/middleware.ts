import { NextResponse, type NextRequest } from 'next/server';

const buckets = new Map<string, { tokens: number; updated: number }>();
const RATE = { capacity: 60, intervalMs: 60_000, refillPerMs: 60 / 60_000 };

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;
  if (pathname.startsWith('/api')) {
    const ip = req.ip ?? req.headers.get('x-forwarded-for') ?? 'unknown';
    const now = Date.now();
    const b = buckets.get(ip) ?? { tokens: RATE.capacity, updated: now };
    const elapsed = now - b.updated;
    b.tokens = Math.min(RATE.capacity, b.tokens + elapsed * RATE.refillPerMs);
    b.updated = now;
    if (b.tokens < 1) return new NextResponse('Rate limit', { status: 429 });
    b.tokens -= 1; buckets.set(ip, b);

    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
      const origin = req.headers.get('origin');
      const base = process.env.NEXTAUTH_URL;
      if (origin && base && !origin.startsWith(base)) {
        return new NextResponse('Bad origin', { status: 403 });
      }
    }
  }
  return NextResponse.next();
}

export const config = { matcher: ['/api/:path*'] };
