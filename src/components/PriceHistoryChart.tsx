'use client';

import { useState, useEffect } from 'react';

interface PricePoint {
  date: string;
  price: number;
  store: string;
  event?: string;
}

interface PriceHistoryChartProps {
  productId: string;
  currentPrice: number;
}

export default function PriceHistoryChart({ productId, currentPrice }: PriceHistoryChartProps) {
  const [priceHistory, setPriceHistory] = useState<PricePoint[]>([]);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [isLoading, setIsLoading] = useState(true);

  // Mock price history data
  const mockPriceHistory: Record<string, PricePoint[]> = {
    'iphone-15-pro-max': [
      { date: '2024-01-01', price: 1199.99, store: 'ElectroHub', event: 'Launch' },
      { date: '2024-01-15', price: 1189.99, store: 'ElectroHub' },
      { date: '2024-02-01', price: 1199.99, store: 'ElectroHub' },
      { date: '2024-02-14', price: 1179.99, store: 'ElectroHub', event: 'Valentine Sale' },
      { date: '2024-02-20', price: 1199.99, store: 'ElectroHub' },
      { date: '2024-03-01', price: 1189.99, store: 'ElectroHub' },
      { date: '2024-03-15', price: 1199.99, store: 'ElectroHub' },
      { date: '2024-03-20', price: 1199.99, store: 'ElectroHub' }
    ],
    'macbook-air-m3': [
      { date: '2024-01-01', price: 1299.99, store: 'ElectroHub' },
      { date: '2024-01-15', price: 1299.99, store: 'ElectroHub' },
      { date: '2024-02-01', price: 1289.99, store: 'ElectroHub' },
      { date: '2024-02-15', price: 1299.99, store: 'ElectroHub' },
      { date: '2024-03-01', price: 1299.99, store: 'ElectroHub' }
    ],
    'sony-wh-1000xm5': [
      { date: '2024-01-01', price: 399.99, store: 'ElectroHub' },
      { date: '2024-01-15', price: 389.99, store: 'ElectroHub', event: 'Flash Sale' },
      { date: '2024-02-01', price: 399.99, store: 'ElectroHub' },
      { date: '2024-02-15', price: 379.99, store: 'ElectroHub', event: 'Price Match' },
      { date: '2024-03-01', price: 399.99, store: 'ElectroHub' }
    ]
  };

  useEffect(() => {
    const fetchPriceHistory = async () => {
      setIsLoading(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const history = mockPriceHistory[productId] || [];
      setPriceHistory(history);
      setIsLoading(false);
    };

    fetchPriceHistory();
  }, [productId, timeRange]);

  if (isLoading) {
    return (
      <div style={{ 
        backgroundColor: '#f8f9fa',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '2rem',
        textAlign: 'center'
      }}>
        <div style={{ 
          width: '40px', 
          height: '40px', 
          border: '4px solid #007bff',
          borderTop: '4px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          margin: '0 auto 1rem'
        }} />
        <div>Loading price history...</div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (priceHistory.length === 0) {
    return (
      <div style={{ 
        backgroundColor: '#f8f9fa',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '2rem',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>📊</div>
        <div>No price history available for this product</div>
      </div>
    );
  }

  const minPrice = Math.min(...priceHistory.map(p => p.price));
  const maxPrice = Math.max(...priceHistory.map(p => p.price));
  const priceRange = maxPrice - minPrice;
  const currentSavings = maxPrice - currentPrice;
  const avgPrice = priceHistory.reduce((sum, p) => sum + p.price, 0) / priceHistory.length;

  // Calculate chart dimensions
  const chartWidth = 600;
  const chartHeight = 200;
  const padding = 40;

  const getYPosition = (price: number) => {
    if (priceRange === 0) return chartHeight / 2;
    return chartHeight - ((price - minPrice) / priceRange) * (chartHeight - padding * 2) - padding;
  };

  const getXPosition = (index: number) => {
    return (index / (priceHistory.length - 1)) * (chartWidth - padding * 2) + padding;
  };

  // Create SVG path for price line
  const pathData = priceHistory.map((point, index) => {
    const x = getXPosition(index);
    const y = getYPosition(point.price);
    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
  }).join(' ');

  return (
    <div style={{ 
      backgroundColor: '#ffffff',
      border: '1px solid #e1e5e9',
      borderRadius: '12px',
      padding: '1.5rem',
      marginBottom: '2rem'
    }}>
      {/* Header */}
      <div style={{ 
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '1.5rem'
      }}>
        <h3 style={{ margin: 0 }}>📈 Price History</h3>
        
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          {(['7d', '30d', '90d', '1y'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: timeRange === range ? '#007bff' : '#f8f9fa',
                color: timeRange === range ? 'white' : '#666',
                border: '1px solid #e1e5e9',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '0.9rem'
              }}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Stats */}
      <div style={{ 
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
        gap: '1rem',
        marginBottom: '1.5rem'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#28a745' }}>
            ${minPrice.toFixed(2)}
          </div>
          <div style={{ fontSize: '0.8rem', color: '#666' }}>Lowest Price</div>
        </div>
        
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#007bff' }}>
            ${avgPrice.toFixed(2)}
          </div>
          <div style={{ fontSize: '0.8rem', color: '#666' }}>Average Price</div>
        </div>
        
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#dc3545' }}>
            ${maxPrice.toFixed(2)}
          </div>
          <div style={{ fontSize: '0.8rem', color: '#666' }}>Highest Price</div>
        </div>
        
        {currentSavings > 0 && (
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: '#28a745' }}>
              ${currentSavings.toFixed(2)}
            </div>
            <div style={{ fontSize: '0.8rem', color: '#666' }}>Current Savings</div>
          </div>
        )}
      </div>

      {/* Chart */}
      <div style={{ 
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        padding: '1rem',
        marginBottom: '1rem'
      }}>
        <svg 
          width="100%" 
          height={chartHeight}
          viewBox={`0 0 ${chartWidth} ${chartHeight}`}
          style={{ overflow: 'visible' }}
        >
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio) => {
            const y = padding + ratio * (chartHeight - padding * 2);
            const price = maxPrice - ratio * priceRange;
            return (
              <g key={ratio}>
                <line
                  x1={padding}
                  y1={y}
                  x2={chartWidth - padding}
                  y2={y}
                  stroke="#e1e5e9"
                  strokeWidth="1"
                />
                <text
                  x={padding - 10}
                  y={y + 4}
                  fontSize="12"
                  fill="#666"
                  textAnchor="end"
                >
                  ${price.toFixed(0)}
                </text>
              </g>
            );
          })}

          {/* Price line */}
          <path
            d={pathData}
            fill="none"
            stroke="#007bff"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />

          {/* Data points */}
          {priceHistory.map((point, index) => {
            const x = getXPosition(index);
            const y = getYPosition(point.price);
            
            return (
              <g key={index}>
                <circle
                  cx={x}
                  cy={y}
                  r="4"
                  fill={point.event ? '#ffc107' : '#007bff'}
                  stroke="white"
                  strokeWidth="2"
                />
                
                {point.event && (
                  <g>
                    <circle
                      cx={x}
                      cy={y}
                      r="8"
                      fill="none"
                      stroke="#ffc107"
                      strokeWidth="2"
                      opacity="0.5"
                    />
                    <text
                      x={x}
                      y={y - 15}
                      fontSize="10"
                      fill="#856404"
                      textAnchor="middle"
                      fontWeight="bold"
                    >
                      {point.event}
                    </text>
                  </g>
                )}
              </g>
            );
          })}

          {/* Date labels */}
          {priceHistory.map((point, index) => {
            if (index % Math.ceil(priceHistory.length / 5) === 0) {
              const x = getXPosition(index);
              const date = new Date(point.date).toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
              });
              
              return (
                <text
                  key={index}
                  x={x}
                  y={chartHeight - 10}
                  fontSize="12"
                  fill="#666"
                  textAnchor="middle"
                >
                  {date}
                </text>
              );
            }
            return null;
          })}
        </svg>
      </div>

      {/* Price trend analysis */}
      <div style={{ 
        backgroundColor: currentPrice < avgPrice ? '#d4edda' : '#fff3cd',
        border: `1px solid ${currentPrice < avgPrice ? '#c3e6cb' : '#ffeaa7'}`,
        borderRadius: '8px',
        padding: '1rem'
      }}>
        <div style={{ 
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          marginBottom: '0.5rem'
        }}>
          <span style={{ fontSize: '1.2rem' }}>
            {currentPrice < avgPrice ? '📉' : '📈'}
          </span>
          <strong style={{ 
            color: currentPrice < avgPrice ? '#155724' : '#856404'
          }}>
            Price Trend Analysis
          </strong>
        </div>
        
        <div style={{ 
          fontSize: '0.9rem',
          color: currentPrice < avgPrice ? '#155724' : '#856404'
        }}>
          {currentPrice < avgPrice ? (
            `Current price is ${((1 - currentPrice/avgPrice) * 100).toFixed(1)}% below average. Great time to buy!`
          ) : (
            `Current price is ${((currentPrice/avgPrice - 1) * 100).toFixed(1)}% above average. Consider waiting for a better deal.`
          )}
        </div>
      </div>
    </div>
  );
}
