'use client';

import { useState, useEffect } from 'react';
import PriceAlertModal from './PriceAlertModal';
import PriceHistoryChart from './PriceHistoryChart';

interface CompetitorPrice {
  store: string;
  price: number;
  originalPrice?: number;
  url: string;
  logo: string;
  shipping: string;
  availability: 'in_stock' | 'limited' | 'out_of_stock';
  lastUpdated: string;
  trustScore: number;
}

interface PriceComparisonProps {
  productName: string;
  ourPrice: number;
  productId: string;
}

export default function PriceComparison({ productName, ourPrice, productId }: PriceComparisonProps) {
  const [competitors, setCompetitors] = useState<CompetitorPrice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [showAll, setShowAll] = useState(false);
  const [showPriceAlert, setShowPriceAlert] = useState(false);
  const [showPriceHistory, setShowPriceHistory] = useState(false);

  // Mock competitor data - in real app, this would come from API
  const mockCompetitorData: Record<string, CompetitorPrice[]> = {
    'iphone-15-pro-max': [
      {
        store: 'Amazon',
        price: 1189.99,
        originalPrice: 1199.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '2 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 1199.00,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '5 min ago',
        trustScore: 4.7
      },
      {
        store: 'Apple Store',
        price: 1199.99,
        url: 'https://apple.com',
        logo: '🍎',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 5.0
      },
      {
        store: 'B&H Photo',
        price: 1179.99,
        url: 'https://bhphotovideo.com',
        logo: '📷',
        shipping: 'Free shipping',
        availability: 'limited',
        lastUpdated: '8 min ago',
        trustScore: 4.6
      },
      {
        store: 'Walmart',
        price: 1199.99,
        url: 'https://walmart.com',
        logo: '🏬',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '12 min ago',
        trustScore: 4.3
      },
      {
        store: 'Target',
        price: 1209.99,
        url: 'https://target.com',
        logo: '🎯',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '15 min ago',
        trustScore: 4.4
      }
    ],
    'macbook-air-m3': [
      {
        store: 'Apple Store',
        price: 1299.99,
        url: 'https://apple.com',
        logo: '🍎',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 5.0
      },
      {
        store: 'Amazon',
        price: 1299.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '3 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 1299.99,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '7 min ago',
        trustScore: 4.7
      },
      {
        store: 'Costco',
        price: 1279.99,
        url: 'https://costco.com',
        logo: '🏢',
        shipping: 'Free shipping',
        availability: 'limited',
        lastUpdated: '20 min ago',
        trustScore: 4.5
      }
    ],
    'sony-wh-1000xm5': [
      {
        store: 'Amazon',
        price: 379.99,
        originalPrice: 399.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 399.99,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '4 min ago',
        trustScore: 4.7
      },
      {
        store: 'Sony Direct',
        price: 399.99,
        url: 'https://sony.com',
        logo: '📺',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '2 min ago',
        trustScore: 4.9
      },
      {
        store: 'Target',
        price: 389.99,
        url: 'https://target.com',
        logo: '🎯',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '6 min ago',
        trustScore: 4.4
      }
    ]
  };

  useEffect(() => {
    // Simulate API call to fetch competitor prices
    const fetchPrices = async () => {
      setIsLoading(true);
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const competitorData = mockCompetitorData[productId] || [];
      setCompetitors(competitorData);
      setLastUpdated(new Date());
      setIsLoading(false);
    };

    fetchPrices();

    // Set up real-time updates every 5 minutes
    const interval = setInterval(fetchPrices, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [productId]);

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'in_stock': return '#28a745';
      case 'limited': return '#ffc107';
      case 'out_of_stock': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case 'in_stock': return 'In Stock';
      case 'limited': return 'Limited Stock';
      case 'out_of_stock': return 'Out of Stock';
      default: return 'Unknown';
    }
  };

  const lowestPrice = Math.min(...competitors.map(c => c.price));
  const ourRank = competitors.filter(c => c.price < ourPrice).length + 1;
  const savings = competitors.length > 0 ? Math.max(0, Math.max(...competitors.map(c => c.price)) - ourPrice) : 0;

  const displayedCompetitors = showAll ? competitors : competitors.slice(0, 3);

  if (isLoading) {
    return (
      <div style={{ 
        backgroundColor: '#f8f9fa',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
          <div style={{ 
            width: '20px', 
            height: '20px', 
            border: '2px solid #007bff',
            borderTop: '2px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <h3 style={{ margin: 0 }}>🔍 Checking competitor prices...</h3>
        </div>
        <p style={{ color: '#666', margin: 0 }}>
          Scanning major retailers for the best deals on {productName}
        </p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{ 
      backgroundColor: '#ffffff',
      border: '1px solid #e1e5e9',
      borderRadius: '12px',
      padding: '1.5rem',
      marginBottom: '2rem'
    }}>
      {/* Header */}
      <div style={{ marginBottom: '1.5rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
          <h3 style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            💰 Real-Time Price Comparison
          </h3>
          <div style={{ fontSize: '0.8rem', color: '#666' }}>
            🔄 Updated {lastUpdated.toLocaleTimeString()}
          </div>
        </div>
        
        {/* Our Price Highlight */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '1rem',
          padding: '1rem',
          backgroundColor: ourPrice <= lowestPrice ? '#d4edda' : '#fff3cd',
          borderRadius: '8px',
          marginBottom: '1rem'
        }}>
          <div>
            <div style={{ fontWeight: 'bold', color: '#333' }}>ElectroHub Price</div>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#007bff' }}>
              ${ourPrice.toFixed(2)}
            </div>
          </div>
          
          <div style={{ flex: 1 }}>
            {ourPrice <= lowestPrice ? (
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '0.5rem',
                color: '#155724',
                fontWeight: 'bold'
              }}>
                🏆 BEST PRICE GUARANTEED
                {savings > 0 && (
                  <span style={{ 
                    backgroundColor: '#28a745',
                    color: 'white',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '12px',
                    fontSize: '0.8rem'
                  }}>
                    Save ${savings.toFixed(2)}
                  </span>
                )}
              </div>
            ) : (
              <div style={{ color: '#856404' }}>
                #{ourRank} of {competitors.length + 1} retailers
                {lowestPrice < ourPrice && (
                  <div style={{ fontSize: '0.9rem' }}>
                    Lowest found: ${lowestPrice.toFixed(2)}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Competitor Prices */}
      <div style={{ marginBottom: '1rem' }}>
        <h4 style={{ marginBottom: '1rem', color: '#333' }}>
          🏪 Compare with {competitors.length} other retailers:
        </h4>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          {displayedCompetitors.map((competitor, index) => (
            <div key={index} style={{ 
              display: 'grid',
              gridTemplateColumns: '60px 1fr auto auto auto',
              alignItems: 'center',
              gap: '1rem',
              padding: '1rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              border: competitor.price === lowestPrice ? '2px solid #28a745' : '1px solid #e1e5e9'
            }}>
              {/* Store Logo & Name */}
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '1.5rem', marginBottom: '0.25rem' }}>
                  {competitor.logo}
                </div>
                <div style={{ fontSize: '0.7rem', color: '#666' }}>
                  ⭐ {competitor.trustScore}
                </div>
              </div>
              
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                  {competitor.store}
                </div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>
                  {competitor.shipping} • {competitor.lastUpdated}
                </div>
              </div>
              
              {/* Price */}
              <div style={{ textAlign: 'right' }}>
                <div style={{ 
                  fontSize: '1.2rem', 
                  fontWeight: 'bold',
                  color: competitor.price < ourPrice ? '#dc3545' : 
                         competitor.price > ourPrice ? '#28a745' : '#007bff'
                }}>
                  ${competitor.price.toFixed(2)}
                </div>
                {competitor.originalPrice && competitor.originalPrice > competitor.price && (
                  <div style={{ 
                    fontSize: '0.9rem', 
                    color: '#666',
                    textDecoration: 'line-through'
                  }}>
                    ${competitor.originalPrice.toFixed(2)}
                  </div>
                )}
              </div>
              
              {/* Availability */}
              <div style={{ textAlign: 'center' }}>
                <div style={{ 
                  padding: '0.25rem 0.5rem',
                  backgroundColor: getAvailabilityColor(competitor.availability) + '20',
                  color: getAvailabilityColor(competitor.availability),
                  borderRadius: '12px',
                  fontSize: '0.8rem',
                  fontWeight: 'bold'
                }}>
                  {getAvailabilityText(competitor.availability)}
                </div>
              </div>
              
              {/* Visit Store Button */}
              <div>
                <a
                  href={competitor.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#007bff',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '6px',
                    fontSize: '0.8rem',
                    fontWeight: 'bold'
                  }}
                >
                  Visit Store →
                </a>
              </div>
            </div>
          ))}
        </div>
        
        {competitors.length > 3 && (
          <button
            onClick={() => setShowAll(!showAll)}
            style={{
              marginTop: '1rem',
              padding: '0.75rem 1.5rem',
              backgroundColor: 'transparent',
              color: '#007bff',
              border: '1px solid #007bff',
              borderRadius: '6px',
              cursor: 'pointer',
              fontWeight: 'bold',
              width: '100%'
            }}
          >
            {showAll ? '▲ Show Less' : `▼ Show All ${competitors.length} Retailers`}
          </button>
        )}
      </div>

      {/* Action Buttons */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1rem',
        marginBottom: '1rem'
      }}>
        <button
          onClick={() => setShowPriceAlert(true)}
          style={{
            padding: '1rem',
            backgroundColor: '#ffc107',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontWeight: 'bold',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem'
          }}
        >
          🔔 Set Price Alert
        </button>

        <button
          onClick={() => setShowPriceHistory(!showPriceHistory)}
          style={{
            padding: '1rem',
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            fontWeight: 'bold',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem'
          }}
        >
          📈 {showPriceHistory ? 'Hide' : 'Show'} Price History
        </button>
      </div>

      {/* Price Match Guarantee */}
      <div style={{
        backgroundColor: '#e3f2fd',
        border: '1px solid #2196f3',
        borderRadius: '8px',
        padding: '1rem',
        textAlign: 'center'
      }}>
        <div style={{ fontWeight: 'bold', color: '#1976d2', marginBottom: '0.5rem' }}>
          🛡️ Price Match Guarantee
        </div>
        <div style={{ fontSize: '0.9rem', color: '#666' }}>
          Found a lower price? We'll match it! Contact us with proof of a lower price from an authorized retailer.
        </div>
      </div>

      {/* Price History Chart */}
      {showPriceHistory && (
        <div style={{ marginTop: '1rem' }}>
          <PriceHistoryChart productId={productId} currentPrice={ourPrice} />
        </div>
      )}

      {/* Price Alert Modal */}
      <PriceAlertModal
        isOpen={showPriceAlert}
        onClose={() => setShowPriceAlert(false)}
        productName={productName}
        currentPrice={ourPrice}
        productId={productId}
      />
    </div>
  );
}
