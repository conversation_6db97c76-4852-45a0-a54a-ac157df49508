'use client';

import { useState, useEffect } from 'react';
import PriceAlertModal from './PriceAlertModal';
import PriceHistoryChart from './PriceHistoryChart';

interface CompetitorPrice {
  store: string;
  price: number;
  originalPrice?: number;
  url: string;
  logo: string;
  shipping: string;
  availability: 'in_stock' | 'limited' | 'out_of_stock';
  lastUpdated: string;
  trustScore: number;
}

interface PriceComparisonProps {
  productName: string;
  ourPrice: number;
  productId: string;
}

export default function PriceComparison({ productName, ourPrice, productId }: PriceComparisonProps) {
  const [competitors, setCompetitors] = useState<CompetitorPrice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [showAll, setShowAll] = useState(false);
  const [showPriceAlert, setShowPriceAlert] = useState(false);
  const [showPriceHistory, setShowPriceHistory] = useState(false);

  // Mock competitor data - in real app, this would come from API
  const mockCompetitorData: Record<string, CompetitorPrice[]> = {
    'iphone-15-pro-max': [
      {
        store: 'Amazon',
        price: 1189.99,
        originalPrice: 1199.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '2 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 1199.00,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '5 min ago',
        trustScore: 4.7
      },
      {
        store: 'Apple Store',
        price: 1199.99,
        url: 'https://apple.com',
        logo: '🍎',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 5.0
      },
      {
        store: 'B&H Photo',
        price: 1179.99,
        url: 'https://bhphotovideo.com',
        logo: '📷',
        shipping: 'Free shipping',
        availability: 'limited',
        lastUpdated: '8 min ago',
        trustScore: 4.6
      },
      {
        store: 'Walmart',
        price: 1199.99,
        url: 'https://walmart.com',
        logo: '🏬',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '12 min ago',
        trustScore: 4.3
      },
      {
        store: 'Target',
        price: 1209.99,
        url: 'https://target.com',
        logo: '🎯',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '15 min ago',
        trustScore: 4.4
      }
    ],
    'macbook-air-m3': [
      {
        store: 'Apple Store',
        price: 1299.99,
        url: 'https://apple.com',
        logo: '🍎',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 5.0
      },
      {
        store: 'Amazon',
        price: 1299.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '3 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 1299.99,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '7 min ago',
        trustScore: 4.7
      },
      {
        store: 'Costco',
        price: 1279.99,
        url: 'https://costco.com',
        logo: '🏢',
        shipping: 'Free shipping',
        availability: 'limited',
        lastUpdated: '20 min ago',
        trustScore: 4.5
      }
    ],
    'sony-wh-1000xm5': [
      {
        store: 'Amazon',
        price: 379.99,
        originalPrice: 399.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 399.99,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '4 min ago',
        trustScore: 4.7
      },
      {
        store: 'Sony Direct',
        price: 399.99,
        url: 'https://sony.com',
        logo: '📺',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '2 min ago',
        trustScore: 4.9
      },
      {
        store: 'Target',
        price: 389.99,
        url: 'https://target.com',
        logo: '🎯',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '6 min ago',
        trustScore: 4.4
      }
    ]
  };

  useEffect(() => {
    // Simulate API call to fetch competitor prices
    const fetchPrices = async () => {
      setIsLoading(true);
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const competitorData = mockCompetitorData[productId] || [];
      setCompetitors(competitorData);
      setLastUpdated(new Date());
      setIsLoading(false);
    };

    fetchPrices();

    // Set up real-time updates every 5 minutes
    const interval = setInterval(fetchPrices, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [productId]);

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'in_stock': return '#28a745';
      case 'limited': return '#ffc107';
      case 'out_of_stock': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case 'in_stock': return 'In Stock';
      case 'limited': return 'Limited Stock';
      case 'out_of_stock': return 'Out of Stock';
      default: return 'Unknown';
    }
  };

  const lowestPrice = Math.min(...competitors.map(c => c.price));
  const ourRank = competitors.filter(c => c.price < ourPrice).length + 1;
  const savings = competitors.length > 0 ? Math.max(0, Math.max(...competitors.map(c => c.price)) - ourPrice) : 0;

  const displayedCompetitors = showAll ? competitors : competitors.slice(0, 3);

  if (isLoading) {
    return (
      <div style={{ 
        backgroundColor: '#f8f9fa',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
          <div style={{ 
            width: '20px', 
            height: '20px', 
            border: '2px solid #007bff',
            borderTop: '2px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <h3 style={{ margin: 0 }}>🔍 Checking competitor prices...</h3>
        </div>
        <p style={{ color: '#666', margin: 0 }}>
          Scanning major retailers for the best deals on {productName}
        </p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{
      position: 'relative',
      marginBottom: '3rem'
    }}>
      {/* Main container with glassmorphism */}
      <div style={{
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%)',
        backdropFilter: 'blur(20px)',
        borderRadius: '32px',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
        padding: '3rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Animated gradient background */}
        <div style={{
          position: 'absolute',
          top: '-50%',
          left: '-50%',
          width: '200%',
          height: '200%',
          background: 'conic-gradient(from 0deg at 50% 50%, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe, #667eea)',
          animation: 'rotate 20s linear infinite',
          opacity: 0.1,
          pointerEvents: 'none'
        }} />

        {/* Floating particles */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 25%), radial-gradient(circle at 80% 80%, rgba(245, 87, 108, 0.1) 0%, transparent 25%), radial-gradient(circle at 40% 60%, rgba(79, 172, 254, 0.1) 0%, transparent 25%)',
          animation: 'float 15s ease-in-out infinite',
          pointerEvents: 'none'
        }} />

        {/* Header */}
        <div style={{ marginBottom: '3rem', position: 'relative', zIndex: 2 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '2rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>
              <div style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '24px',
                padding: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 10px 30px rgba(102, 126, 234, 0.3)',
                position: 'relative'
              }}>
                <div style={{
                  position: 'absolute',
                  inset: '-2px',
                  background: 'linear-gradient(135deg, #667eea, #764ba2, #f093fb)',
                  borderRadius: '26px',
                  opacity: 0.7,
                  animation: 'pulse 3s ease-in-out infinite',
                  zIndex: -1
                }} />
                <span style={{ fontSize: '2rem', filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }}>💰</span>
              </div>
              <div>
                <h3 style={{
                  margin: 0,
                  fontSize: '2.5rem',
                  fontWeight: '900',
                  background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  letterSpacing: '-0.02em',
                  lineHeight: '1.2'
                }}>
                  AI Price Intelligence
                </h3>
                <p style={{
                  margin: '0.75rem 0 0 0',
                  color: '#64748b',
                  fontSize: '1.1rem',
                  fontWeight: '500',
                  letterSpacing: '0.01em'
                }}>
                  Real-time monitoring across 10+ premium retailers with ML-powered optimization
                </p>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem',
              padding: '1rem 1.5rem',
              background: 'rgba(16, 185, 129, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '20px',
              border: '1px solid rgba(16, 185, 129, 0.2)',
              boxShadow: '0 4px 20px rgba(16, 185, 129, 0.1)'
            }}>
              <div style={{
                width: '12px',
                height: '12px',
                background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                borderRadius: '50%',
                animation: 'pulse 2s infinite',
                boxShadow: '0 0 10px rgba(16, 185, 129, 0.5)'
              }} />
              <span style={{
                fontSize: '0.9rem',
                color: '#059669',
                fontWeight: '600',
                textShadow: '0 1px 2px rgba(0,0,0,0.1)'
              }}>
                Live • Updated {lastUpdated.toLocaleTimeString()}
              </span>
            </div>
          </div>

          {/* Our Price Highlight - Premium Card */}
          <div style={{
            position: 'relative',
            marginBottom: '3rem'
          }}>
            {/* Glow effect */}
            <div style={{
              position: 'absolute',
              inset: '-4px',
              background: ourPrice <= lowestPrice
                ? 'linear-gradient(135deg, #10b981, #059669, #047857)'
                : 'linear-gradient(135deg, #f59e0b, #d97706, #b45309)',
              borderRadius: '28px',
              opacity: 0.3,
              filter: 'blur(8px)',
              animation: 'pulse 3s ease-in-out infinite'
            }} />

            <div style={{
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(20px)',
              borderRadius: '24px',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              padding: '2.5rem',
              position: 'relative',
              overflow: 'hidden',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1)'
            }}>
              {/* Animated background */}
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: ourPrice <= lowestPrice
                  ? 'radial-gradient(circle at 20% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(5, 150, 105, 0.1) 0%, transparent 50%)'
                  : 'radial-gradient(circle at 20% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(217, 119, 6, 0.1) 0%, transparent 50%)',
                animation: 'float 10s ease-in-out infinite',
                pointerEvents: 'none'
              }} />

              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '2.5rem',
                position: 'relative',
                zIndex: 1
              }}>
                <div style={{
                  background: ourPrice <= lowestPrice
                    ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                    : 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                  borderRadius: '24px',
                  padding: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
                  position: 'relative'
                }}>
                  <div style={{
                    position: 'absolute',
                    inset: '-2px',
                    background: ourPrice <= lowestPrice
                      ? 'linear-gradient(135deg, #10b981, #059669, #047857)'
                      : 'linear-gradient(135deg, #f59e0b, #d97706, #b45309)',
                    borderRadius: '26px',
                    opacity: 0.5,
                    animation: 'rotate 10s linear infinite',
                    zIndex: -1
                  }} />
                  <span style={{
                    fontSize: '3rem',
                    filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))'
                  }}>
                    {ourPrice <= lowestPrice ? '🏆' : '💎'}
                  </span>
                </div>

                <div style={{ flex: 1 }}>
                  <div style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    color: '#64748b',
                    marginBottom: '0.75rem',
                    letterSpacing: '0.05em',
                    textTransform: 'uppercase'
                  }}>
                    ElectroHub Premium Price
                  </div>
                  <div style={{
                    fontSize: '4rem',
                    fontWeight: '900',
                    background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    marginBottom: '1rem',
                    letterSpacing: '-0.02em',
                    lineHeight: '1'
                  }}>
                    ${ourPrice.toFixed(2)}
                  </div>

                  {ourPrice <= lowestPrice ? (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1.5rem',
                      flexWrap: 'wrap'
                    }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.75rem',
                        padding: '1rem 2rem',
                        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                        color: 'white',
                        borderRadius: '20px',
                        fontSize: '1.1rem',
                        fontWeight: '700',
                        boxShadow: '0 8px 25px rgba(16, 185, 129, 0.3)',
                        textTransform: 'uppercase',
                        letterSpacing: '0.05em'
                      }}>
                        <span style={{ fontSize: '1.3rem' }}>🏆</span>
                        BEST PRICE GUARANTEED
                      </div>
                      {savings > 0 && (
                        <div style={{
                          padding: '1rem 2rem',
                          background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
                          color: 'white',
                          borderRadius: '20px',
                          fontSize: '1.1rem',
                          fontWeight: '700',
                          boxShadow: '0 8px 25px rgba(251, 191, 36, 0.3)'
                        }}>
                          💰 You save ${savings.toFixed(2)}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '1.5rem',
                      flexWrap: 'wrap'
                    }}>
                      <div style={{
                        padding: '1rem 2rem',
                        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                        color: 'white',
                        borderRadius: '20px',
                        fontSize: '1.1rem',
                        fontWeight: '700',
                        boxShadow: '0 8px 25px rgba(245, 158, 11, 0.3)'
                      }}>
                        #{ourRank} of {competitors.length + 1} retailers
                      </div>
                      {lowestPrice < ourPrice && (
                        <div style={{
                          fontSize: '1.1rem',
                          color: '#64748b',
                          fontWeight: '600'
                        }}>
                          Market low: ${lowestPrice.toFixed(2)}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
      </div>

          {/* Competitor Prices */}
          <div style={{ marginBottom: '3rem', position: 'relative', zIndex: 2 }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '2rem',
              marginBottom: '2.5rem'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                borderRadius: '20px',
                padding: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 10px 30px rgba(99, 102, 241, 0.3)',
                position: 'relative'
              }}>
                <div style={{
                  position: 'absolute',
                  inset: '-2px',
                  background: 'linear-gradient(135deg, #6366f1, #8b5cf6, #a855f7)',
                  borderRadius: '22px',
                  opacity: 0.7,
                  animation: 'pulse 3s ease-in-out infinite',
                  zIndex: -1
                }} />
                <span style={{ fontSize: '1.8rem', filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }}>🏪</span>
              </div>
              <h4 style={{
                margin: 0,
                fontSize: '2rem',
                fontWeight: '900',
                background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                letterSpacing: '-0.02em'
              }}>
                Premium Retailer Comparison
              </h4>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              {displayedCompetitors.map((competitor, index) => (
                <div key={index} style={{
                  position: 'relative'
                }}>
                  {/* Glow effect for best price */}
                  {competitor.price === lowestPrice && (
                    <div style={{
                      position: 'absolute',
                      inset: '-4px',
                      background: 'linear-gradient(135deg, #10b981, #059669, #047857)',
                      borderRadius: '28px',
                      opacity: 0.3,
                      filter: 'blur(12px)',
                      animation: 'pulse 3s ease-in-out infinite'
                    }} />
                  )}

                  <div style={{
                    background: 'rgba(255, 255, 255, 0.9)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: '24px',
                    border: competitor.price === lowestPrice
                      ? '2px solid rgba(16, 185, 129, 0.3)'
                      : '1px solid rgba(255, 255, 255, 0.2)',
                    padding: '2rem',
                    display: 'grid',
                    gridTemplateColumns: '80px 1fr auto auto auto',
                    alignItems: 'center',
                    gap: '2rem',
                    boxShadow: competitor.price === lowestPrice
                      ? '0 20px 60px rgba(16, 185, 129, 0.2)'
                      : '0 10px 40px rgba(0, 0, 0, 0.1)',
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-4px)';
                    e.currentTarget.style.boxShadow = competitor.price === lowestPrice
                      ? '0 25px 70px rgba(16, 185, 129, 0.3)'
                      : '0 15px 50px rgba(0, 0, 0, 0.15)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = competitor.price === lowestPrice
                      ? '0 20px 60px rgba(16, 185, 129, 0.2)'
                      : '0 10px 40px rgba(0, 0, 0, 0.1)';
                  }}
                  >
                    {/* Background pattern */}
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: competitor.price === lowestPrice
                        ? 'radial-gradient(circle at 20% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)'
                        : 'radial-gradient(circle at 80% 80%, rgba(99, 102, 241, 0.05) 0%, transparent 50%)',
                      pointerEvents: 'none'
                    }} />
                    {/* Store Logo & Name */}
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '80px',
                      height: '80px',
                      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                      borderRadius: '24px',
                      fontSize: '2.5rem',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                      position: 'relative',
                      zIndex: 1
                    }}>
                      {competitor.logo}
                    </div>

                    <div style={{ position: 'relative', zIndex: 1 }}>
                      <div style={{
                        fontWeight: '800',
                        fontSize: '1.4rem',
                        color: '#1e293b',
                        marginBottom: '0.75rem',
                        letterSpacing: '-0.01em'
                      }}>
                        {competitor.store}
                      </div>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1.5rem',
                        fontSize: '0.95rem',
                        color: '#64748b',
                        fontWeight: '500'
                      }}>
                        <span>{competitor.shipping}</span>
                        <span style={{ opacity: 0.5 }}>•</span>
                        <span>{competitor.lastUpdated}</span>
                        <span style={{ opacity: 0.5 }}>•</span>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          padding: '0.25rem 0.75rem',
                          background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
                          color: 'white',
                          borderRadius: '12px',
                          fontSize: '0.85rem',
                          fontWeight: '600'
                        }}>
                          <span>⭐</span>
                          <span>{competitor.trustScore}</span>
                        </div>
                      </div>
                    </div>

                    {/* Price */}
                    <div style={{ textAlign: 'right', position: 'relative', zIndex: 1 }}>
                      <div style={{
                        fontSize: '2.2rem',
                        fontWeight: '900',
                        background: competitor.price < ourPrice
                          ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
                          : competitor.price > ourPrice
                          ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                          : 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundClip: 'text',
                        marginBottom: '0.5rem',
                        letterSpacing: '-0.02em'
                      }}>
                        ${competitor.price.toFixed(2)}
                      </div>
                      {competitor.originalPrice && competitor.originalPrice > competitor.price && (
                        <div style={{
                          fontSize: '1.1rem',
                          color: '#94a3b8',
                          textDecoration: 'line-through',
                          fontWeight: '600'
                        }}>
                          ${competitor.originalPrice.toFixed(2)}
                        </div>
                      )}
                    </div>

                    {/* Availability */}
                    <div style={{ textAlign: 'center', position: 'relative', zIndex: 1 }}>
                      <div style={{
                        padding: '0.75rem 1.5rem',
                        background: competitor.availability === 'in_stock'
                          ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                          : competitor.availability === 'limited'
                          ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
                          : 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                        color: 'white',
                        borderRadius: '16px',
                        fontSize: '0.9rem',
                        fontWeight: '700',
                        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
                        textTransform: 'uppercase',
                        letterSpacing: '0.05em'
                      }}>
                        {getAvailabilityText(competitor.availability)}
                      </div>
                    </div>

                    {/* Visit Store Button */}
                    <div style={{ position: 'relative', zIndex: 1 }}>
                      <a
                        href={competitor.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: '0.75rem',
                          padding: '1rem 2rem',
                          background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                          color: 'white',
                          textDecoration: 'none',
                          borderRadius: '20px',
                          fontSize: '1rem',
                          fontWeight: '700',
                          transition: 'all 0.3s ease',
                          boxShadow: '0 8px 25px rgba(99, 102, 241, 0.3)',
                          textTransform: 'uppercase',
                          letterSpacing: '0.05em',
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-3px) scale(1.05)';
                          e.currentTarget.style.boxShadow = '0 12px 35px rgba(99, 102, 241, 0.4)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0) scale(1)';
                          e.currentTarget.style.boxShadow = '0 8px 25px rgba(99, 102, 241, 0.3)';
                        }}
                      >
                        <span>Visit Store</span>
                        <span style={{ fontSize: '1.2rem', transition: 'transform 0.3s ease' }}>→</span>
                      </a>
                    </div>
            </div>
          ))}
        </div>
        
        {competitors.length > 3 && (
          <button
            onClick={() => setShowAll(!showAll)}
            style={{
              marginTop: '1rem',
              padding: '0.75rem 1.5rem',
              backgroundColor: 'transparent',
              color: '#007bff',
              border: '1px solid #007bff',
              borderRadius: '6px',
              cursor: 'pointer',
              fontWeight: 'bold',
              width: '100%'
            }}
          >
            {showAll ? '▲ Show Less' : `▼ Show All ${competitors.length} Retailers`}
          </button>
        )}
      </div>

          {/* Action Buttons */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            marginBottom: '2rem',
            position: 'relative',
            zIndex: 1
          }}>
            <button
              onClick={() => setShowPriceAlert(true)}
              style={{
                padding: '1rem 1.5rem',
                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '16px',
                fontWeight: '600',
                fontSize: '1rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.75rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 20px rgba(245, 158, 11, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 30px rgba(245, 158, 11, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(245, 158, 11, 0.3)';
              }}
            >
              <span style={{ fontSize: '1.2rem' }}>🔔</span>
              Set Price Alert
            </button>

            <button
              onClick={() => setShowPriceHistory(!showPriceHistory)}
              style={{
                padding: '1rem 1.5rem',
                background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '16px',
                fontWeight: '600',
                fontSize: '1rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.75rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 20px rgba(6, 182, 212, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 30px rgba(6, 182, 212, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(6, 182, 212, 0.3)';
              }}
            >
              <span style={{ fontSize: '1.2rem' }}>📈</span>
              {showPriceHistory ? 'Hide' : 'Show'} Price History
            </button>
          </div>

          {/* Price Match Guarantee */}
          <div style={{
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            borderRadius: '20px',
            padding: '2px',
            marginBottom: '1rem',
            position: 'relative',
            zIndex: 1
          }}>
            <div style={{
              backgroundColor: '#ffffff',
              borderRadius: '18px',
              padding: '2rem',
              textAlign: 'center',
              position: 'relative',
              overflow: 'hidden'
            }}>
              {/* Background pattern */}
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
                pointerEvents: 'none'
              }} />

              <div style={{ position: 'relative', zIndex: 1 }}>
                <div style={{
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  borderRadius: '16px',
                  padding: '12px',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '1rem'
                }}>
                  <span style={{ fontSize: '2rem' }}>🛡️</span>
                </div>

                <h4 style={{
                  margin: '0 0 1rem 0',
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}>
                  100% Price Match Guarantee
                </h4>

                <p style={{
                  fontSize: '1rem',
                  color: '#64748b',
                  lineHeight: '1.6',
                  margin: 0,
                  maxWidth: '500px',
                  marginLeft: 'auto',
                  marginRight: 'auto'
                }}>
                  Found a lower price from an authorized retailer? We'll match it instantly and give you an additional 10% of the difference as store credit.
                </p>
              </div>
            </div>
          </div>

      {/* Price History Chart */}
      {showPriceHistory && (
        <div style={{ marginTop: '1rem' }}>
          <PriceHistoryChart productId={productId} currentPrice={ourPrice} />
        </div>
      )}

      {/* Price Alert Modal */}
        {/* Price Alert Modal */}
        <PriceAlertModal
          isOpen={showPriceAlert}
          onClose={() => setShowPriceAlert(false)}
          productName={productName}
          currentPrice={ourPrice}
          productId={productId}
        />

        {/* Advanced CSS Animations */}
        <style jsx>{`
          @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          @keyframes pulse {
            0%, 100% {
              opacity: 1;
              transform: scale(1);
            }
            50% {
              opacity: 0.8;
              transform: scale(1.05);
            }
          }

          @keyframes float {
            0%, 100% {
              transform: translateY(0px) rotate(0deg);
              opacity: 0.7;
            }
            25% {
              transform: translateY(-10px) rotate(90deg);
              opacity: 0.9;
            }
            50% {
              transform: translateY(-20px) rotate(180deg);
              opacity: 1;
            }
            75% {
              transform: translateY(-10px) rotate(270deg);
              opacity: 0.9;
            }
          }

          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(30px) scale(0.95);
            }
            to {
              opacity: 1;
              transform: translateY(0) scale(1);
            }
          }

          @keyframes shimmer {
            0% {
              transform: translateX(-100%) skewX(-15deg);
              opacity: 0;
            }
            50% {
              opacity: 1;
            }
            100% {
              transform: translateX(200%) skewX(-15deg);
              opacity: 0;
            }
          }

          @keyframes glow {
            0%, 100% {
              box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            }
            50% {
              box-shadow: 0 0 40px rgba(102, 126, 234, 0.6);
            }
          }

          @keyframes slideInFromLeft {
            from {
              opacity: 0;
              transform: translateX(-50px);
            }
            to {
              opacity: 1;
              transform: translateX(0);
            }
          }

          @keyframes bounceIn {
            0% {
              opacity: 0;
              transform: scale(0.3) rotate(-10deg);
            }
            50% {
              opacity: 1;
              transform: scale(1.05) rotate(5deg);
            }
            70% {
              transform: scale(0.95) rotate(-2deg);
            }
            100% {
              opacity: 1;
              transform: scale(1) rotate(0deg);
            }
          }
        `}</style>
      </div>
    </div>
  );
}
