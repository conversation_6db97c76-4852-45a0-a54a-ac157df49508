'use client';

import { useState, useEffect } from 'react';
import PriceAlertModal from './PriceAlertModal';
import PriceHistoryChart from './PriceHistoryChart';

interface CompetitorPrice {
  store: string;
  price: number;
  originalPrice?: number;
  url: string;
  logo: string;
  shipping: string;
  availability: 'in_stock' | 'limited' | 'out_of_stock';
  lastUpdated: string;
  trustScore: number;
}

interface PriceComparisonProps {
  productName: string;
  ourPrice: number;
  productId: string;
}

export default function PriceComparison({ productName, ourPrice, productId }: PriceComparisonProps) {
  const [competitors, setCompetitors] = useState<CompetitorPrice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [showAll, setShowAll] = useState(false);
  const [showPriceAlert, setShowPriceAlert] = useState(false);
  const [showPriceHistory, setShowPriceHistory] = useState(false);

  // Mock competitor data - in real app, this would come from API
  const mockCompetitorData: Record<string, CompetitorPrice[]> = {
    'iphone-15-pro-max': [
      {
        store: 'Amazon',
        price: 1189.99,
        originalPrice: 1199.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '2 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 1199.00,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '5 min ago',
        trustScore: 4.7
      },
      {
        store: 'Apple Store',
        price: 1199.99,
        url: 'https://apple.com',
        logo: '🍎',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 5.0
      },
      {
        store: 'B&H Photo',
        price: 1179.99,
        url: 'https://bhphotovideo.com',
        logo: '📷',
        shipping: 'Free shipping',
        availability: 'limited',
        lastUpdated: '8 min ago',
        trustScore: 4.6
      },
      {
        store: 'Walmart',
        price: 1199.99,
        url: 'https://walmart.com',
        logo: '🏬',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '12 min ago',
        trustScore: 4.3
      },
      {
        store: 'Target',
        price: 1209.99,
        url: 'https://target.com',
        logo: '🎯',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '15 min ago',
        trustScore: 4.4
      }
    ],
    'macbook-air-m3': [
      {
        store: 'Apple Store',
        price: 1299.99,
        url: 'https://apple.com',
        logo: '🍎',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 5.0
      },
      {
        store: 'Amazon',
        price: 1299.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '3 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 1299.99,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '7 min ago',
        trustScore: 4.7
      },
      {
        store: 'Costco',
        price: 1279.99,
        url: 'https://costco.com',
        logo: '🏢',
        shipping: 'Free shipping',
        availability: 'limited',
        lastUpdated: '20 min ago',
        trustScore: 4.5
      }
    ],
    'sony-wh-1000xm5': [
      {
        store: 'Amazon',
        price: 379.99,
        originalPrice: 399.99,
        url: 'https://amazon.com',
        logo: '🛒',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '1 min ago',
        trustScore: 4.8
      },
      {
        store: 'Best Buy',
        price: 399.99,
        url: 'https://bestbuy.com',
        logo: '🏪',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '4 min ago',
        trustScore: 4.7
      },
      {
        store: 'Sony Direct',
        price: 399.99,
        url: 'https://sony.com',
        logo: '📺',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '2 min ago',
        trustScore: 4.9
      },
      {
        store: 'Target',
        price: 389.99,
        url: 'https://target.com',
        logo: '🎯',
        shipping: 'Free shipping',
        availability: 'in_stock',
        lastUpdated: '6 min ago',
        trustScore: 4.4
      }
    ]
  };

  useEffect(() => {
    // Simulate API call to fetch competitor prices
    const fetchPrices = async () => {
      setIsLoading(true);
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const competitorData = mockCompetitorData[productId] || [];
      setCompetitors(competitorData);
      setLastUpdated(new Date());
      setIsLoading(false);
    };

    fetchPrices();

    // Set up real-time updates every 5 minutes
    const interval = setInterval(fetchPrices, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [productId]);

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'in_stock': return '#28a745';
      case 'limited': return '#ffc107';
      case 'out_of_stock': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getAvailabilityText = (availability: string) => {
    switch (availability) {
      case 'in_stock': return 'In Stock';
      case 'limited': return 'Limited Stock';
      case 'out_of_stock': return 'Out of Stock';
      default: return 'Unknown';
    }
  };

  const lowestPrice = Math.min(...competitors.map(c => c.price));
  const ourRank = competitors.filter(c => c.price < ourPrice).length + 1;
  const savings = competitors.length > 0 ? Math.max(0, Math.max(...competitors.map(c => c.price)) - ourPrice) : 0;

  const displayedCompetitors = showAll ? competitors : competitors.slice(0, 3);

  if (isLoading) {
    return (
      <div style={{ 
        backgroundColor: '#f8f9fa',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
          <div style={{ 
            width: '20px', 
            height: '20px', 
            border: '2px solid #007bff',
            borderTop: '2px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <h3 style={{ margin: 0 }}>🔍 Checking competitor prices...</h3>
        </div>
        <p style={{ color: '#666', margin: 0 }}>
          Scanning major retailers for the best deals on {productName}
        </p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      borderRadius: '20px',
      padding: '2px',
      marginBottom: '2rem',
      boxShadow: '0 10px 40px rgba(102, 126, 234, 0.3)'
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '18px',
        padding: '2rem',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Animated background pattern */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.05) 0%, transparent 50%)',
          pointerEvents: 'none'
        }} />

        {/* Header */}
        <div style={{ marginBottom: '2rem', position: 'relative', zIndex: 1 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '16px',
                padding: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ fontSize: '1.5rem' }}>💰</span>
              </div>
              <div>
                <h3 style={{
                  margin: 0,
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}>
                  Real-Time Price Intelligence
                </h3>
                <p style={{
                  margin: '0.25rem 0 0 0',
                  color: '#64748b',
                  fontSize: '0.9rem'
                }}>
                  Live monitoring across 6+ premium retailers
                </p>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '0.75rem 1rem',
              backgroundColor: '#f1f5f9',
              borderRadius: '12px',
              border: '1px solid #e2e8f0'
            }}>
              <div style={{
                width: '8px',
                height: '8px',
                backgroundColor: '#10b981',
                borderRadius: '50%',
                animation: 'pulse 2s infinite'
              }} />
              <span style={{ fontSize: '0.8rem', color: '#475569', fontWeight: '500' }}>
                Updated {lastUpdated.toLocaleTimeString()}
              </span>
            </div>
          </div>

          {/* Our Price Highlight */}
          <div style={{
            background: ourPrice <= lowestPrice
              ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
              : 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
            borderRadius: '16px',
            padding: '2px',
            marginBottom: '2rem'
          }}>
            <div style={{
              backgroundColor: '#ffffff',
              borderRadius: '14px',
              padding: '1.5rem',
              display: 'flex',
              alignItems: 'center',
              gap: '1.5rem'
            }}>
              <div style={{
                background: ourPrice <= lowestPrice
                  ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                  : 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                borderRadius: '16px',
                padding: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ fontSize: '2rem' }}>
                  {ourPrice <= lowestPrice ? '🏆' : '💎'}
                </span>
              </div>

              <div style={{ flex: 1 }}>
                <div style={{
                  fontSize: '0.9rem',
                  fontWeight: '600',
                  color: '#64748b',
                  marginBottom: '0.5rem'
                }}>
                  ElectroHub Price
                </div>
                <div style={{
                  fontSize: '2.5rem',
                  fontWeight: '800',
                  background: 'linear-gradient(135deg, #1e293b 0%, #475569 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  marginBottom: '0.5rem'
                }}>
                  ${ourPrice.toFixed(2)}
                </div>

                {ourPrice <= lowestPrice ? (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '1rem'
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                      color: 'white',
                      borderRadius: '12px',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}>
                      🏆 BEST PRICE GUARANTEED
                    </div>
                    {savings > 0 && (
                      <div style={{
                        padding: '0.5rem 1rem',
                        backgroundColor: '#fef3c7',
                        color: '#92400e',
                        borderRadius: '12px',
                        fontSize: '0.9rem',
                        fontWeight: '600'
                      }}>
                        You save ${savings.toFixed(2)}
                      </div>
                    )}
                  </div>
                ) : (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '1rem'
                  }}>
                    <div style={{
                      padding: '0.5rem 1rem',
                      backgroundColor: '#fef3c7',
                      color: '#92400e',
                      borderRadius: '12px',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}>
                      #{ourRank} of {competitors.length + 1} retailers
                    </div>
                    {lowestPrice < ourPrice && (
                      <div style={{
                        fontSize: '0.9rem',
                        color: '#64748b'
                      }}>
                        Lowest: ${lowestPrice.toFixed(2)}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
      </div>

          {/* Competitor Prices */}
          <div style={{ marginBottom: '2rem', position: 'relative', zIndex: 1 }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              marginBottom: '1.5rem'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                borderRadius: '12px',
                padding: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{ fontSize: '1.2rem' }}>🏪</span>
              </div>
              <h4 style={{
                margin: 0,
                fontSize: '1.3rem',
                fontWeight: '700',
                color: '#1e293b'
              }}>
                Compare with {competitors.length} premium retailers
              </h4>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {displayedCompetitors.map((competitor, index) => (
                <div key={index} style={{
                  background: competitor.price === lowestPrice
                    ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                    : '#ffffff',
                  borderRadius: '16px',
                  padding: competitor.price === lowestPrice ? '2px' : '0',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                  border: competitor.price === lowestPrice ? 'none' : '1px solid #e2e8f0'
                }}>
                  <div style={{
                    backgroundColor: '#ffffff',
                    borderRadius: competitor.price === lowestPrice ? '14px' : '16px',
                    padding: '1.5rem',
                    display: 'grid',
                    gridTemplateColumns: '60px 1fr auto auto auto',
                    alignItems: 'center',
                    gap: '1.5rem'
                  }}>
                    {/* Store Logo & Name */}
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '60px',
                      height: '60px',
                      background: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)',
                      borderRadius: '16px',
                      fontSize: '1.8rem'
                    }}>
                      {competitor.logo}
                    </div>

                    <div>
                      <div style={{
                        fontWeight: '700',
                        fontSize: '1.1rem',
                        color: '#1e293b',
                        marginBottom: '0.5rem'
                      }}>
                        {competitor.store}
                      </div>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem',
                        fontSize: '0.85rem',
                        color: '#64748b'
                      }}>
                        <span>{competitor.shipping}</span>
                        <span>•</span>
                        <span>{competitor.lastUpdated}</span>
                        <span>•</span>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                          <span>⭐</span>
                          <span style={{ fontWeight: '600' }}>{competitor.trustScore}</span>
                        </div>
                      </div>
                    </div>

                    {/* Price */}
                    <div style={{ textAlign: 'right' }}>
                      <div style={{
                        fontSize: '1.5rem',
                        fontWeight: '800',
                        color: competitor.price < ourPrice ? '#ef4444' :
                               competitor.price > ourPrice ? '#10b981' : '#6366f1',
                        marginBottom: '0.25rem'
                      }}>
                        ${competitor.price.toFixed(2)}
                      </div>
                      {competitor.originalPrice && competitor.originalPrice > competitor.price && (
                        <div style={{
                          fontSize: '0.9rem',
                          color: '#94a3b8',
                          textDecoration: 'line-through'
                        }}>
                          ${competitor.originalPrice.toFixed(2)}
                        </div>
                      )}
                    </div>

                    {/* Availability */}
                    <div style={{ textAlign: 'center' }}>
                      <div style={{
                        padding: '0.5rem 1rem',
                        background: competitor.availability === 'in_stock'
                          ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
                          : competitor.availability === 'limited'
                          ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'
                          : 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                        color: 'white',
                        borderRadius: '12px',
                        fontSize: '0.8rem',
                        fontWeight: '600'
                      }}>
                        {getAvailabilityText(competitor.availability)}
                      </div>
                    </div>

                    {/* Visit Store Button */}
                    <div>
                      <a
                        href={competitor.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          padding: '0.75rem 1.25rem',
                          background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                          color: 'white',
                          textDecoration: 'none',
                          borderRadius: '12px',
                          fontSize: '0.9rem',
                          fontWeight: '600',
                          transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                          boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 8px 20px rgba(99, 102, 241, 0.4)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(99, 102, 241, 0.3)';
                        }}
                      >
                        Visit Store
                        <span style={{ fontSize: '0.8rem' }}>→</span>
                      </a>
                    </div>
            </div>
          ))}
        </div>
        
        {competitors.length > 3 && (
          <button
            onClick={() => setShowAll(!showAll)}
            style={{
              marginTop: '1rem',
              padding: '0.75rem 1.5rem',
              backgroundColor: 'transparent',
              color: '#007bff',
              border: '1px solid #007bff',
              borderRadius: '6px',
              cursor: 'pointer',
              fontWeight: 'bold',
              width: '100%'
            }}
          >
            {showAll ? '▲ Show Less' : `▼ Show All ${competitors.length} Retailers`}
          </button>
        )}
      </div>

          {/* Action Buttons */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '1rem',
            marginBottom: '2rem',
            position: 'relative',
            zIndex: 1
          }}>
            <button
              onClick={() => setShowPriceAlert(true)}
              style={{
                padding: '1rem 1.5rem',
                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '16px',
                fontWeight: '600',
                fontSize: '1rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.75rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 20px rgba(245, 158, 11, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 30px rgba(245, 158, 11, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(245, 158, 11, 0.3)';
              }}
            >
              <span style={{ fontSize: '1.2rem' }}>🔔</span>
              Set Price Alert
            </button>

            <button
              onClick={() => setShowPriceHistory(!showPriceHistory)}
              style={{
                padding: '1rem 1.5rem',
                background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '16px',
                fontWeight: '600',
                fontSize: '1rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.75rem',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 20px rgba(6, 182, 212, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 30px rgba(6, 182, 212, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 20px rgba(6, 182, 212, 0.3)';
              }}
            >
              <span style={{ fontSize: '1.2rem' }}>📈</span>
              {showPriceHistory ? 'Hide' : 'Show'} Price History
            </button>
          </div>

          {/* Price Match Guarantee */}
          <div style={{
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            borderRadius: '20px',
            padding: '2px',
            marginBottom: '1rem',
            position: 'relative',
            zIndex: 1
          }}>
            <div style={{
              backgroundColor: '#ffffff',
              borderRadius: '18px',
              padding: '2rem',
              textAlign: 'center',
              position: 'relative',
              overflow: 'hidden'
            }}>
              {/* Background pattern */}
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
                pointerEvents: 'none'
              }} />

              <div style={{ position: 'relative', zIndex: 1 }}>
                <div style={{
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  borderRadius: '16px',
                  padding: '12px',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '1rem'
                }}>
                  <span style={{ fontSize: '2rem' }}>🛡️</span>
                </div>

                <h4 style={{
                  margin: '0 0 1rem 0',
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}>
                  100% Price Match Guarantee
                </h4>

                <p style={{
                  fontSize: '1rem',
                  color: '#64748b',
                  lineHeight: '1.6',
                  margin: 0,
                  maxWidth: '500px',
                  marginLeft: 'auto',
                  marginRight: 'auto'
                }}>
                  Found a lower price from an authorized retailer? We'll match it instantly and give you an additional 10% of the difference as store credit.
                </p>
              </div>
            </div>
          </div>

      {/* Price History Chart */}
      {showPriceHistory && (
        <div style={{ marginTop: '1rem' }}>
          <PriceHistoryChart productId={productId} currentPrice={ourPrice} />
        </div>
      )}

      {/* Price Alert Modal */}
        {/* Price Alert Modal */}
        <PriceAlertModal
          isOpen={showPriceAlert}
          onClose={() => setShowPriceAlert(false)}
          productName={productName}
          currentPrice={ourPrice}
          productId={productId}
        />

        {/* CSS Animations */}
        <style jsx>{`
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }

          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }
        `}</style>
      </div>
    </div>
  );
}
