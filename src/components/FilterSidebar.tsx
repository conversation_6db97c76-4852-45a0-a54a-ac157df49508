'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';

export default function FilterSidebar({ brands }: { brands: string[] }) {
  const router = useRouter();
  const sp = useSearchParams();
  const [brand, setBrand] = useState(sp.get('brand') || '');
  const [min, setMin] = useState(sp.get('min') || '');
  const [max, setMax] = useState(sp.get('max') || '');

  function apply() {
    const q = new URLSearchParams(sp.toString());
    if (brand) q.set('brand', brand); else q.delete('brand');
    if (min) q.set('min', min); else q.delete('min');
    if (max) q.set('max', max); else q.delete('max');
    router.push(`?${q.toString()}`);
  }

  return (
    <aside aria-label="Filters" style={{ borderRight: '1px solid #eee', paddingRight: '1rem' }}>
      <label>Brand
        <select value={brand} onChange={(e) => setBrand(e.target.value)} aria-label="Filter by brand">
          <option value="">All</option>
          {brands.map((b) => <option key={b} value={b}>{b}</option>)}
        </select>
      </label>
      <div>
        <label>Min $<input value={min} onChange={(e) => setMin(e.target.value)} inputMode="numeric" /></label>
        <label>Max $<input value={max} onChange={(e) => setMax(e.target.value)} inputMode="numeric" /></label>
      </div>
      <button onClick={apply}>Apply</button>
    </aside>
  );
}
