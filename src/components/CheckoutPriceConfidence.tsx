'use client';

import { useState, useEffect } from 'react';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  slug: string;
}

interface CheckoutPriceConfidenceProps {
  cartItems: CartItem[];
}

interface PriceSummary {
  totalSavings: number;
  bestPriceItems: number;
  competitiveItems: number;
  confidenceScore: number;
  lastChecked: Date;
}

export default function CheckoutPriceConfidence({ cartItems }: CheckoutPriceConfidenceProps) {
  const [priceSummary, setPriceSummary] = useState<PriceSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const analyzePrices = async () => {
      setIsLoading(true);
      
      // Simulate price analysis
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock analysis results
      const totalSavings = cartItems.reduce((sum, item) => {
        // Simulate savings calculation
        const avgSavings = Math.random() * 50 + 10;
        return sum + (avgSavings * item.quantity);
      }, 0);

      const bestPriceItems = Math.floor(cartItems.length * 0.7); // 70% best price
      const competitiveItems = cartItems.length - bestPriceItems;
      const confidenceScore = Math.min(95, 75 + (bestPriceItems / cartItems.length) * 20);

      setPriceSummary({
        totalSavings,
        bestPriceItems,
        competitiveItems,
        confidenceScore,
        lastChecked: new Date()
      });

      setIsLoading(false);
    };

    if (cartItems.length > 0) {
      analyzePrices();
    }
  }, [cartItems]);

  if (cartItems.length === 0) return null;

  if (isLoading) {
    return (
      <div style={{ 
        backgroundColor: '#f8f9fa',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '1.5rem',
        margin: '1rem 0'
      }}>
        <div style={{ 
          display: 'flex',
          alignItems: 'center',
          gap: '1rem',
          marginBottom: '1rem'
        }}>
          <div style={{ 
            width: '30px', 
            height: '30px', 
            border: '3px solid #007bff',
            borderTop: '3px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
              🔍 Verifying Best Prices...
            </div>
            <div style={{ fontSize: '0.9rem', color: '#666' }}>
              Checking {cartItems.length} items against competitor prices
            </div>
          </div>
        </div>
        
        <div style={{ 
          display: 'flex',
          flexDirection: 'column',
          gap: '0.5rem',
          fontSize: '0.9rem',
          color: '#666'
        }}>
          <div>✅ Scanning major retailers...</div>
          <div>🔍 Comparing current market prices...</div>
          <div>💰 Calculating your savings...</div>
        </div>

        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (!priceSummary) return null;

  const getConfidenceColor = (score: number) => {
    if (score >= 90) return '#28a745';
    if (score >= 75) return '#ffc107';
    return '#dc3545';
  };

  const getConfidenceText = (score: number) => {
    if (score >= 90) return 'Excellent Deal';
    if (score >= 75) return 'Good Value';
    return 'Fair Price';
  };

  return (
    <div style={{ 
      backgroundColor: '#ffffff',
      border: '2px solid #28a745',
      borderRadius: '12px',
      padding: '1.5rem',
      margin: '1rem 0'
    }}>
      {/* Header */}
      <div style={{ 
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '1rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
          <div style={{ fontSize: '2rem' }}>🛡️</div>
          <div>
            <div style={{ 
              fontWeight: 'bold',
              fontSize: '1.2rem',
              color: '#28a745'
            }}>
              Price Confidence Verified
            </div>
            <div style={{ 
              fontSize: '0.9rem',
              color: '#666'
            }}>
              Last checked: {priceSummary.lastChecked.toLocaleTimeString()}
            </div>
          </div>
        </div>

        <button
          onClick={() => setShowDetails(!showDetails)}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#f8f9fa',
            border: '1px solid #e1e5e9',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '0.9rem'
          }}
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
      </div>

      {/* Summary Stats */}
      <div style={{ 
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
        gap: '1rem',
        marginBottom: '1rem'
      }}>
        <div style={{ 
          textAlign: 'center',
          padding: '1rem',
          backgroundColor: '#d4edda',
          borderRadius: '8px'
        }}>
          <div style={{ 
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#155724',
            marginBottom: '0.25rem'
          }}>
            ${priceSummary.totalSavings.toFixed(2)}
          </div>
          <div style={{ fontSize: '0.8rem', color: '#155724' }}>
            Total Savings
          </div>
        </div>

        <div style={{ 
          textAlign: 'center',
          padding: '1rem',
          backgroundColor: '#d4edda',
          borderRadius: '8px'
        }}>
          <div style={{ 
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#155724',
            marginBottom: '0.25rem'
          }}>
            {priceSummary.bestPriceItems}
          </div>
          <div style={{ fontSize: '0.8rem', color: '#155724' }}>
            Best Price Items
          </div>
        </div>

        <div style={{ 
          textAlign: 'center',
          padding: '1rem',
          backgroundColor: '#fff3cd',
          borderRadius: '8px'
        }}>
          <div style={{ 
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#856404',
            marginBottom: '0.25rem'
          }}>
            {priceSummary.competitiveItems}
          </div>
          <div style={{ fontSize: '0.8rem', color: '#856404' }}>
            Competitive Items
          </div>
        </div>

        <div style={{ 
          textAlign: 'center',
          padding: '1rem',
          backgroundColor: getConfidenceColor(priceSummary.confidenceScore) + '20',
          borderRadius: '8px'
        }}>
          <div style={{ 
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: getConfidenceColor(priceSummary.confidenceScore),
            marginBottom: '0.25rem'
          }}>
            {priceSummary.confidenceScore.toFixed(0)}%
          </div>
          <div style={{ 
            fontSize: '0.8rem', 
            color: getConfidenceColor(priceSummary.confidenceScore)
          }}>
            {getConfidenceText(priceSummary.confidenceScore)}
          </div>
        </div>
      </div>

      {/* Detailed Breakdown */}
      {showDetails && (
        <div style={{ 
          borderTop: '1px solid #e1e5e9',
          paddingTop: '1rem',
          animation: 'slideDown 0.3s ease'
        }}>
          <h4 style={{ marginBottom: '1rem', color: '#333' }}>
            📊 Item-by-Item Analysis
          </h4>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            {cartItems.map((item, index) => {
              const isBestPrice = index < priceSummary.bestPriceItems;
              const itemSavings = Math.random() * 30 + 5; // Mock savings per item
              
              return (
                <div key={item.id} style={{ 
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '0.75rem',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '6px'
                }}>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                      {item.name}
                    </div>
                    <div style={{ 
                      fontSize: '0.8rem',
                      color: '#666'
                    }}>
                      Qty: {item.quantity} × ${item.price.toFixed(2)}
                    </div>
                  </div>
                  
                  <div style={{ 
                    display: 'flex',
                    alignItems: 'center',
                    gap: '1rem'
                  }}>
                    <div style={{ 
                      padding: '0.25rem 0.75rem',
                      backgroundColor: isBestPrice ? '#d4edda' : '#fff3cd',
                      color: isBestPrice ? '#155724' : '#856404',
                      borderRadius: '12px',
                      fontSize: '0.8rem',
                      fontWeight: 'bold'
                    }}>
                      {isBestPrice ? '🏆 Best Price' : '💰 Competitive'}
                    </div>
                    
                    <div style={{ 
                      fontSize: '0.9rem',
                      fontWeight: 'bold',
                      color: '#28a745'
                    }}>
                      Save ${itemSavings.toFixed(2)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Guarantee Message */}
      <div style={{ 
        marginTop: '1rem',
        padding: '1rem',
        backgroundColor: '#e3f2fd',
        borderRadius: '8px',
        textAlign: 'center'
      }}>
        <div style={{ 
          fontWeight: 'bold',
          color: '#1976d2',
          marginBottom: '0.5rem'
        }}>
          🛡️ 100% Price Match Guarantee
        </div>
        <div style={{ 
          fontSize: '0.9rem',
          color: '#666'
        }}>
          If you find a lower price within 30 days, we'll refund the difference plus 10% of the difference as store credit.
        </div>
      </div>

      <style jsx>{`
        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
}
