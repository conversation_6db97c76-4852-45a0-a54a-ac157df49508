'use client';

import { useState } from 'react';

interface PriceAlertModalProps {
  isOpen: boolean;
  onClose: () => void;
  productName: string;
  currentPrice: number;
  productId: string;
}

export default function PriceAlertModal({ isOpen, onClose, productName, currentPrice, productId }: PriceAlertModalProps) {
  const [email, setEmail] = useState('');
  const [targetPrice, setTargetPrice] = useState(currentPrice * 0.9); // Default to 10% off
  const [alertType, setAlertType] = useState<'price_drop' | 'back_in_stock' | 'competitor_beat'>('price_drop');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSuccess(true);
    
    // Auto close after success
    setTimeout(() => {
      setIsSuccess(false);
      onClose();
    }, 3000);
  };

  const suggestedPrices = [
    { label: '5% off', price: currentPrice * 0.95 },
    { label: '10% off', price: currentPrice * 0.90 },
    { label: '15% off', price: currentPrice * 0.85 },
    { label: '20% off', price: currentPrice * 0.80 }
  ];

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '1rem'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '2rem',
        maxWidth: '500px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto',
        position: 'relative'
      }}>
        {/* Close button */}
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '1rem',
            right: '1rem',
            background: 'none',
            border: 'none',
            fontSize: '1.5rem',
            cursor: 'pointer',
            color: '#666'
          }}
        >
          ×
        </button>

        {isSuccess ? (
          <div style={{ textAlign: 'center', padding: '2rem 0' }}>
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>✅</div>
            <h2 style={{ color: '#28a745', marginBottom: '1rem' }}>Price Alert Set!</h2>
            <p style={{ color: '#666' }}>
              We'll notify you when {productName} meets your price criteria.
            </p>
          </div>
        ) : (
          <>
            <h2 style={{ marginBottom: '1rem', color: '#333' }}>
              🔔 Set Price Alert
            </h2>
            
            <div style={{ 
              backgroundColor: '#f8f9fa',
              padding: '1rem',
              borderRadius: '8px',
              marginBottom: '1.5rem'
            }}>
              <div style={{ fontWeight: 'bold', marginBottom: '0.5rem' }}>
                {productName}
              </div>
              <div style={{ color: '#666' }}>
                Current price: <strong>${currentPrice.toFixed(2)}</strong>
              </div>
            </div>

            <form onSubmit={handleSubmit}>
              {/* Alert Type */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{ 
                  display: 'block',
                  fontWeight: 'bold',
                  marginBottom: '0.5rem',
                  color: '#333'
                }}>
                  Alert Type
                </label>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="radio"
                      value="price_drop"
                      checked={alertType === 'price_drop'}
                      onChange={(e) => setAlertType(e.target.value as any)}
                    />
                    <span>💰 Price drops to target amount</span>
                  </label>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="radio"
                      value="competitor_beat"
                      checked={alertType === 'competitor_beat'}
                      onChange={(e) => setAlertType(e.target.value as any)}
                    />
                    <span>🏆 We beat competitor prices</span>
                  </label>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="radio"
                      value="back_in_stock"
                      checked={alertType === 'back_in_stock'}
                      onChange={(e) => setAlertType(e.target.value as any)}
                    />
                    <span>📦 Back in stock notification</span>
                  </label>
                </div>
              </div>

              {/* Target Price */}
              {alertType === 'price_drop' && (
                <div style={{ marginBottom: '1.5rem' }}>
                  <label style={{ 
                    display: 'block',
                    fontWeight: 'bold',
                    marginBottom: '0.5rem',
                    color: '#333'
                  }}>
                    Target Price
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max={currentPrice}
                    value={targetPrice}
                    onChange={(e) => setTargetPrice(parseFloat(e.target.value))}
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '2px solid #e1e5e9',
                      borderRadius: '8px',
                      fontSize: '1rem',
                      marginBottom: '0.5rem'
                    }}
                  />
                  
                  {/* Quick price suggestions */}
                  <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                    {suggestedPrices.map((suggestion, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setTargetPrice(suggestion.price)}
                        style={{
                          padding: '0.25rem 0.5rem',
                          backgroundColor: targetPrice === suggestion.price ? '#007bff' : '#f8f9fa',
                          color: targetPrice === suggestion.price ? 'white' : '#666',
                          border: '1px solid #e1e5e9',
                          borderRadius: '12px',
                          fontSize: '0.8rem',
                          cursor: 'pointer'
                        }}
                      >
                        {suggestion.label} (${suggestion.price.toFixed(2)})
                      </button>
                    ))}
                  </div>
                  
                  <div style={{ 
                    fontSize: '0.9rem',
                    color: targetPrice < currentPrice ? '#28a745' : '#dc3545',
                    marginTop: '0.5rem'
                  }}>
                    {targetPrice < currentPrice ? (
                      `💰 Save $${(currentPrice - targetPrice).toFixed(2)} (${Math.round((1 - targetPrice/currentPrice) * 100)}% off)`
                    ) : (
                      '⚠️ Target price should be lower than current price'
                    )}
                  </div>
                </div>
              )}

              {/* Email */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{ 
                  display: 'block',
                  fontWeight: 'bold',
                  marginBottom: '0.5rem',
                  color: '#333'
                }}>
                  Email Address
                </label>
                <input
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    border: '2px solid #e1e5e9',
                    borderRadius: '8px',
                    fontSize: '1rem'
                  }}
                />
              </div>

              {/* Features */}
              <div style={{ 
                backgroundColor: '#e3f2fd',
                padding: '1rem',
                borderRadius: '8px',
                marginBottom: '1.5rem'
              }}>
                <h4 style={{ margin: '0 0 0.5rem 0', color: '#1976d2' }}>
                  🚀 Smart Alert Features
                </h4>
                <ul style={{ margin: 0, paddingLeft: '1.2rem', color: '#666', fontSize: '0.9rem' }}>
                  <li>Real-time price monitoring across 10+ retailers</li>
                  <li>Instant notifications via email and SMS</li>
                  <li>Price history and trend analysis</li>
                  <li>Stock availability alerts</li>
                  <li>Competitor price beat notifications</li>
                </ul>
              </div>

              {/* Submit */}
              <button
                type="submit"
                disabled={isSubmitting || (alertType === 'price_drop' && targetPrice >= currentPrice)}
                style={{
                  width: '100%',
                  padding: '1rem',
                  backgroundColor: isSubmitting ? '#6c757d' : '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '0.5rem'
                }}
              >
                {isSubmitting ? (
                  <>
                    <div style={{ 
                      width: '20px', 
                      height: '20px', 
                      border: '2px solid #ffffff',
                      borderTop: '2px solid transparent',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }} />
                    Setting Up Alert...
                  </>
                ) : (
                  <>
                    🔔 Create Price Alert
                  </>
                )}
              </button>
            </form>
          </>
        )}

        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    </div>
  );
}
