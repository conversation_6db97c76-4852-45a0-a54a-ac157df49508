import Link from 'next/link';
import { formatMoney } from '@/lib/currency';
import PriceComparisonBadge from './PriceComparisonBadge';

type Props = { product: any };
export default function ProductCard({ product }: Props) {
  const img = product.media?.[0]?.url || 'https://via.placeholder.com/400x300?text=ElectroHub';
  const price = Number(product.variants?.[0]?.price ?? 0);

  return (
    <article style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      borderRadius: '20px',
      padding: '2px',
      transition: 'all 0.3s ease',
      cursor: 'pointer',
      boxShadow: '0 4px 20px rgba(102, 126, 234, 0.15)'
    }}
    onMouseEnter={(e) => {
      e.currentTarget.style.transform = 'translateY(-8px)';
      e.currentTarget.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.25)';
    }}
    onMouseLeave={(e) => {
      e.currentTarget.style.transform = 'translateY(0)';
      e.currentTarget.style.boxShadow = '0 4px 20px rgba(102, 126, 234, 0.15)';
    }}
    >
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '18px',
        overflow: 'hidden',
        position: 'relative'
      }}>
        <Link href={`/product/${product.slug}`} style={{ textDecoration: 'none', color: 'inherit' }}>
          <div style={{ position: 'relative', overflow: 'hidden' }}>
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={img}
              alt={product.title}
              width={400}
              height={300}
              style={{
                width: '100%',
                height: '220px',
                objectFit: 'cover',
                transition: 'transform 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.05)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
              }}
            />

            {/* Gradient overlay */}
            <div style={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: '60px',
              background: 'linear-gradient(transparent, rgba(0,0,0,0.1))',
              pointerEvents: 'none'
            }} />

            {/* Brand Badge */}
            <div style={{
              position: 'absolute',
              top: '1rem',
              left: '1rem',
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%)',
              backdropFilter: 'blur(10px)',
              padding: '0.5rem 1rem',
              borderRadius: '16px',
              fontSize: '0.8rem',
              fontWeight: '600',
              color: '#1e293b',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
            }}>
              {product.brand?.name}
            </div>
          </div>

          <div style={{ padding: '1.5rem', position: 'relative' }}>
            {/* Background pattern */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'radial-gradient(circle at 80% 20%, rgba(102, 126, 234, 0.03) 0%, transparent 50%)',
              pointerEvents: 'none'
            }} />

            <div style={{ position: 'relative', zIndex: 1 }}>
              <h3 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.2rem',
                fontWeight: '700',
                lineHeight: '1.4',
                color: '#1e293b',
                minHeight: '2.8rem',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}>
                {product.title}
              </h3>

              <div style={{
                fontSize: '1.8rem',
                fontWeight: '800',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                marginBottom: '1rem'
              }}>
                {formatMoney(price)}
              </div>

              {/* Price Comparison Badge */}
              <PriceComparisonBadge
                productId={product.slug}
                ourPrice={price}
                compact={true}
              />
            </div>
          </div>
        </Link>
      </div>
    </article>
  );
}
