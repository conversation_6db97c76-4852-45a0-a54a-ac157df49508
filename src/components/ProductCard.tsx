import Link from 'next/link';
import { formatMoney } from '@/lib/currency';
import PriceComparisonBadge from './PriceComparisonBadge';

type Props = { product: any };
export default function ProductCard({ product }: Props) {
  const img = product.media?.[0]?.url || 'https://via.placeholder.com/400x300?text=ElectroHub';
  const price = Number(product.variants?.[0]?.price ?? 0);

  return (
    <article style={{
      position: 'relative',
      cursor: 'pointer',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
    }}
    onMouseEnter={(e) => {
      e.currentTarget.style.transform = 'translateY(-12px) scale(1.02)';
    }}
    onMouseLeave={(e) => {
      e.currentTarget.style.transform = 'translateY(0) scale(1)';
    }}
    >
      {/* Glow effect */}
      <div style={{
        position: 'absolute',
        inset: '-6px',
        background: 'conic-gradient(from 0deg at 50% 50%, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe, #667eea)',
        borderRadius: '32px',
        opacity: 0.6,
        filter: 'blur(20px)',
        animation: 'rotate 10s linear infinite',
        zIndex: -1
      }} />

      {/* Main card */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        borderRadius: '28px',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        overflow: 'hidden',
        position: 'relative',
        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
      }}>
        <Link href={`/product/${product.slug}`} style={{ textDecoration: 'none', color: 'inherit' }}>
          <div style={{ position: 'relative', overflow: 'hidden' }}>
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={img}
              alt={product.title}
              width={400}
              height={300}
              style={{
                width: '100%',
                height: '260px',
                objectFit: 'cover',
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.1)';
                e.currentTarget.style.filter = 'brightness(1.1) saturate(1.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.filter = 'brightness(1) saturate(1)';
              }}
            />

            {/* Animated gradient overlay */}
            <div style={{
              position: 'absolute',
              inset: 0,
              background: 'linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%)',
              opacity: 0,
              transition: 'opacity 0.4s ease',
              pointerEvents: 'none'
            }} />

            {/* Premium brand badge */}
            <div style={{
              position: 'absolute',
              top: '1.5rem',
              left: '1.5rem',
              background: 'rgba(0, 0, 0, 0.8)',
              backdropFilter: 'blur(20px)',
              padding: '0.75rem 1.5rem',
              borderRadius: '20px',
              fontSize: '0.9rem',
              fontWeight: '700',
              color: 'white',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
              textTransform: 'uppercase',
              letterSpacing: '0.1em'
            }}>
              {product.brand?.name}
            </div>

            {/* Floating particles effect */}
            <div style={{
              position: 'absolute',
              top: '2rem',
              right: '2rem',
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%)',
              borderRadius: '50%',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              animation: 'float 6s ease-in-out infinite'
            }} />
          </div>

          <div style={{ padding: '2rem', position: 'relative' }}>
            {/* Animated background pattern */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(245, 87, 108, 0.05) 0%, transparent 50%)',
              animation: 'float 15s ease-in-out infinite',
              pointerEvents: 'none'
            }} />

            <div style={{ position: 'relative', zIndex: 1 }}>
              <h3 style={{
                margin: '0 0 1.5rem 0',
                fontSize: '1.4rem',
                fontWeight: '800',
                lineHeight: '1.3',
                background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                minHeight: '3.6rem',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                letterSpacing: '-0.02em'
              }}>
                {product.title}
              </h3>

              <div style={{
                fontSize: '2.2rem',
                fontWeight: '900',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                marginBottom: '1.5rem',
                letterSpacing: '-0.03em',
                textShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                {formatMoney(price)}
              </div>

              {/* Enhanced Price Comparison Badge */}
              <PriceComparisonBadge
                productId={product.slug}
                ourPrice={price}
                compact={true}
              />
            </div>
          </div>
        </Link>
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes rotate {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
          }
          50% {
            transform: translateY(-10px) rotate(180deg);
            opacity: 1;
          }
        }
      `}</style>
    </article>
  );
}
