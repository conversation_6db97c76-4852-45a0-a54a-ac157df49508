import Link from 'next/link';
import { formatMoney } from '@/lib/currency';

type Props = { product: any };
export default function ProductCard({ product }: Props) {
  const img = product.media?.[0]?.url || 'https://via.placeholder.com/400x300?text=ElectroHub';
  const price = Number(product.variants?.[0]?.price ?? 0);
  return (
    <article style={{ border: '1px solid #ddd', borderRadius: 8, overflow: 'hidden' }}>
      <Link href={`/product/${product.slug}`}>
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img src={img} alt={product.title} width={400} height={300} style={{ width: '100%', height: 'auto' }} />
        <div style={{ padding: '0.75rem' }}>
          <h3 style={{ margin: 0 }}>{product.title}</h3>
          <p style={{ margin: 0 }}>{product.brand?.name}</p>
          <strong>{formatMoney(price)}</strong>
        </div>
      </Link>
    </article>
  );
}
