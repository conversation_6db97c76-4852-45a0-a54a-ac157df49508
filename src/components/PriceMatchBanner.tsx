'use client';

import { useState, useEffect } from 'react';

export default function PriceMatchBanner() {
  const [stats, setStats] = useState({
    totalComparisons: 0,
    averageSavings: 0,
    bestPricePercentage: 0,
    lastUpdated: new Date()
  });
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Simulate real-time stats
    const updateStats = () => {
      setStats({
        totalComparisons: Math.floor(Math.random() * 1000) + 15000,
        averageSavings: Math.floor(Math.random() * 50) + 25,
        bestPricePercentage: Math.floor(Math.random() * 20) + 75,
        lastUpdated: new Date()
      });
    };

    updateStats();
    const interval = setInterval(updateStats, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  if (!isVisible) return null;

  return (
    <div style={{
      position: 'relative',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      color: 'white',
      padding: '3rem 1rem',
      overflow: 'hidden'
    }}>
      {/* Advanced animated background */}
      <div style={{
        position: 'absolute',
        inset: 0,
        background: 'conic-gradient(from 0deg at 50% 50%, rgba(255,255,255,0.1), transparent, rgba(255,255,255,0.1), transparent)',
        animation: 'rotate 30s linear infinite',
        pointerEvents: 'none'
      }} />

      {/* Floating geometric shapes */}
      <div style={{
        position: 'absolute',
        top: '10%',
        left: '5%',
        width: '100px',
        height: '100px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '50%',
        animation: 'float 20s ease-in-out infinite',
        pointerEvents: 'none'
      }} />

      <div style={{
        position: 'absolute',
        top: '60%',
        right: '10%',
        width: '80px',
        height: '80px',
        background: 'rgba(255, 255, 255, 0.08)',
        borderRadius: '20px',
        transform: 'rotate(45deg)',
        animation: 'float 25s ease-in-out infinite reverse',
        pointerEvents: 'none'
      }} />

      {/* Particle system */}
      <div style={{
        position: 'absolute',
        top: '-50%',
        left: '-10%',
        width: '120%',
        height: '200%',
        background: 'radial-gradient(circle, rgba(255,255,255,0.15) 2px, transparent 2px)',
        backgroundSize: '60px 60px',
        animation: 'float 40s infinite linear',
        pointerEvents: 'none',
        opacity: 0.6
      }} />

      {/* Premium close button */}
      <button
        onClick={() => setIsVisible(false)}
        style={{
          position: 'absolute',
          top: '2rem',
          right: '2rem',
          background: 'rgba(0, 0, 0, 0.3)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          color: 'white',
          borderRadius: '50%',
          width: '50px',
          height: '50px',
          cursor: 'pointer',
          fontSize: '1.5rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          zIndex: 20,
          boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
          e.currentTarget.style.transform = 'scale(1.15) rotate(90deg)';
          e.currentTarget.style.boxShadow = '0 12px 35px rgba(0, 0, 0, 0.4)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = 'rgba(0, 0, 0, 0.3)';
          e.currentTarget.style.transform = 'scale(1) rotate(0deg)';
          e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
        }}
      >
        ×
      </button>

      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '2rem',
        alignItems: 'center',
        position: 'relative',
        zIndex: 1
      }}>
        {/* Main Message */}
        <div>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem',
            marginBottom: '1rem'
          }}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(10px)',
              borderRadius: '16px',
              padding: '12px',
              border: '1px solid rgba(255, 255, 255, 0.2)'
            }}>
              <span style={{ fontSize: '2rem' }}>🏆</span>
            </div>
            <h2 style={{
              margin: 0,
              fontSize: '2rem',
              fontWeight: '800',
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
            }}>
              Real-Time Price Intelligence
            </h2>
          </div>
          <p style={{
            margin: 0,
            opacity: 0.95,
            fontSize: '1.1rem',
            lineHeight: '1.6',
            textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
          }}>
            AI-powered monitoring across 10+ premium retailers ensures you always get the absolute best deals available
          </p>
        </div>

        {/* Stats */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: '1.5rem'
        }}>
          {[
            { value: `${stats.totalComparisons.toLocaleString()}+`, label: 'Daily Price Checks', icon: '🔍' },
            { value: `$${stats.averageSavings}`, label: 'Average Savings', icon: '💰' },
            { value: `${stats.bestPricePercentage}%`, label: 'Best Price Match', icon: '🎯' }
          ].map((stat, index) => (
            <div key={index} style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              borderRadius: '16px',
              padding: '1.5rem',
              textAlign: 'center',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              transition: 'transform 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
            }}
            >
              <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>
                {stat.icon}
              </div>
              <div style={{
                fontSize: '2.2rem',
                fontWeight: '800',
                marginBottom: '0.5rem',
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
              }}>
                {stat.value}
              </div>
              <div style={{
                fontSize: '0.9rem',
                opacity: 0.9,
                fontWeight: '500'
              }}>
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div style={{
          textAlign: 'center',
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: '20px',
          padding: '2rem',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.15)',
            borderRadius: '16px',
            padding: '12px',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '1rem'
          }}>
            <span style={{ fontSize: '2rem' }}>💡</span>
          </div>
          <div style={{
            marginBottom: '1rem',
            fontWeight: '800',
            fontSize: '1.3rem',
            textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
          }}>
            100% Price Match Guarantee
          </div>
          <div style={{
            fontSize: '1rem',
            opacity: 0.95,
            lineHeight: '1.5',
            textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
          }}>
            Found a lower price? We'll match it instantly and give you 10% of the difference as store credit!
          </div>
        </div>
      </div>

      {/* Last updated indicator */}
      <div style={{ 
        position: 'absolute',
        bottom: '0.25rem',
        right: '0.5rem',
        fontSize: '0.7rem',
        opacity: 0.7
      }}>
        🔄 Updated {stats.lastUpdated.toLocaleTimeString()}
      </div>

      {/* Animated background elements */}
      <div style={{
        position: 'absolute',
        top: '-50%',
        left: '-10%',
        width: '120%',
        height: '200%',
        background: 'radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)',
        backgroundSize: '20px 20px',
        animation: 'float 20s infinite linear',
        pointerEvents: 'none'
      }} />

      <style jsx>{`
        @keyframes rotate {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.6;
          }
          25% {
            transform: translateY(-20px) rotate(90deg);
            opacity: 0.8;
          }
          50% {
            transform: translateY(-40px) rotate(180deg);
            opacity: 1;
          }
          75% {
            transform: translateY(-20px) rotate(270deg);
            opacity: 0.8;
          }
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.8;
            transform: scale(1.05);
          }
        }

        @keyframes glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
          }
          50% {
            box-shadow: 0 0 40px rgba(255, 255, 255, 0.6);
          }
        }
      `}</style>
    </div>
  );
}
