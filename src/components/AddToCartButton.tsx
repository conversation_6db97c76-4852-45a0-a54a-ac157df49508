'use client';

export default function AddToCartButton({ variantId }: { variantId: string }) {
  async function add() {
    const res = await fetch('/api/cart', { method: 'POST', headers: { 'content-type': 'application/json' }, body: JSON.stringify({ variantId, quantity: 1 }) });
    if (!res.ok) alert('Failed to add to cart');
    else alert('Added to cart');
  }
  return <button onClick={add} aria-label="Add to cart">Add to cart</button>;
}
