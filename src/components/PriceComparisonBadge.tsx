'use client';

import { useState, useEffect } from 'react';

interface PriceComparisonBadgeProps {
  productId: string;
  ourPrice: number;
  compact?: boolean;
}

export default function PriceComparisonBadge({ productId, ourPrice, compact = false }: PriceComparisonBadgeProps) {
  const [competitorPrices, setCompetitorPrices] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Mock competitor prices - in real app, this would come from API
  const mockPrices: Record<string, number[]> = {
    'iphone-15-pro-max': [1189.99, 1199.00, 1199.99, 1179.99, 1199.99, 1209.99],
    'macbook-air-m3': [1299.99, 1299.99, 1299.99, 1279.99],
    'sony-wh-1000xm5': [379.99, 399.99, 399.99, 389.99],
    'samsung-galaxy-s24-ultra': [1289.99, 1299.99, 1309.99, 1279.99],
    'airpods-pro-2': [239.99, 249.99, 249.99, 244.99],
    'dell-xps-13-plus': [1389.99, 1399.99, 1409.99],
    'nintendo-switch-oled': [349.99, 349.99, 359.99, 339.99],
    'ipad-pro-12-9-m2': [1089.99, 1099.99, 1109.99, 1079.99],
    'google-pixel-8-pro': [989.99, 999.99, 1009.99, 979.99],
    'surface-laptop-5': [1289.99, 1299.99, 1309.99]
  };

  useEffect(() => {
    const fetchPrices = async () => {
      setIsLoading(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const prices = mockPrices[productId] || [];
      setCompetitorPrices(prices);
      setIsLoading(false);
    };

    fetchPrices();
  }, [productId]);

  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        padding: compact ? '0.25rem 0.5rem' : '0.5rem 0.75rem',
        backgroundColor: '#f8f9fa',
        borderRadius: '6px',
        fontSize: compact ? '0.8rem' : '0.9rem'
      }}>
        <div style={{ 
          width: '12px', 
          height: '12px', 
          border: '2px solid #007bff',
          borderTop: '2px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <span style={{ color: '#666' }}>Checking prices...</span>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (competitorPrices.length === 0) {
    return null;
  }

  const lowestPrice = Math.min(...competitorPrices);
  const highestPrice = Math.max(...competitorPrices);
  const averagePrice = competitorPrices.reduce((sum, price) => sum + price, 0) / competitorPrices.length;
  const savings = Math.max(0, highestPrice - ourPrice);
  const isBestPrice = ourPrice <= lowestPrice;
  const isGoodDeal = ourPrice <= averagePrice;

  if (compact) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: '0.75rem'
      }}>
        {isBestPrice ? (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.5rem 1rem',
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            color: 'white',
            borderRadius: '16px',
            fontWeight: '600',
            fontSize: '0.85rem',
            boxShadow: '0 2px 8px rgba(16, 185, 129, 0.3)'
          }}>
            <span style={{ fontSize: '1rem' }}>🏆</span>
            Best Price
          </div>
        ) : isGoodDeal ? (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.5rem 1rem',
            background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
            color: 'white',
            borderRadius: '16px',
            fontWeight: '600',
            fontSize: '0.85rem',
            boxShadow: '0 2px 8px rgba(245, 158, 11, 0.3)'
          }}>
            <span style={{ fontSize: '1rem' }}>💰</span>
            Great Deal
          </div>
        ) : (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.5rem 1rem',
            background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
            color: 'white',
            borderRadius: '16px',
            fontWeight: '600',
            fontSize: '0.85rem',
            boxShadow: '0 2px 8px rgba(107, 114, 128, 0.3)'
          }}>
            <span style={{ fontSize: '1rem' }}>📊</span>
            Compared
          </div>
        )}

        <div style={{
          fontSize: '0.8rem',
          color: '#64748b',
          fontWeight: '500'
        }}>
          vs {competitorPrices.length} stores
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '0.75rem',
      backgroundColor: isBestPrice ? '#d4edda' : isGoodDeal ? '#fff3cd' : '#f8f9fa',
      border: `1px solid ${isBestPrice ? '#c3e6cb' : isGoodDeal ? '#ffeaa7' : '#e1e5e9'}`,
      borderRadius: '8px',
      marginTop: '0.5rem'
    }}>
      <div style={{ 
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '0.5rem'
      }}>
        <div style={{ 
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          fontWeight: 'bold',
          color: isBestPrice ? '#155724' : isGoodDeal ? '#856404' : '#333'
        }}>
          {isBestPrice ? '🏆 BEST PRICE' : isGoodDeal ? '💰 GREAT DEAL' : '📊 PRICE CHECKED'}
        </div>
        
        <div style={{ 
          fontSize: '0.8rem',
          color: '#666'
        }}>
          vs {competitorPrices.length} retailers
        </div>
      </div>
      
      <div style={{ 
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(80px, 1fr))',
        gap: '0.5rem',
        fontSize: '0.8rem'
      }}>
        <div>
          <div style={{ color: '#666' }}>Lowest</div>
          <div style={{ 
            fontWeight: 'bold',
            color: ourPrice <= lowestPrice ? '#28a745' : '#dc3545'
          }}>
            ${lowestPrice.toFixed(2)}
          </div>
        </div>
        
        <div>
          <div style={{ color: '#666' }}>Average</div>
          <div style={{ 
            fontWeight: 'bold',
            color: ourPrice <= averagePrice ? '#28a745' : '#ffc107'
          }}>
            ${averagePrice.toFixed(2)}
          </div>
        </div>
        
        <div>
          <div style={{ color: '#666' }}>Highest</div>
          <div style={{ fontWeight: 'bold', color: '#666' }}>
            ${highestPrice.toFixed(2)}
          </div>
        </div>
        
        {savings > 0 && (
          <div>
            <div style={{ color: '#666' }}>You Save</div>
            <div style={{ fontWeight: 'bold', color: '#28a745' }}>
              ${savings.toFixed(2)}
            </div>
          </div>
        )}
      </div>
      
      {isBestPrice && (
        <div style={{ 
          marginTop: '0.5rem',
          fontSize: '0.8rem',
          color: '#155724',
          fontWeight: 'bold'
        }}>
          ✅ Price Match Guaranteed
        </div>
      )}
    </div>
  );
}
