'use client';

import { useState, useEffect } from 'react';

interface MobilePriceComparisonProps {
  productName: string;
  ourPrice: number;
  productId: string;
}

interface CompetitorPrice {
  store: string;
  price: number;
  logo: string;
  savings?: number;
}

export default function MobilePriceComparison({ productName, ourPrice, productId }: MobilePriceComparisonProps) {
  const [competitors, setCompetitors] = useState<CompetitorPrice[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Mock competitor data
  const mockCompetitorData: Record<string, CompetitorPrice[]> = {
    'iphone-15-pro-max': [
      { store: 'Amazon', price: 1189.99, logo: '🛒' },
      { store: 'Best Buy', price: 1199.00, logo: '🏪' },
      { store: 'Apple Store', price: 1199.99, logo: '🍎' },
      { store: 'B&H Photo', price: 1179.99, logo: '📷' }
    ],
    'macbook-air-m3': [
      { store: 'Apple Store', price: 1299.99, logo: '🍎' },
      { store: 'Amazon', price: 1299.99, logo: '🛒' },
      { store: 'Best Buy', price: 1299.99, logo: '🏪' },
      { store: 'Costco', price: 1279.99, logo: '🏢' }
    ],
    'sony-wh-1000xm5': [
      { store: 'Amazon', price: 379.99, logo: '🛒' },
      { store: 'Best Buy', price: 399.99, logo: '🏪' },
      { store: 'Sony Direct', price: 399.99, logo: '📺' },
      { store: 'Target', price: 389.99, logo: '🎯' }
    ]
  };

  useEffect(() => {
    const fetchPrices = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const competitorData = mockCompetitorData[productId] || [];
      const competitorsWithSavings = competitorData.map(comp => ({
        ...comp,
        savings: Math.max(0, comp.price - ourPrice)
      }));
      
      setCompetitors(competitorsWithSavings);
      setIsLoading(false);
    };

    fetchPrices();
  }, [productId, ourPrice]);

  const lowestPrice = Math.min(...competitors.map(c => c.price));
  const isBestPrice = ourPrice <= lowestPrice;
  const maxSavings = Math.max(...competitors.map(c => c.savings || 0));

  if (isLoading) {
    return (
      <div style={{ 
        backgroundColor: '#f8f9fa',
        border: '1px solid #e1e5e9',
        borderRadius: '8px',
        padding: '1rem',
        margin: '1rem 0',
        textAlign: 'center'
      }}>
        <div style={{ 
          width: '20px', 
          height: '20px', 
          border: '2px solid #007bff',
          borderTop: '2px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          margin: '0 auto 0.5rem'
        }} />
        <div style={{ fontSize: '0.9rem', color: '#666' }}>
          Checking competitor prices...
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{ 
      backgroundColor: '#ffffff',
      border: '1px solid #e1e5e9',
      borderRadius: '8px',
      margin: '1rem 0',
      overflow: 'hidden'
    }}>
      {/* Header - Always Visible */}
      <div 
        onClick={() => setIsExpanded(!isExpanded)}
        style={{ 
          padding: '1rem',
          backgroundColor: isBestPrice ? '#d4edda' : '#fff3cd',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
          <div style={{ fontSize: '1.5rem' }}>
            {isBestPrice ? '🏆' : '💰'}
          </div>
          <div>
            <div style={{ 
              fontWeight: 'bold',
              color: isBestPrice ? '#155724' : '#856404',
              fontSize: '0.9rem'
            }}>
              {isBestPrice ? 'BEST PRICE FOUND' : 'GREAT DEAL'}
            </div>
            <div style={{ 
              fontSize: '0.8rem',
              color: '#666'
            }}>
              Compared with {competitors.length} stores
              {maxSavings > 0 && ` • Save up to $${maxSavings.toFixed(2)}`}
            </div>
          </div>
        </div>
        
        <div style={{ 
          fontSize: '1.2rem',
          color: '#666',
          transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.3s ease'
        }}>
          ▼
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div style={{ 
          borderTop: '1px solid #e1e5e9',
          animation: 'slideDown 0.3s ease'
        }}>
          {/* Our Price */}
          <div style={{ 
            padding: '1rem',
            backgroundColor: '#f8f9fa',
            borderBottom: '1px solid #e1e5e9'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <div style={{ fontWeight: 'bold', color: '#333' }}>ElectroHub</div>
                <div style={{ fontSize: '0.8rem', color: '#666' }}>Our Price</div>
              </div>
              <div style={{ 
                fontSize: '1.2rem',
                fontWeight: 'bold',
                color: '#007bff'
              }}>
                ${ourPrice.toFixed(2)}
              </div>
            </div>
          </div>

          {/* Competitor Prices */}
          <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
            {competitors.map((competitor, index) => (
              <div key={index} style={{ 
                padding: '0.75rem 1rem',
                borderBottom: index < competitors.length - 1 ? '1px solid #f0f0f0' : 'none',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <div style={{ fontSize: '1.2rem' }}>
                    {competitor.logo}
                  </div>
                  <div>
                    <div style={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                      {competitor.store}
                    </div>
                    {competitor.savings && competitor.savings > 0 && (
                      <div style={{ 
                        fontSize: '0.7rem',
                        color: '#28a745',
                        fontWeight: 'bold'
                      }}>
                        You save ${competitor.savings.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>
                
                <div style={{ textAlign: 'right' }}>
                  <div style={{ 
                    fontSize: '1rem',
                    fontWeight: 'bold',
                    color: competitor.price < ourPrice ? '#dc3545' : 
                           competitor.price > ourPrice ? '#28a745' : '#007bff'
                  }}>
                    ${competitor.price.toFixed(2)}
                  </div>
                  {competitor.price !== ourPrice && (
                    <div style={{ 
                      fontSize: '0.7rem',
                      color: competitor.price < ourPrice ? '#dc3545' : '#28a745'
                    }}>
                      {competitor.price < ourPrice ? 'Lower' : 'Higher'}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Price Match Guarantee */}
          <div style={{ 
            padding: '1rem',
            backgroundColor: '#e3f2fd',
            textAlign: 'center'
          }}>
            <div style={{ 
              fontWeight: 'bold',
              color: '#1976d2',
              fontSize: '0.9rem',
              marginBottom: '0.25rem'
            }}>
              🛡️ Price Match Guarantee
            </div>
            <div style={{ 
              fontSize: '0.8rem',
              color: '#666'
            }}>
              Found a lower price? We'll match it!
            </div>
          </div>

          {/* Action Buttons */}
          <div style={{ 
            padding: '1rem',
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '0.5rem'
          }}>
            <button style={{
              padding: '0.75rem',
              backgroundColor: '#ffc107',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '0.8rem',
              fontWeight: 'bold',
              cursor: 'pointer'
            }}>
              🔔 Price Alert
            </button>
            
            <button style={{
              padding: '0.75rem',
              backgroundColor: '#17a2b8',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '0.8rem',
              fontWeight: 'bold',
              cursor: 'pointer'
            }}>
              📈 Price History
            </button>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
}
