import { formatMoney } from '@/lib/currency';
import AddToCartButton from '@/components/AddToCartButton';
import Link from 'next/link';

// Mock product data - comprehensive product details
const mockProducts = {
  'iphone-15-pro-max': {
    id: '1',
    title: 'iPhone 15 Pro Max',
    slug: 'iphone-15-pro-max',
    brand: { name: 'Apple' },
    category: { name: 'Smartphones', slug: 'smartphones' },
    variants: [{ id: 'v1', price: 1199.99, sku: 'IPHONE15PM-256-TIT' }],
    media: [
      { url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop' },
      { url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop&sat=-100' },
      { url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800&h=600&fit=crop&hue=180' }
    ],
    description: 'The most advanced iPhone ever with titanium design, A17 Pro chip, and professional camera system.',
    features: [
      '6.7-inch Super Retina XDR display',
      'A17 Pro chip with 6-core GPU',
      'Pro camera system with 48MP main camera',
      'Up to 29 hours video playback',
      'Titanium design with textured matte glass back',
      'Action Button for quick access to features',
      'USB-C connector',
      'Face ID for secure authentication'
    ],
    specifications: {
      'Display': '6.7-inch Super Retina XDR OLED, 2796 x 1290 pixels',
      'Processor': 'A17 Pro chip',
      'Storage': '256GB, 512GB, 1TB',
      'Camera': '48MP Main, 12MP Ultra Wide, 12MP Telephoto',
      'Battery': 'Up to 29 hours video playback',
      'Operating System': 'iOS 17',
      'Connectivity': '5G, Wi-Fi 6E, Bluetooth 5.3',
      'Dimensions': '159.9 × 76.7 × 8.25 mm',
      'Weight': '221 grams'
    },
    inStock: true,
    rating: 4.8,
    reviewCount: 1247
  },
  'macbook-air-m3': {
    id: '9',
    title: 'MacBook Air M3',
    slug: 'macbook-air-m3',
    brand: { name: 'Apple' },
    category: { name: 'Laptops & Computers', slug: 'laptops-computers' },
    variants: [{ id: 'v9', price: 1299.99, sku: 'MBA-M3-13-256-SG' }],
    media: [
      { url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=800&h=600&fit=crop' },
      { url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=800&h=600&fit=crop&sat=-50' }
    ],
    description: 'Supercharged by the M3 chip, MacBook Air is up to 60% faster than the previous generation.',
    features: [
      '13.6-inch Liquid Retina display',
      'Apple M3 chip with 8-core CPU and 10-core GPU',
      'Up to 18 hours battery life',
      '256GB SSD storage',
      '8GB unified memory',
      'Two Thunderbolt ports',
      'MagSafe 3 charging',
      'Backlit Magic Keyboard with Touch ID'
    ],
    specifications: {
      'Display': '13.6-inch Liquid Retina, 2560 x 1664 pixels',
      'Processor': 'Apple M3 chip',
      'Memory': '8GB unified memory',
      'Storage': '256GB SSD',
      'Graphics': '10-core GPU',
      'Battery': 'Up to 18 hours',
      'Operating System': 'macOS Sonoma',
      'Ports': '2x Thunderbolt, MagSafe 3, 3.5mm headphone',
      'Dimensions': '30.41 × 21.5 × 1.13 cm',
      'Weight': '1.24 kg'
    },
    inStock: true,
    rating: 4.7,
    reviewCount: 892
  },
  'sony-wh-1000xm5': {
    id: '16',
    title: 'Sony WH-1000XM5',
    slug: 'sony-wh-1000xm5',
    brand: { name: 'Sony' },
    category: { name: 'Audio & Headphones', slug: 'audio-headphones' },
    variants: [{ id: 'v16', price: 399.99, sku: 'SONY-WH1000XM5-BLK' }],
    media: [
      { url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=800&h=600&fit=crop' }
    ],
    description: 'Industry-leading noise canceling with new lightweight design for all-day comfort.',
    features: [
      'Industry-leading noise canceling technology',
      '30-hour battery life with quick charge',
      'Crystal clear hands-free calling',
      'Intuitive touch control settings',
      'Multipoint connection to two devices',
      'Adaptive Sound Control',
      'Speak-to-Chat technology',
      'Premium comfort and sound'
    ],
    specifications: {
      'Driver': '30mm dynamic drivers',
      'Frequency Response': '4Hz - 40kHz',
      'Battery Life': '30 hours with ANC on',
      'Charging': 'USB-C, 3 min charge = 3 hours playback',
      'Weight': '250g',
      'Connectivity': 'Bluetooth 5.2, NFC',
      'Noise Canceling': 'Dual Noise Sensor technology',
      'Microphone': 'Beamforming microphone'
    },
    inStock: true,
    rating: 4.6,
    reviewCount: 2156
  }
};

export default function ProductDetailPage({ params }: any) {
  const productSlug = params.slug;
  const product = mockProducts[productSlug as keyof typeof mockProducts];

  if (!product) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center', maxWidth: '600px', margin: '0 auto' }}>
        <h1>Product Not Found</h1>
        <p>The product "{productSlug}" doesn't exist in our catalog.</p>
        <Link href="/" style={{ color: '#007bff', textDecoration: 'none', fontWeight: 'bold' }}>
          ← Back to Home
        </Link>
      </div>
    );
  }

  const variant = product.variants[0];
  const mainImage = product.media[0]?.url || 'https://via.placeholder.com/800x600?text=ElectroHub';

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto', padding: '1rem' }}>
      {/* Breadcrumb */}
      <nav style={{ marginBottom: '2rem', fontSize: '0.9rem', color: '#666' }}>
        <Link href="/" style={{ color: '#007bff', textDecoration: 'none' }}>Home</Link>
        {' > '}
        <Link href={`/category/${product.category.slug}`} style={{ color: '#007bff', textDecoration: 'none' }}>
          {product.category.name}
        </Link>
        {' > '}
        <span>{product.title}</span>
      </nav>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '3rem', marginBottom: '3rem' }}>
        {/* Product Images */}
        <div>
          <div style={{ marginBottom: '1rem' }}>
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={mainImage}
              alt={product.title}
              style={{
                width: '100%',
                height: 'auto',
                borderRadius: '12px',
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
              }}
            />
          </div>

          {/* Thumbnail Images */}
          {product.media.length > 1 && (
            <div style={{ display: 'flex', gap: '0.5rem', overflowX: 'auto' }}>
              {product.media.map((media, index) => (
                <img
                  key={index}
                  src={media.url}
                  alt={`${product.title} view ${index + 1}`}
                  style={{
                    width: '80px',
                    height: '80px',
                    objectFit: 'cover',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    border: index === 0 ? '2px solid #007bff' : '2px solid transparent'
                  }}
                />
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div>
          <div style={{ marginBottom: '1rem' }}>
            <Link
              href={`/category/${product.category.slug}`}
              style={{
                color: '#007bff',
                textDecoration: 'none',
                fontSize: '0.9rem',
                fontWeight: '500'
              }}
            >
              {product.category.name}
            </Link>
          </div>

          <h1 style={{ fontSize: '2.5rem', marginBottom: '0.5rem', lineHeight: '1.2' }}>
            {product.title}
          </h1>

          <div style={{ marginBottom: '1rem', color: '#666', fontSize: '1.1rem' }}>
            by <strong>{product.brand.name}</strong>
          </div>

          {/* Rating */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            marginBottom: '1.5rem'
          }}>
            <div style={{ color: '#ffa500' }}>
              {'★'.repeat(Math.floor(product.rating))}{'☆'.repeat(5 - Math.floor(product.rating))}
            </div>
            <span style={{ fontWeight: 'bold' }}>{product.rating}</span>
            <span style={{ color: '#666' }}>({product.reviewCount.toLocaleString()} reviews)</span>
          </div>

          {/* Price */}
          <div style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            color: '#007bff',
            marginBottom: '1.5rem'
          }}>
            {formatMoney(variant.price)}
          </div>

          {/* Stock Status */}
          <div style={{
            marginBottom: '1.5rem',
            padding: '0.5rem 1rem',
            backgroundColor: product.inStock ? '#d4edda' : '#f8d7da',
            color: product.inStock ? '#155724' : '#721c24',
            borderRadius: '6px',
            display: 'inline-block',
            fontWeight: '500'
          }}>
            {product.inStock ? '✓ In Stock' : '✗ Out of Stock'}
          </div>

          {/* Description */}
          <p style={{
            fontSize: '1.1rem',
            lineHeight: '1.6',
            marginBottom: '2rem',
            color: '#333'
          }}>
            {product.description}
          </p>

          {/* Add to Cart */}
          <div style={{ marginBottom: '2rem' }}>
            <AddToCartButton variantId={variant.id} />
          </div>

          {/* Key Features */}
          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '1.5rem',
            borderRadius: '12px',
            marginBottom: '2rem'
          }}>
            <h3 style={{ marginBottom: '1rem' }}>Key Features</h3>
            <ul style={{ margin: 0, paddingLeft: '1.2rem' }}>
              {product.features.map((feature, index) => (
                <li key={index} style={{ marginBottom: '0.5rem', lineHeight: '1.5' }}>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Specifications */}
      <div style={{
        backgroundColor: '#ffffff',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '2rem',
        marginBottom: '2rem'
      }}>
        <h2 style={{ marginBottom: '1.5rem' }}>Technical Specifications</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem' }}>
          {Object.entries(product.specifications).map(([key, value]) => (
            <div key={key} style={{
              display: 'flex',
              padding: '0.75rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px'
            }}>
              <strong style={{ minWidth: '120px', color: '#333' }}>{key}:</strong>
              <span style={{ color: '#666' }}>{value}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Related Products Placeholder */}
      <div style={{
        textAlign: 'center',
        padding: '2rem',
        backgroundColor: '#f8f9fa',
        borderRadius: '12px'
      }}>
        <h3>Related Products</h3>
        <p style={{ color: '#666', marginBottom: '1rem' }}>
          Discover more products from {product.brand.name} and similar items in {product.category.name}
        </p>
        <Link
          href={`/category/${product.category.slug}`}
          style={{
            display: 'inline-block',
            padding: '12px 24px',
            backgroundColor: '#007bff',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '8px',
            fontWeight: 'bold'
          }}
        >
          Browse {product.category.name} →
        </Link>
      </div>
    </div>
  );
}
