import { prisma } from '@/lib/prisma';
import { formatMoney } from '@/lib/currency';
import AddToCartButton from '@/components/AddToCartButton';

export default async function PDP({ params }: any) {
  const product = await prisma.product.findUnique({ where: { slug: params.slug }, include: { media: true, variants: true, brand: true, category: true } });
  if (!product) return <div>Not found</div>;
  const v = product.variants[0];
  const img = product.media?.[0]?.url || 'https://via.placeholder.com/600x400?text=ElectroHub';
  return (
    <div style={{ padding: '1rem', display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem' }}>
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img src={img} alt={product.title} width={800} height={600} style={{ width: '100%', height: 'auto' }} />
      <div>
        <h1>{product.title}</h1>
        <p>{product.brand?.name} — {product.category?.name}</p>
        {v && <p><strong>{formatMoney(Number(v.price))}</strong></p>}
        <p>{product.description}</p>
        {v && <AddToCartButton variantId={v.id} />}
      </div>
    </div>
  );
}
