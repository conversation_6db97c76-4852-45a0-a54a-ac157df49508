import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { prisma } from '@/lib/prisma';

export async function POST(req: Request) {
  const sig = req.headers.get('stripe-signature');
  const secret = process.env.STRIPE_WEBHOOK_SECRET!;
  const raw = await req.text();
  let event: Stripe.Event;
  try {
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', { apiVersion: '2024-04-10' });
    event = stripe.webhooks.constructEvent(raw, sig!, secret);
  } catch (err: any) {
    return new NextResponse(`Webhook Error: ${err.message}`, { status: 400 });
  }

  if (event.type === 'checkout.session.completed') {
    const session = event.data.object as Stripe.Checkout.Session;
    const cartId = session.metadata?.cartId;
    if (cartId) {
      const cart = await prisma.cart.findUnique({ where: { id: cartId }, include: { items: { include: { variant: { include: { product: true } } } } } });
      if (cart && cart.items.length) {
        const total = cart.items.reduce((s, i) => s + Number(i.unitPrice) * i.quantity, 0);
        const userId = cart.userId ?? (await prisma.user.create({ data: {} })).id;
        const order = await prisma.order.create({ data: { userId, total, currency: 'USD', status: 'PAID' } });
        for (const i of cart.items) {
          await prisma.orderItem.create({ data: { orderId: order.id, vendorId: i.variant.product.vendorId, variantId: i.variantId, quantity: i.quantity, unitPrice: i.unitPrice } });
        }
        await prisma.payment.create({ data: { orderId: order.id, provider: 'STRIPE', providerId: session.id, status: 'SUCCEEDED', amount: total, currency: 'USD', raw: event } });
        await prisma.cart.delete({ where: { id: cart.id } });
      }
    }
  }

  return NextResponse.json({ received: true });
}
