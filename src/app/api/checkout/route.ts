import Stripe from 'stripe';
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/prisma';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', { apiVersion: '2024-04-10' });

export async function POST() {
  const base = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const sessionId = cookies().get('cartId')?.value;
  if (!sessionId) return NextResponse.json({ error: 'Empty cart' }, { status: 400 });
  const cart = await prisma.cart.findUnique({ where: { sessionId }, include: { items: { include: { variant: { include: { product: true } } } } } });
  const items = cart?.items || [];
  if (items.length === 0) return NextResponse.json({ error: 'Empty cart' }, { status: 400 });

  const line_items = items.map(i => ({
    quantity: i.quantity,
    price_data: {
      currency: 'usd',
      product_data: { name: i.variant.product.title },
      unit_amount: Math.round(Number(i.unitPrice) * 100)
    }
  }));

  const checkout = await stripe.checkout.sessions.create({
    mode: 'payment',
    line_items,
    success_url: `${base}/?success=1`,
    cancel_url: `${base}/cart?canceled=1`,
    metadata: { cartId: cart!.id },
  });

  return NextResponse.json({ url: checkout.url });
}
