import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { randomUUID } from 'crypto';

const AddSchema = z.object({ variantId: z.string(), quantity: z.number().int().min(1).max(10).default(1) });

export async function GET() {
  const jar = cookies();
  let sessionId = jar.get('cartId')?.value;
  if (!sessionId) {
    sessionId = randomUUID();
    jar.set('cartId', sessionId, { httpOnly: true, sameSite: 'lax', path: '/', secure: true, maxAge: 60 * 60 * 24 * 7 });
  }
  let cart = await prisma.cart.findUnique({ where: { sessionId }, include: { items: { include: { variant: { include: { product: true } } } } } });
  if (!cart) cart = await prisma.cart.create({ data: { sessionId } });
  return NextResponse.json(cart);
}

export async function POST(req: Request) {
  const jar = cookies();
  let sessionId = jar.get('cartId')?.value;
  if (!sessionId) {
    sessionId = randomUUID();
    jar.set('cartId', sessionId, { httpOnly: true, sameSite: 'lax', path: '/', secure: true, maxAge: 60 * 60 * 24 * 7 });
  }
  const body = await req.json();
  const parsed = AddSchema.safeParse(body);
  if (!parsed.success) return NextResponse.json({ error: parsed.error.flatten() }, { status: 400 });
  const variant = await prisma.productVariant.findUnique({ where: { id: parsed.data.variantId }, include: { inventory: true, product: true } });
  if (!variant) return NextResponse.json({ error: 'Variant not found' }, { status: 404 });
  const price = Number(variant.price);
  let cart = await prisma.cart.findUnique({ where: { sessionId } });
  if (!cart) cart = await prisma.cart.create({ data: { sessionId } });
  await prisma.cartItem.create({ data: { cartId: cart.id, variantId: variant.id, quantity: parsed.data.quantity, unitPrice: price } });
  return NextResponse.json({ ok: true });
}
