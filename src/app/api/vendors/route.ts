import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const CreateVendor = z.object({ name: z.string().min(2), slug: z.string().min(2), contactEmail: z.string().email().optional() });

export async function GET() {
  const vendors = await prisma.vendor.findMany({ orderBy: { name: 'asc' } });
  return NextResponse.json(vendors);
}

export async function POST(req: Request) {
  const json = await req.json();
  const parsed = CreateVendor.safeParse(json);
  if (!parsed.success) return NextResponse.json({ error: parsed.error.flatten() }, { status: 400 });
  const v = await prisma.vendor.create({ data: { ...parsed.data, kycStatus: 'PENDING' } });
  return NextResponse.json(v, { status: 201 });
}
