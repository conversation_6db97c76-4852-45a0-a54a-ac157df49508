import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const CreateSchema = z.object({
  title: z.string().min(3),
  description: z.string().min(10),
  vendorId: z.string(),
  brandId: z.string().optional(),
  categoryId: z.string(),
  price: z.number().positive(),
  currency: z.string().default('USD'),
  sku: z.string().min(3)
});

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const q = searchParams.get('q') || undefined;
  const categorySlug = searchParams.get('category') || undefined;
  const products = await prisma.product.findMany({
    where: { status: 'PUBLISHED', ...(q ? { OR: [{ title: { contains: q, mode: 'insensitive' } }, { description: { contains: q, mode: 'insensitive' } }] } : {}), ...(categorySlug ? { category: { slug: categorySlug } } : {}) },
    include: { media: true, brand: true, variants: true },
    take: 50
  });
  return NextResponse.json(products);
}

export async function POST(req: Request) {
  const body = await req.json();
  const data = CreateSchema.safeParse(body);
  if (!data.success) return NextResponse.json({ error: data.error.flatten() }, { status: 400 });
  const { title, description, vendorId, brandId, categoryId, price, currency, sku } = data.data;
  const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
  const product = await prisma.product.create({
    data: {
      title, description, slug, status: 'PUBLISHED', vendorId, brandId, categoryId,
      variants: { create: { sku, attributes: {}, condition: 'NEW', price, currency, inventory: { create: { stock: 25 } } } }
    },
    include: { variants: true }
  });
  return NextResponse.json(product, { status: 201 });
}
