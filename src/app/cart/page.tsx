import CheckoutButton from '@/components/CheckoutButton';
import { formatMoney } from '@/lib/currency';
import Link from 'next/link';

// Mock cart data for demo
const mockCartItems = [
  {
    id: '1',
    product: {
      title: 'iPhone 15 Pro',
      slug: 'iphone-15-pro',
      brand: { name: 'Apple' },
      media: [{ url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=150&h=150&fit=crop' }]
    },
    variant: {
      id: 'v1',
      price: 999.99,
      sku: 'IPHONE15P-256-TIT'
    },
    quantity: 1,
    unitPrice: 999.99
  },
  {
    id: '2',
    product: {
      title: 'AirPods Pro 2',
      slug: 'airpods-pro-2',
      brand: { name: 'Apple' },
      media: [{ url: 'https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=150&h=150&fit=crop' }]
    },
    variant: {
      id: 'v15',
      price: 249.99,
      sku: 'AIRPODS-PRO2-WHT'
    },
    quantity: 1,
    unitPrice: 249.99
  }
];

export default function CartPage() {
  const items = mockCartItems;
  const subtotal = items.reduce((sum, i) => sum + i.unitPrice * i.quantity, 0);
  const shipping = subtotal > 50 ? 0 : 9.99;
  const tax = subtotal * 0.08; // 8% tax
  const total = subtotal + shipping + tax;

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '1rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>🛒 Shopping Cart</h1>
        <nav style={{ fontSize: '0.9rem', color: '#666' }}>
          <Link href="/" style={{ color: '#007bff', textDecoration: 'none' }}>Home</Link>
          {' > '}
          <span>Cart</span>
        </nav>
      </div>

      {items.length === 0 ? (
        // Empty Cart
        <div style={{
          textAlign: 'center',
          padding: '4rem 2rem',
          backgroundColor: '#f8f9fa',
          borderRadius: '12px'
        }}>
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🛒</div>
          <h2 style={{ marginBottom: '1rem' }}>Your cart is empty</h2>
          <p style={{ color: '#666', marginBottom: '2rem', fontSize: '1.1rem' }}>
            Looks like you haven't added any items to your cart yet.
          </p>
          <Link
            href="/products"
            style={{
              display: 'inline-block',
              padding: '12px 24px',
              backgroundColor: '#007bff',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: 'bold',
              fontSize: '1.1rem'
            }}
          >
            Continue Shopping
          </Link>
        </div>
      ) : (
        // Cart with Items
        <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '2rem' }}>
          {/* Cart Items */}
          <div>
            <div style={{
              backgroundColor: '#ffffff',
              border: '1px solid #e1e5e9',
              borderRadius: '12px',
              overflow: 'hidden'
            }}>
              <div style={{
                padding: '1.5rem',
                backgroundColor: '#f8f9fa',
                borderBottom: '1px solid #e1e5e9',
                fontWeight: 'bold'
              }}>
                Cart Items ({items.length})
              </div>

              {items.map((item, index) => (
                <div key={item.id}>
                  <div style={{
                    padding: '1.5rem',
                    display: 'grid',
                    gridTemplateColumns: '100px 1fr auto auto',
                    gap: '1rem',
                    alignItems: 'center'
                  }}>
                    {/* Product Image */}
                    <Link href={`/product/${item.product.slug}`}>
                      <img
                        src={item.product.media[0]?.url || 'https://via.placeholder.com/100x100?text=Product'}
                        alt={item.product.title}
                        style={{
                          width: '100px',
                          height: '100px',
                          objectFit: 'cover',
                          borderRadius: '8px',
                          cursor: 'pointer'
                        }}
                      />
                    </Link>

                    {/* Product Details */}
                    <div>
                      <Link
                        href={`/product/${item.product.slug}`}
                        style={{
                          textDecoration: 'none',
                          color: '#333',
                          fontWeight: 'bold',
                          fontSize: '1.1rem'
                        }}
                      >
                        {item.product.title}
                      </Link>
                      <div style={{ color: '#666', marginTop: '0.25rem' }}>
                        by {item.product.brand.name}
                      </div>
                      <div style={{ color: '#666', fontSize: '0.9rem', marginTop: '0.25rem' }}>
                        SKU: {item.variant.sku}
                      </div>
                      <div style={{ marginTop: '0.5rem' }}>
                        <button style={{
                          background: 'none',
                          border: 'none',
                          color: '#dc3545',
                          cursor: 'pointer',
                          fontSize: '0.9rem',
                          textDecoration: 'underline'
                        }}>
                          Remove
                        </button>
                      </div>
                    </div>

                    {/* Quantity */}
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ marginBottom: '0.5rem', fontSize: '0.9rem', color: '#666' }}>
                        Quantity
                      </div>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        border: '1px solid #ddd',
                        borderRadius: '6px',
                        overflow: 'hidden'
                      }}>
                        <button style={{
                          padding: '0.5rem',
                          border: 'none',
                          backgroundColor: '#f8f9fa',
                          cursor: 'pointer'
                        }}>
                          -
                        </button>
                        <span style={{
                          padding: '0.5rem 1rem',
                          minWidth: '50px',
                          textAlign: 'center'
                        }}>
                          {item.quantity}
                        </span>
                        <button style={{
                          padding: '0.5rem',
                          border: 'none',
                          backgroundColor: '#f8f9fa',
                          cursor: 'pointer'
                        }}>
                          +
                        </button>
                      </div>
                    </div>

                    {/* Price */}
                    <div style={{ textAlign: 'right' }}>
                      <div style={{
                        fontWeight: 'bold',
                        fontSize: '1.2rem',
                        color: '#007bff'
                      }}>
                        {formatMoney(item.unitPrice * item.quantity)}
                      </div>
                      <div style={{
                        fontSize: '0.9rem',
                        color: '#666'
                      }}>
                        {formatMoney(item.unitPrice)} each
                      </div>
                    </div>
                  </div>

                  {index < items.length - 1 && (
                    <div style={{ borderBottom: '1px solid #e1e5e9' }} />
                  )}
                </div>
              ))}
            </div>

            {/* Continue Shopping */}
            <div style={{ marginTop: '1rem' }}>
              <Link
                href="/products"
                style={{
                  color: '#007bff',
                  textDecoration: 'none',
                  fontWeight: 'bold'
                }}
              >
                ← Continue Shopping
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div>
            <div style={{
              backgroundColor: '#ffffff',
              border: '1px solid #e1e5e9',
              borderRadius: '12px',
              padding: '1.5rem',
              position: 'sticky',
              top: '1rem'
            }}>
              <h3 style={{ marginBottom: '1.5rem' }}>Order Summary</h3>

              <div style={{ marginBottom: '1rem' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '0.5rem'
                }}>
                  <span>Subtotal ({items.length} items)</span>
                  <span>{formatMoney(subtotal)}</span>
                </div>

                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '0.5rem'
                }}>
                  <span>Shipping</span>
                  <span style={{ color: shipping === 0 ? '#28a745' : '#333' }}>
                    {shipping === 0 ? 'FREE' : formatMoney(shipping)}
                  </span>
                </div>

                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '0.5rem'
                }}>
                  <span>Tax</span>
                  <span>{formatMoney(tax)}</span>
                </div>

                {shipping > 0 && (
                  <div style={{
                    fontSize: '0.9rem',
                    color: '#666',
                    marginTop: '0.5rem',
                    padding: '0.5rem',
                    backgroundColor: '#fff3cd',
                    borderRadius: '6px'
                  }}>
                    💡 Add {formatMoney(50 - subtotal)} more for free shipping!
                  </div>
                )}
              </div>

              <div style={{
                borderTop: '1px solid #e1e5e9',
                paddingTop: '1rem',
                marginBottom: '1.5rem'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  fontSize: '1.2rem',
                  fontWeight: 'bold'
                }}>
                  <span>Total</span>
                  <span style={{ color: '#007bff' }}>{formatMoney(total)}</span>
                </div>
              </div>

              <CheckoutButton />

              <div style={{
                marginTop: '1rem',
                fontSize: '0.9rem',
                color: '#666',
                textAlign: 'center'
              }}>
                🔒 Secure checkout with SSL encryption
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
