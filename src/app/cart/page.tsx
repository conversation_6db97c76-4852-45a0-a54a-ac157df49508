import { cookies } from 'next/headers';
import { prisma } from '@/lib/prisma';
import CheckoutButton from '@/components/CheckoutButton';
import { formatMoney } from '@/lib/currency';

export default async function CartPage() {
  const jar = cookies();
  const sessionId = jar.get('cartId')?.value;
  const cart = sessionId ? await prisma.cart.findUnique({ where: { sessionId }, include: { items: { include: { variant: { include: { product: true } } } } } }) : null;
  const items = cart?.items ?? [];
  const total = items.reduce((sum, i) => sum + Number(i.unitPrice) * i.quantity, 0);
  return (
    <div style={{ padding: '1rem' }}>
      <h1>Your Cart</h1>
      {items.length === 0 && <p>Cart is empty.</p>}
      {items.length > 0 && (
        <table>
          <thead><tr><th>Item</th><th>Qty</th><th>Price</th><th>Subtotal</th></tr></thead>
          <tbody>
            {items.map((i) => (
              <tr key={i.id}>
                <td>{i.variant.product.title}</td>
                <td>{i.quantity}</td>
                <td>{formatMoney(Number(i.unitPrice))}</td>
                <td>{formatMoney(Number(i.unitPrice) * i.quantity)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
      <h2>Total: {formatMoney(total)}</h2>
      {items.length > 0 && <CheckoutButton />}
    </div>
  );
}
