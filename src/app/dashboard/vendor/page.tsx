import { prisma } from '@/lib/prisma';

export default async function VendorDashboard() {
  const products = await prisma.product.findMany({ take: 10, orderBy: { updatedAt: 'desc' } });
  const orders = await prisma.order.findMany({ take: 10, orderBy: { createdAt: 'desc' } });
  return (
    <div style={{ padding: '1rem' }}>
      <h1>Vendor Dashboard</h1>
      <h2>Your Products</h2>
      <ul>{products.map(p => <li key={p.id}>{p.title}</li>)}</ul>
      <h2>Recent Orders</h2>
      <ul>{orders.map(o => <li key={o.id}>{o.id} — {o.status}</li>)}</ul>
    </div>
  );
}
