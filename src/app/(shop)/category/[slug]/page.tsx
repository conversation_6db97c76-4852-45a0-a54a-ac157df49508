import { prisma } from '@/lib/prisma';
import ProductCard from '@/components/ProductCard';
import FilterSidebar from '@/components/FilterSidebar';

export default async function CategoryPage({ params, searchParams }: any) {
  const category = await prisma.category.findUnique({ where: { slug: params.slug } });
  if (!category) return <div>Category not found</div>;
  const brands = (await prisma.brand.findMany({ select: { name: true } })).map(b => b.name);
  const where: any = { status: 'PUBLISHED', categoryId: category.id };
  if (searchParams.brand) where.brand = { name: { equals: searchParams.brand, mode: 'insensitive' } };
  const products = await prisma.product.findMany({ where, include: { media: true, brand: true, variants: true }, take: 48 });
  return (
    <div style={{ display: 'grid', gridTemplateColumns: '240px 1fr', gap: '1rem', padding: '1rem' }}>
      <FilterSidebar brands={brands} />
      <div>
        <h1>{category.name}</h1>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4,1fr)', gap: '1rem' }}>
          {products.map(p => <ProductCard key={p.id} product={p} />)}
        </div>
      </div>
    </div>
  );
}
