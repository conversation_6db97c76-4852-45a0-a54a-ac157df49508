import ProductCard from '@/components/ProductCard';
import FilterSidebar from '@/components/FilterSidebar';
import Link from 'next/link';

// Mock data - same as homepage but organized by category
const mockCategories = {
  'smartphones': {
    name: 'Smartphones',
    description: 'Latest smartphones from top brands including iPhone, Samsung Galaxy, Google Pixel, and more.',
    icon: '📱'
  },
  'laptops-computers': {
    name: 'Laptops & Computers',
    description: 'High-performance laptops, ultrabooks, and desktop computers for work and gaming.',
    icon: '💻'
  },
  'audio-headphones': {
    name: 'Audio & Headphones',
    description: 'Premium headphones, earbuds, and audio equipment from Sony, Bose, Apple, and more.',
    icon: '🎧'
  },
  'gaming-consoles': {
    name: 'Gaming & Consoles',
    description: 'Gaming consoles, handheld devices, and gaming accessories.',
    icon: '🎮'
  },
  'tablets-ereaders': {
    name: 'Tablets & E-readers',
    description: 'iPads, Android tablets, e-readers, and digital reading devices.',
    icon: '📱'
  },
  'wearables-fitness': {
    name: 'Wearables & Fitness',
    description: 'Smartwatches, fitness trackers, and health monitoring devices.',
    icon: '⌚'
  },
  'smart-home-iot': {
    name: 'Smart Home & IoT',
    description: 'Smart speakers, home automation, security cameras, and IoT devices.',
    icon: '🏠'
  },
  'cameras-photography': {
    name: 'Cameras & Photography',
    description: 'Professional cameras, lenses, action cameras, and photography equipment.',
    icon: '📷'
  },
  'tv-home-theater': {
    name: 'TV & Home Theater',
    description: '4K TVs, OLED displays, soundbars, and home entertainment systems.',
    icon: '📺'
  },
  'computer-components': {
    name: 'Computer Components',
    description: 'Graphics cards, processors, motherboards, and PC building components.',
    icon: '🔧'
  },
  'monitors-displays': {
    name: 'Monitors & Displays',
    description: '4K monitors, gaming displays, professional monitors, and external screens.',
    icon: '🖥️'
  },
  'keyboards-mice': {
    name: 'Keyboards & Mice',
    description: 'Mechanical keyboards, gaming mice, and computer peripherals.',
    icon: '⌨️'
  },
  'storage-memory': {
    name: 'Storage & Memory',
    description: 'SSDs, hard drives, RAM, and data storage solutions.',
    icon: '💾'
  },
  'networking-wifi': {
    name: 'Networking & WiFi',
    description: 'Routers, WiFi extenders, network switches, and connectivity solutions.',
    icon: '📡'
  },
  'mobile-accessories': {
    name: 'Mobile Accessories',
    description: 'Phone cases, screen protectors, chargers, and mobile accessories.',
    icon: '📱'
  },
  'power-charging': {
    name: 'Power & Charging',
    description: 'Power banks, wireless chargers, cables, and charging solutions.',
    icon: '🔋'
  },
  'drones-rc': {
    name: 'Drones & RC',
    description: 'Consumer drones, professional UAVs, and remote control devices.',
    icon: '🚁'
  },
  'vr-ar': {
    name: 'VR & AR',
    description: 'Virtual reality headsets, augmented reality devices, and immersive technology.',
    icon: '🥽'
  },
  'security-surveillance': {
    name: 'Security & Surveillance',
    description: 'Security cameras, alarm systems, and surveillance equipment.',
    icon: '🔒'
  },
  'office-electronics': {
    name: 'Office Electronics',
    description: 'Printers, scanners, projectors, and office technology.',
    icon: '🖨️'
  }
};

const mockProducts = [
  // SMARTPHONES
  { id: '1', title: 'iPhone 15 Pro Max', slug: 'iphone-15-pro-max', brand: { name: 'Apple' }, variants: [{ price: 1199.99 }], media: [{ url: 'https://images.unsplash.com/photo-*************-74b7b21085ab?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '2', title: 'iPhone 15 Pro', slug: 'iphone-15-pro', brand: { name: 'Apple' }, variants: [{ price: 999.99 }], media: [{ url: 'https://images.unsplash.com/photo-*************-74b7b21085ab?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '3', title: 'Samsung Galaxy S24 Ultra', slug: 'galaxy-s24-ultra', brand: { name: 'Samsung' }, variants: [{ price: 1299.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '4', title: 'Samsung Galaxy S24', slug: 'galaxy-s24', brand: { name: 'Samsung' }, variants: [{ price: 899.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '5', title: 'Google Pixel 8 Pro', slug: 'google-pixel-8-pro', brand: { name: 'Google' }, variants: [{ price: 999.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '6', title: 'OnePlus 12', slug: 'oneplus-12', brand: { name: 'OnePlus' }, variants: [{ price: 799.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '7', title: 'Xiaomi 14 Ultra', slug: 'xiaomi-14-ultra', brand: { name: 'Xiaomi' }, variants: [{ price: 699.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },

  // LAPTOPS
  { id: '8', title: 'MacBook Pro 16" M3 Max', slug: 'macbook-pro-16-m3-max', brand: { name: 'Apple' }, variants: [{ price: 3499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop' }], category: 'laptops-computers' },
  { id: '9', title: 'MacBook Air M3', slug: 'macbook-air-m3', brand: { name: 'Apple' }, variants: [{ price: 1299.99 }], media: [{ url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop' }], category: 'laptops-computers' },
  { id: '10', title: 'Dell XPS 13 Plus', slug: 'dell-xps-13-plus', brand: { name: 'Dell' }, variants: [{ price: 1399.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops-computers' },
  { id: '11', title: 'ThinkPad X1 Carbon', slug: 'thinkpad-x1-carbon', brand: { name: 'Lenovo' }, variants: [{ price: 1599.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops-computers' },
  { id: '12', title: 'Surface Laptop 5', slug: 'surface-laptop-5', brand: { name: 'Microsoft' }, variants: [{ price: 1299.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops-computers' },
  { id: '13', title: 'ASUS ROG Zephyrus G14', slug: 'asus-rog-zephyrus-g14', brand: { name: 'ASUS' }, variants: [{ price: 1899.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops-computers' },
  { id: '14', title: 'HP Spectre x360', slug: 'hp-spectre-x360', brand: { name: 'HP' }, variants: [{ price: 1199.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops-computers' },

  // AUDIO
  { id: '15', title: 'AirPods Pro 2', slug: 'airpods-pro-2', brand: { name: 'Apple' }, variants: [{ price: 249.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400&h=300&fit=crop' }], category: 'audio-headphones' },
  { id: '16', title: 'Sony WH-1000XM5', slug: 'sony-wh-1000xm5', brand: { name: 'Sony' }, variants: [{ price: 399.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio-headphones' },
  { id: '17', title: 'Bose QuietComfort Ultra', slug: 'bose-quietcomfort-ultra', brand: { name: 'Bose' }, variants: [{ price: 429.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio-headphones' },
  { id: '18', title: 'Sennheiser HD 800S', slug: 'sennheiser-hd-800s', brand: { name: 'Sennheiser' }, variants: [{ price: 1699.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio-headphones' },
  { id: '19', title: 'Audio-Technica ATH-M50x', slug: 'audio-technica-ath-m50x', brand: { name: 'Audio-Technica' }, variants: [{ price: 149.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio-headphones' },
  { id: '20', title: 'Beats Studio Pro', slug: 'beats-studio-pro', brand: { name: 'Beats' }, variants: [{ price: 349.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio-headphones' },

  // Add more products for other categories...
  { id: '21', title: 'PlayStation 5', slug: 'playstation-5', brand: { name: 'Sony' }, variants: [{ price: 499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }], category: 'gaming-consoles' },
  { id: '22', title: 'Xbox Series X', slug: 'xbox-series-x', brand: { name: 'Microsoft' }, variants: [{ price: 499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }], category: 'gaming-consoles' },
  { id: '23', title: 'Nintendo Switch OLED', slug: 'nintendo-switch-oled', brand: { name: 'Nintendo' }, variants: [{ price: 349.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }], category: 'gaming-consoles' },
  { id: '24', title: 'Steam Deck OLED', slug: 'steam-deck-oled', brand: { name: 'Valve' }, variants: [{ price: 549.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }], category: 'gaming-consoles' },
];

export default function CategoryPage({ params, searchParams }: any) {
  const categorySlug = params.slug;
  const category = mockCategories[categorySlug as keyof typeof mockCategories];

  if (!category) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <h1>Category Not Found</h1>
        <p>The category "{categorySlug}" doesn't exist.</p>
        <Link href="/" style={{ color: '#007bff', textDecoration: 'none' }}>← Back to Home</Link>
      </div>
    );
  }

  // Filter products by category
  const categoryProducts = mockProducts.filter(p => p.category === categorySlug);

  // Get unique brands for this category
  const brands = [...new Set(categoryProducts.map(p => p.brand.name))];

  // Apply brand filter if specified
  const filteredProducts = searchParams.brand
    ? categoryProducts.filter(p => p.brand.name.toLowerCase() === searchParams.brand.toLowerCase())
    : categoryProducts;

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto', padding: '1rem' }}>
      {/* Category Header */}
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '12px',
        marginBottom: '2rem',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>{category.icon}</div>
        <h1 style={{ fontSize: '2.5rem', margin: '0 0 1rem 0' }}>{category.name}</h1>
        <p style={{ fontSize: '1.2rem', opacity: 0.9, maxWidth: '600px', margin: '0 auto' }}>
          {category.description}
        </p>
        <div style={{ marginTop: '1rem', fontSize: '1.1rem' }}>
          <strong>{filteredProducts.length}</strong> products available
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '280px 1fr', gap: '2rem' }}>
        {/* Sidebar */}
        <div>
          <FilterSidebar brands={brands} />

          {/* Category Navigation */}
          <div style={{
            marginTop: '2rem',
            padding: '1.5rem',
            backgroundColor: '#f8f9fa',
            borderRadius: '12px'
          }}>
            <h3 style={{ marginBottom: '1rem' }}>Browse Other Categories</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              {Object.entries(mockCategories).slice(0, 6).map(([slug, cat]) => (
                <Link
                  key={slug}
                  href={`/category/${slug}`}
                  style={{
                    textDecoration: 'none',
                    color: slug === categorySlug ? '#007bff' : '#666',
                    padding: '0.5rem',
                    borderRadius: '6px',
                    backgroundColor: slug === categorySlug ? '#e3f2fd' : 'transparent',
                    fontWeight: slug === categorySlug ? 'bold' : 'normal'
                  }}
                >
                  {cat.icon} {cat.name}
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div>
          {filteredProducts.length > 0 ? (
            <>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1.5rem',
                padding: '1rem',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
              }}>
                <div>
                  <strong>{filteredProducts.length}</strong> products found
                  {searchParams.brand && (
                    <span style={{ color: '#666', marginLeft: '0.5rem' }}>
                      filtered by "{searchParams.brand}"
                    </span>
                  )}
                </div>
                <div style={{ fontSize: '0.9rem', color: '#666' }}>
                  Sorted by: Featured
                </div>
              </div>

              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
                gap: '1.5rem'
              }}>
                {filteredProducts.map(p => (
                  <ProductCard key={p.id} product={p} />
                ))}
              </div>
            </>
          ) : (
            <div style={{
              textAlign: 'center',
              padding: '3rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '12px'
            }}>
              <h3>No products found</h3>
              <p style={{ color: '#666', marginBottom: '1rem' }}>
                No products match your current filters.
              </p>
              <Link
                href={`/category/${categorySlug}`}
                style={{
                  color: '#007bff',
                  textDecoration: 'none',
                  fontWeight: 'bold'
                }}
              >
                Clear filters and view all {category.name.toLowerCase()}
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
