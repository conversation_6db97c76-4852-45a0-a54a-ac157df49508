import { prisma } from '@/lib/prisma';

export default async function AdminPage() {
  const pendingReviews = await prisma.review.findMany({ where: { status: 'PENDING' }, take: 20, include: { product: true, user: true } });
  return (
    <div style={{ padding: '1rem' }}>
      <h1>Admin</h1>
      <h2>Pending Reviews</h2>
      <ul>
        {pendingReviews.map(r => (
          <li key={r.id}>{r.product.title}: {r.rating}/5 — {r.body.slice(0,80)}...</li>
        ))}
      </ul>
    </div>
  );
}
