import Link from 'next/link';

export default function AdminPage() {
  const adminStats = {
    totalProducts: 70,
    pendingReviews: 12,
    lowStock: 5,
    recentOrders: 28
  };

  const recentActivity = [
    { id: 1, action: 'Product Added', item: 'iPhone 15 Pro Max', time: '2 hours ago', status: 'success' },
    { id: 2, action: 'Price Updated', item: 'MacBook Air M3', time: '4 hours ago', status: 'info' },
    { id: 3, action: 'Stock Alert', item: 'AirPods Pro 2', time: '6 hours ago', status: 'warning' },
    { id: 4, action: 'Review Pending', item: 'Samsung Galaxy S24', time: '8 hours ago', status: 'pending' },
  ];

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto', padding: '1rem' }}>
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '12px',
        marginBottom: '2rem'
      }}>
        <h1 style={{ fontSize: '2.5rem', margin: '0 0 0.5rem 0' }}>
          🛠️ ElectroHub Admin Portal
        </h1>
        <p style={{ fontSize: '1.2rem', opacity: 0.9 }}>
          Manage products, pricing, and inventory with AI-powered tools
        </p>
      </div>

      {/* Quick Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#007bff', marginBottom: '0.5rem' }}>
            {adminStats.totalProducts}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Total Products</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Active in catalog</div>
        </div>

        <div style={{
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#ffc107', marginBottom: '0.5rem' }}>
            {adminStats.pendingReviews}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Pending Reviews</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Need approval</div>
        </div>

        <div style={{
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#dc3545', marginBottom: '0.5rem' }}>
            {adminStats.lowStock}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Low Stock</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Need restocking</div>
        </div>

        <div style={{
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#28a745', marginBottom: '0.5rem' }}>
            {adminStats.recentOrders}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Recent Orders</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Last 24 hours</div>
        </div>
      </div>

      {/* Main Actions */}
      <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '2rem' }}>
        {/* Quick Actions */}
        <div style={{
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '2rem'
        }}>
          <h2 style={{ marginBottom: '1.5rem' }}>🚀 Quick Actions</h2>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
            <Link
              href="/admin/products/add"
              style={{
                display: 'block',
                padding: '1.5rem',
                backgroundColor: '#007bff',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '8px',
                textAlign: 'center',
                fontWeight: 'bold',
                transition: 'transform 0.2s'
              }}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>➕</div>
              Add New Product
            </Link>

            <Link
              href="/admin/products"
              style={{
                display: 'block',
                padding: '1.5rem',
                backgroundColor: '#28a745',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '8px',
                textAlign: 'center',
                fontWeight: 'bold'
              }}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📦</div>
              Manage Products
            </Link>

            <Link
              href="/admin/price-match"
              style={{
                display: 'block',
                padding: '1.5rem',
                backgroundColor: '#ffc107',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '8px',
                textAlign: 'center',
                fontWeight: 'bold'
              }}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>💰</div>
              Price Matching
            </Link>

            <Link
              href="/admin/bulk-import"
              style={{
                display: 'block',
                padding: '1.5rem',
                backgroundColor: '#6f42c1',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '8px',
                textAlign: 'center',
                fontWeight: 'bold'
              }}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📊</div>
              Bulk Import
            </Link>
          </div>
        </div>

        {/* Recent Activity */}
        <div style={{
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '2rem'
        }}>
          <h3 style={{ marginBottom: '1.5rem' }}>📈 Recent Activity</h3>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {recentActivity.map((activity) => (
              <div key={activity.id} style={{
                padding: '1rem',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                borderLeft: `4px solid ${
                  activity.status === 'success' ? '#28a745' :
                  activity.status === 'info' ? '#007bff' :
                  activity.status === 'warning' ? '#ffc107' : '#6c757d'
                }`
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                  {activity.action}
                </div>
                <div style={{ color: '#666', fontSize: '0.9rem', marginBottom: '0.25rem' }}>
                  {activity.item}
                </div>
                <div style={{ color: '#999', fontSize: '0.8rem' }}>
                  {activity.time}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* AI Features Preview */}
      <div style={{
        marginTop: '2rem',
        backgroundColor: '#ffffff',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '2rem'
      }}>
        <h2 style={{ marginBottom: '1.5rem' }}>🤖 AI-Powered Features</h2>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem' }}>
          <div style={{
            padding: '1.5rem',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid #e1e5e9'
          }}>
            <h4 style={{ color: '#007bff', marginBottom: '0.5rem' }}>🔍 Smart Product Detection</h4>
            <p style={{ color: '#666', fontSize: '0.9rem', margin: 0 }}>
              AI automatically identifies products from names/descriptions and fetches specifications, images, and competitive pricing data.
            </p>
          </div>

          <div style={{
            padding: '1.5rem',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid #e1e5e9'
          }}>
            <h4 style={{ color: '#28a745', marginBottom: '0.5rem' }}>💰 Real-time Price Matching</h4>
            <p style={{ color: '#666', fontSize: '0.9rem', margin: 0 }}>
              Continuously monitors competitor prices and suggests optimal pricing strategies to stay competitive.
            </p>
          </div>

          <div style={{
            padding: '1.5rem',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid #e1e5e9'
          }}>
            <h4 style={{ color: '#ffc107', marginBottom: '0.5rem' }}>📸 Automatic Image Sourcing</h4>
            <p style={{ color: '#666', fontSize: '0.9rem', margin: 0 }}>
              Finds and curates high-quality product images from multiple sources, ensuring consistent visual presentation.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
