import Link from 'next/link';

// Mock product data for admin management
const adminProducts = [
  {
    id: '1',
    title: 'iPhone 15 Pro Max',
    brand: 'Apple',
    category: 'Smartphones',
    price: 1199.99,
    stock: 45,
    status: 'published',
    lastUpdated: '2024-02-15',
    sales: 127,
    revenue: 152399.73
  },
  {
    id: '2',
    title: 'MacBook Air M3',
    brand: 'Apple',
    category: 'Laptops',
    price: 1299.99,
    stock: 23,
    status: 'published',
    lastUpdated: '2024-02-14',
    sales: 89,
    revenue: 115699.11
  },
  {
    id: '3',
    title: 'Sony WH-1000XM5',
    brand: 'Sony',
    category: 'Audio',
    price: 399.99,
    stock: 67,
    status: 'published',
    lastUpdated: '2024-02-13',
    sales: 203,
    revenue: 81197.97
  },
  {
    id: '4',
    title: 'Samsung Galaxy S24 Ultra',
    brand: 'Samsung',
    category: 'Smartphones',
    price: 1299.99,
    stock: 12,
    status: 'low_stock',
    lastUpdated: '2024-02-12',
    sales: 156,
    revenue: 202798.44
  },
  {
    id: '5',
    title: 'AirPods Pro 2',
    brand: 'Apple',
    category: 'Audio',
    price: 249.99,
    stock: 89,
    status: 'published',
    lastUpdated: '2024-02-11',
    sales: 312,
    revenue: 77997.88
  },
  {
    id: '6',
    title: 'Dell XPS 13 Plus',
    brand: 'Dell',
    category: 'Laptops',
    price: 1399.99,
    stock: 0,
    status: 'out_of_stock',
    lastUpdated: '2024-02-10',
    sales: 67,
    revenue: 93799.33
  }
];

export default function AdminProductsPage() {
  const totalProducts = adminProducts.length;
  const publishedProducts = adminProducts.filter(p => p.status === 'published').length;
  const lowStockProducts = adminProducts.filter(p => p.status === 'low_stock').length;
  const outOfStockProducts = adminProducts.filter(p => p.status === 'out_of_stock').length;
  const totalRevenue = adminProducts.reduce((sum, p) => sum + p.revenue, 0);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return '#28a745';
      case 'low_stock': return '#ffc107';
      case 'out_of_stock': return '#dc3545';
      case 'draft': return '#6c757d';
      default: return '#6c757d';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published': return '✅';
      case 'low_stock': return '⚠️';
      case 'out_of_stock': return '❌';
      case 'draft': return '📝';
      default: return '❓';
    }
  };

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto', padding: '1rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <nav style={{ fontSize: '0.9rem', color: '#666', marginBottom: '1rem' }}>
          <Link href="/admin" style={{ color: '#007bff', textDecoration: 'none' }}>Admin</Link>
          {' > '}
          <span>Products</span>
        </nav>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '2.5rem', margin: '0 0 0.5rem 0' }}>
              📦 Product Management
            </h1>
            <p style={{ color: '#666', fontSize: '1.1rem', margin: 0 }}>
              Manage your product catalog, inventory, and pricing
            </p>
          </div>
          <Link 
            href="/admin/products/add"
            style={{
              padding: '1rem 2rem',
              backgroundColor: '#007bff',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            ➕ Add New Product
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#007bff', marginBottom: '0.5rem' }}>
            {totalProducts}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Total Products</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>In catalog</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#28a745', marginBottom: '0.5rem' }}>
            {publishedProducts}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Published</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Live products</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#ffc107', marginBottom: '0.5rem' }}>
            {lowStockProducts}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Low Stock</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Need restocking</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#dc3545', marginBottom: '0.5rem' }}>
            {outOfStockProducts}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Out of Stock</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Unavailable</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', color: '#28a745', marginBottom: '0.5rem' }}>
            ${totalRevenue.toLocaleString()}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Total Revenue</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>All time</div>
        </div>
      </div>

      {/* Products Table */}
      <div style={{ 
        backgroundColor: '#ffffff',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        overflow: 'hidden'
      }}>
        <div style={{ 
          padding: '1.5rem',
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #e1e5e9',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <h2 style={{ margin: 0 }}>📊 Product Inventory</h2>
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <select style={{
              padding: '0.5rem',
              border: '1px solid #e1e5e9',
              borderRadius: '6px',
              backgroundColor: 'white'
            }}>
              <option>All Categories</option>
              <option>Smartphones</option>
              <option>Laptops</option>
              <option>Audio</option>
            </select>
            <select style={{
              padding: '0.5rem',
              border: '1px solid #e1e5e9',
              borderRadius: '6px',
              backgroundColor: 'white'
            }}>
              <option>All Status</option>
              <option>Published</option>
              <option>Low Stock</option>
              <option>Out of Stock</option>
            </select>
          </div>
        </div>

        <div style={{ overflowX: 'auto' }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #e1e5e9' }}>Product</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Price</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Stock</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Status</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Sales</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Revenue</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {adminProducts.map((product) => (
                <tr key={product.id} style={{ borderBottom: '1px solid #e1e5e9' }}>
                  <td style={{ padding: '1rem' }}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                        {product.title}
                      </div>
                      <div style={{ fontSize: '0.9rem', color: '#666' }}>
                        {product.brand} • {product.category}
                      </div>
                      <div style={{ fontSize: '0.8rem', color: '#999' }}>
                        Updated {product.lastUpdated}
                      </div>
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ fontSize: '1.1rem', fontWeight: 'bold' }}>
                      ${product.price.toFixed(2)}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ 
                      fontSize: '1.1rem', 
                      fontWeight: 'bold',
                      color: product.stock === 0 ? '#dc3545' : 
                             product.stock < 20 ? '#ffc107' : '#28a745'
                    }}>
                      {product.stock}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ 
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      backgroundColor: getStatusColor(product.status) + '20',
                      color: getStatusColor(product.status),
                      borderRadius: '20px',
                      fontSize: '0.9rem',
                      fontWeight: 'bold'
                    }}>
                      {getStatusIcon(product.status)}
                      {product.status.replace('_', ' ').toUpperCase()}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ fontSize: '1.1rem', fontWeight: 'bold' }}>
                      {product.sales}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ fontSize: '1.1rem', fontWeight: 'bold', color: '#28a745' }}>
                      ${product.revenue.toLocaleString()}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center' }}>
                      <button style={{
                        padding: '0.5rem',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        fontSize: '0.8rem'
                      }}>
                        ✏️
                      </button>
                      <button style={{
                        padding: '0.5rem',
                        backgroundColor: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        fontSize: '0.8rem'
                      }}>
                        👁️
                      </button>
                      <button style={{
                        padding: '0.5rem',
                        backgroundColor: '#ffc107',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        fontSize: '0.8rem'
                      }}>
                        📊
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <div style={{ 
        marginTop: '2rem',
        backgroundColor: '#ffffff',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '2rem'
      }}>
        <h3 style={{ marginBottom: '1.5rem' }}>⚡ Quick Actions</h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
          <Link 
            href="/admin/bulk-import"
            style={{
              display: 'block',
              padding: '1.5rem',
              backgroundColor: '#6f42c1',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              textAlign: 'center',
              fontWeight: 'bold'
            }}
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📊</div>
            Bulk Import Products
          </Link>

          <Link 
            href="/admin/price-match"
            style={{
              display: 'block',
              padding: '1.5rem',
              backgroundColor: '#ffc107',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              textAlign: 'center',
              fontWeight: 'bold'
            }}
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>💰</div>
            Update Pricing
          </Link>

          <button style={{
            padding: '1.5rem',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            textAlign: 'center',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📦</div>
            Restock Alerts
          </button>

          <button style={{
            padding: '1.5rem',
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            textAlign: 'center',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📈</div>
            Analytics Report
          </button>
        </div>
      </div>
    </div>
  );
}
