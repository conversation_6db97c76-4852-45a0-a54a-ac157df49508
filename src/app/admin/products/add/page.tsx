'use client';

import { useState } from 'react';
import Link from 'next/link';

interface ProductData {
  name: string;
  brand: string;
  category: string;
  description: string;
  price: number;
  specifications: Record<string, string>;
  images: string[];
  competitors: Array<{
    store: string;
    price: number;
    url: string;
  }>;
}

export default function AddProductPage() {
  const [productInput, setProductInput] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [productData, setProductData] = useState<ProductData | null>(null);
  const [step, setStep] = useState<'input' | 'review' | 'success'>('input');

  // Mock AI analysis function
  const analyzeProduct = async (input: string) => {
    setIsAnalyzing(true);
    
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Mock AI-generated product data
    const mockData: ProductData = {
      name: input.includes('iPhone') ? 'iPhone 15 Pro Max' : 
            input.includes('MacBook') ? 'MacBook Air M3' :
            input.includes('AirPods') ? 'AirPods Pro 2' :
            input.includes('Samsung') ? 'Samsung Galaxy S24 Ultra' :
            'Generic Product',
      brand: input.includes('iPhone') || input.includes('MacBook') || input.includes('AirPods') ? 'Apple' :
             input.includes('Samsung') ? 'Samsung' :
             input.includes('Sony') ? 'Sony' : 'Unknown Brand',
      category: input.toLowerCase().includes('phone') ? 'smartphones' :
                input.toLowerCase().includes('laptop') || input.toLowerCase().includes('macbook') ? 'laptops' :
                input.toLowerCase().includes('airpods') || input.toLowerCase().includes('headphone') ? 'audio' :
                'electronics',
      description: `Premium ${input} with advanced features and cutting-edge technology. Perfect for professionals and enthusiasts alike.`,
      price: Math.floor(Math.random() * 2000) + 299,
      specifications: {
        'Display': '6.7-inch Super Retina XDR',
        'Processor': 'A17 Pro chip',
        'Storage': '256GB, 512GB, 1TB',
        'Camera': '48MP Pro camera system',
        'Battery': 'All-day battery life',
        'Operating System': 'iOS 17',
        'Connectivity': '5G, Wi-Fi 6E, Bluetooth 5.3',
        'Dimensions': '159.9 × 76.7 × 8.25 mm'
      },
      images: [
        'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=400&fit=crop',
        'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=400&fit=crop&sat=-50',
        'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=400&fit=crop&hue=180'
      ],
      competitors: [
        { store: 'Amazon', price: 1199.99, url: 'https://amazon.com' },
        { store: 'Best Buy', price: 1199.00, url: 'https://bestbuy.com' },
        { store: 'Apple Store', price: 1199.99, url: 'https://apple.com' },
        { store: 'B&H Photo', price: 1189.99, url: 'https://bhphotovideo.com' }
      ]
    };
    
    setProductData(mockData);
    setIsAnalyzing(false);
    setStep('review');
  };

  const handleSubmit = async () => {
    // Simulate saving to database
    await new Promise(resolve => setTimeout(resolve, 1000));
    setStep('success');
  };

  if (step === 'success') {
    return (
      <div style={{ maxWidth: '800px', margin: '0 auto', padding: '2rem', textAlign: 'center' }}>
        <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>✅</div>
        <h1 style={{ color: '#28a745', marginBottom: '1rem' }}>Product Added Successfully!</h1>
        <p style={{ color: '#666', marginBottom: '2rem' }}>
          {productData?.name} has been added to your catalog with AI-optimized pricing and images.
        </p>
        <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
          <Link 
            href="/admin/products/add"
            onClick={() => {
              setStep('input');
              setProductInput('');
              setProductData(null);
            }}
            style={{
              padding: '12px 24px',
              backgroundColor: '#007bff',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: 'bold'
            }}
          >
            Add Another Product
          </Link>
          <Link 
            href="/admin"
            style={{
              padding: '12px 24px',
              backgroundColor: '#6c757d',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: 'bold'
            }}
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '1rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <nav style={{ fontSize: '0.9rem', color: '#666', marginBottom: '1rem' }}>
          <Link href="/admin" style={{ color: '#007bff', textDecoration: 'none' }}>Admin</Link>
          {' > '}
          <span>Add Product</span>
        </nav>
        <h1 style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>
          🤖 AI-Powered Product Addition
        </h1>
        <p style={{ color: '#666', fontSize: '1.1rem' }}>
          Simply describe or name the product - our AI will handle the rest!
        </p>
      </div>

      {step === 'input' && (
        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '2rem'
        }}>
          <h2 style={{ marginBottom: '1.5rem' }}>📝 Product Information</h2>
          
          <div style={{ marginBottom: '2rem' }}>
            <label style={{ 
              display: 'block', 
              fontWeight: 'bold', 
              marginBottom: '0.5rem',
              color: '#333'
            }}>
              Product Name or Description
            </label>
            <textarea
              value={productInput}
              onChange={(e) => setProductInput(e.target.value)}
              placeholder="e.g., iPhone 15 Pro Max 256GB Titanium, MacBook Air M3 13-inch, Sony WH-1000XM5 Headphones..."
              style={{
                width: '100%',
                minHeight: '120px',
                padding: '1rem',
                border: '2px solid #e1e5e9',
                borderRadius: '8px',
                fontSize: '1rem',
                resize: 'vertical'
              }}
            />
            <div style={{ fontSize: '0.9rem', color: '#666', marginTop: '0.5rem' }}>
              💡 The more specific you are, the better our AI can identify and price the product!
            </div>
          </div>

          <button
            onClick={() => analyzeProduct(productInput)}
            disabled={!productInput.trim() || isAnalyzing}
            style={{
              padding: '1rem 2rem',
              backgroundColor: isAnalyzing ? '#6c757d' : '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              cursor: isAnalyzing ? 'not-allowed' : 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            {isAnalyzing ? (
              <>
                <div style={{ 
                  width: '20px', 
                  height: '20px', 
                  border: '2px solid #ffffff',
                  borderTop: '2px solid transparent',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }} />
                Analyzing Product...
              </>
            ) : (
              <>
                🔍 Analyze with AI
              </>
            )}
          </button>

          {isAnalyzing && (
            <div style={{ 
              marginTop: '2rem',
              padding: '1.5rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              border: '1px solid #e1e5e9'
            }}>
              <h4 style={{ marginBottom: '1rem', color: '#007bff' }}>🤖 AI Processing...</h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                <div>✅ Identifying product specifications...</div>
                <div>🔍 Searching for competitive pricing...</div>
                <div>📸 Finding high-quality product images...</div>
                <div>💰 Calculating optimal pricing strategy...</div>
              </div>
            </div>
          )}
        </div>
      )}

      {step === 'review' && productData && (
        <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '2rem' }}>
          {/* Product Details */}
          <div style={{ 
            backgroundColor: '#ffffff',
            border: '1px solid #e1e5e9',
            borderRadius: '12px',
            padding: '2rem'
          }}>
            <h2 style={{ marginBottom: '1.5rem' }}>📋 Review Product Details</h2>
            
            <div style={{ marginBottom: '2rem' }}>
              <h3 style={{ color: '#007bff', marginBottom: '1rem' }}>{productData.name}</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', marginBottom: '1rem' }}>
                <div>
                  <strong>Brand:</strong> {productData.brand}
                </div>
                <div>
                  <strong>Category:</strong> {productData.category}
                </div>
              </div>
              <div style={{ marginBottom: '1rem' }}>
                <strong>Description:</strong>
                <p style={{ color: '#666', marginTop: '0.5rem' }}>{productData.description}</p>
              </div>
            </div>

            {/* Specifications */}
            <div style={{ marginBottom: '2rem' }}>
              <h4 style={{ marginBottom: '1rem' }}>🔧 Specifications</h4>
              <div style={{ display: 'grid', gap: '0.5rem' }}>
                {Object.entries(productData.specifications).map(([key, value]) => (
                  <div key={key} style={{ 
                    display: 'grid',
                    gridTemplateColumns: '150px 1fr',
                    padding: '0.5rem',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '6px'
                  }}>
                    <strong>{key}:</strong>
                    <span>{value}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Images */}
            <div style={{ marginBottom: '2rem' }}>
              <h4 style={{ marginBottom: '1rem' }}>📸 Product Images</h4>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem' }}>
                {productData.images.map((image, index) => (
                  <img 
                    key={index}
                    src={image} 
                    alt={`${productData.name} view ${index + 1}`}
                    style={{ 
                      width: '100%', 
                      height: '150px', 
                      objectFit: 'cover',
                      borderRadius: '8px',
                      border: '1px solid #e1e5e9'
                    }} 
                  />
                ))}
              </div>
            </div>

            <div style={{ display: 'flex', gap: '1rem' }}>
              <button
                onClick={handleSubmit}
                style={{
                  padding: '1rem 2rem',
                  backgroundColor: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}
              >
                ✅ Add Product to Catalog
              </button>
              <button
                onClick={() => setStep('input')}
                style={{
                  padding: '1rem 2rem',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  cursor: 'pointer'
                }}
              >
                ← Back to Edit
              </button>
            </div>
          </div>

          {/* Pricing & Competition */}
          <div>
            <div style={{ 
              backgroundColor: '#ffffff',
              border: '1px solid #e1e5e9',
              borderRadius: '12px',
              padding: '1.5rem',
              marginBottom: '1rem'
            }}>
              <h3 style={{ marginBottom: '1rem' }}>💰 Pricing Analysis</h3>
              
              <div style={{ 
                fontSize: '2rem',
                fontWeight: 'bold',
                color: '#007bff',
                marginBottom: '1rem',
                textAlign: 'center'
              }}>
                ${productData.price.toFixed(2)}
              </div>
              
              <div style={{ 
                backgroundColor: '#d4edda',
                color: '#155724',
                padding: '0.75rem',
                borderRadius: '6px',
                marginBottom: '1rem',
                textAlign: 'center',
                fontSize: '0.9rem'
              }}>
                ✅ Competitively Priced
              </div>
            </div>

            <div style={{ 
              backgroundColor: '#ffffff',
              border: '1px solid #e1e5e9',
              borderRadius: '12px',
              padding: '1.5rem'
            }}>
              <h4 style={{ marginBottom: '1rem' }}>🏪 Competitor Prices</h4>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                {productData.competitors.map((competitor, index) => (
                  <div key={index} style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0.75rem',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '6px'
                  }}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{competitor.store}</div>
                    </div>
                    <div style={{ 
                      fontWeight: 'bold',
                      color: competitor.price < productData.price ? '#dc3545' : 
                             competitor.price > productData.price ? '#28a745' : '#007bff'
                    }}>
                      ${competitor.price.toFixed(2)}
                    </div>
                  </div>
                ))}
              </div>
              
              <div style={{ 
                marginTop: '1rem',
                fontSize: '0.9rem',
                color: '#666',
                textAlign: 'center'
              }}>
                🔄 Updated 5 minutes ago
              </div>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
