'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface PricingMetrics {
  totalProducts: number;
  competitivePrices: number;
  priceAlerts: number;
  avgSavings: number;
  conversionRate: number;
  revenueImpact: number;
}

interface CompetitorInsight {
  store: string;
  avgPriceDiff: number;
  marketShare: number;
  reliability: number;
  lastUpdate: string;
}

export default function PriceDashboardPage() {
  const [metrics, setMetrics] = useState<PricingMetrics>({
    totalProducts: 0,
    competitivePrices: 0,
    priceAlerts: 0,
    avgSavings: 0,
    conversionRate: 0,
    revenueImpact: 0
  });

  const [competitorInsights, setCompetitorInsights] = useState<CompetitorInsight[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call for metrics
    const fetchMetrics = async () => {
      setIsLoading(true);
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setMetrics({
        totalProducts: 70,
        competitivePrices: 52,
        priceAlerts: 1247,
        avgSavings: 47.32,
        conversionRate: 23.8,
        revenueImpact: 156789
      });

      setCompetitorInsights([
        {
          store: 'Amazon',
          avgPriceDiff: -2.3,
          marketShare: 35.2,
          reliability: 98.5,
          lastUpdate: '2 min ago'
        },
        {
          store: 'Best Buy',
          avgPriceDiff: 1.8,
          marketShare: 18.7,
          reliability: 96.2,
          lastUpdate: '5 min ago'
        },
        {
          store: 'Apple Store',
          avgPriceDiff: 0.0,
          marketShare: 12.4,
          reliability: 99.8,
          lastUpdate: '1 min ago'
        },
        {
          store: 'B&H Photo',
          avgPriceDiff: -4.1,
          marketShare: 8.9,
          reliability: 94.7,
          lastUpdate: '8 min ago'
        },
        {
          store: 'Walmart',
          avgPriceDiff: 3.2,
          marketShare: 15.3,
          reliability: 91.2,
          lastUpdate: '12 min ago'
        },
        {
          store: 'Target',
          avgPriceDiff: 2.7,
          marketShare: 9.5,
          reliability: 93.8,
          lastUpdate: '15 min ago'
        }
      ]);

      setIsLoading(false);
    };

    fetchMetrics();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div style={{ 
        maxWidth: '1400px', 
        margin: '0 auto', 
        padding: '2rem',
        textAlign: 'center'
      }}>
        <div style={{ 
          width: '60px', 
          height: '60px', 
          border: '4px solid #007bff',
          borderTop: '4px solid transparent',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          margin: '0 auto 2rem'
        }} />
        <h2>Loading Price Intelligence Dashboard...</h2>
        <p style={{ color: '#666' }}>
          Analyzing competitor data and pricing metrics
        </p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto', padding: '1rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <nav style={{ fontSize: '0.9rem', color: '#666', marginBottom: '1rem' }}>
          <Link href="/admin" style={{ color: '#007bff', textDecoration: 'none' }}>Admin</Link>
          {' > '}
          <span>Price Intelligence Dashboard</span>
        </nav>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '2.5rem', margin: '0 0 0.5rem 0' }}>
              📊 Price Intelligence Dashboard
            </h1>
            <p style={{ color: '#666', fontSize: '1.1rem', margin: 0 }}>
              Real-time competitive analysis and pricing optimization
            </p>
          </div>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '0.75rem 1rem',
            backgroundColor: '#d4edda',
            color: '#155724',
            borderRadius: '8px',
            fontWeight: 'bold'
          }}>
            🟢 Live Monitoring Active
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#007bff', marginBottom: '0.5rem' }}>
            {metrics.totalProducts}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Products Monitored</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Across 6+ retailers</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#28a745', marginBottom: '0.5rem' }}>
            {metrics.competitivePrices}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Competitive Prices</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>
            {Math.round((metrics.competitivePrices / metrics.totalProducts) * 100)}% of catalog
          </div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#ffc107', marginBottom: '0.5rem' }}>
            {metrics.priceAlerts.toLocaleString()}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Active Price Alerts</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Customer notifications</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#17a2b8', marginBottom: '0.5rem' }}>
            ${metrics.avgSavings}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Avg Customer Savings</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>Per transaction</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2.5rem', color: '#6f42c1', marginBottom: '0.5rem' }}>
            {metrics.conversionRate}%
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Conversion Rate</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>+5.2% vs last month</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', color: '#28a745', marginBottom: '0.5rem' }}>
            ${metrics.revenueImpact.toLocaleString()}
          </div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Revenue Impact</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>This month</div>
        </div>
      </div>

      {/* Competitor Analysis */}
      <div style={{ 
        backgroundColor: '#ffffff',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        overflow: 'hidden',
        marginBottom: '2rem'
      }}>
        <div style={{ 
          padding: '1.5rem',
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #e1e5e9'
        }}>
          <h2 style={{ margin: 0 }}>🏪 Competitor Intelligence</h2>
        </div>

        <div style={{ overflowX: 'auto' }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #e1e5e9' }}>Retailer</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Avg Price Diff</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Market Share</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Data Reliability</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Last Update</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Status</th>
              </tr>
            </thead>
            <tbody>
              {competitorInsights.map((competitor, index) => (
                <tr key={index} style={{ borderBottom: '1px solid #e1e5e9' }}>
                  <td style={{ padding: '1rem' }}>
                    <div style={{ fontWeight: 'bold' }}>{competitor.store}</div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ 
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      color: competitor.avgPriceDiff < 0 ? '#dc3545' : 
                             competitor.avgPriceDiff > 0 ? '#28a745' : '#007bff'
                    }}>
                      {competitor.avgPriceDiff > 0 ? '+' : ''}{competitor.avgPriceDiff}%
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ fontSize: '1.1rem', fontWeight: 'bold' }}>
                      {competitor.marketShare}%
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ 
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.25rem 0.75rem',
                      backgroundColor: competitor.reliability > 95 ? '#d4edda' : 
                                     competitor.reliability > 90 ? '#fff3cd' : '#f8d7da',
                      color: competitor.reliability > 95 ? '#155724' : 
                             competitor.reliability > 90 ? '#856404' : '#721c24',
                      borderRadius: '12px',
                      fontSize: '0.9rem',
                      fontWeight: 'bold'
                    }}>
                      {competitor.reliability}%
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>
                      {competitor.lastUpdate}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ 
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.25rem',
                      padding: '0.25rem 0.75rem',
                      backgroundColor: '#d4edda',
                      color: '#155724',
                      borderRadius: '12px',
                      fontSize: '0.8rem',
                      fontWeight: 'bold'
                    }}>
                      🟢 Active
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <div style={{ 
        backgroundColor: '#ffffff',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        padding: '2rem'
      }}>
        <h3 style={{ marginBottom: '1.5rem' }}>⚡ Price Intelligence Actions</h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
          <Link 
            href="/admin/price-match"
            style={{
              display: 'block',
              padding: '1.5rem',
              backgroundColor: '#ffc107',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              textAlign: 'center',
              fontWeight: 'bold'
            }}
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>💰</div>
            Update Pricing Strategy
          </Link>

          <button style={{
            padding: '1.5rem',
            backgroundColor: '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            textAlign: 'center',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📊</div>
            Generate Report
          </button>

          <button style={{
            padding: '1.5rem',
            backgroundColor: '#6f42c1',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            textAlign: 'center',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🔔</div>
            Manage Alerts
          </button>

          <button style={{
            padding: '1.5rem',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            textAlign: 'center',
            fontWeight: 'bold',
            cursor: 'pointer'
          }}>
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>⚙️</div>
            Configure Monitoring
          </button>
        </div>
      </div>
    </div>
  );
}
