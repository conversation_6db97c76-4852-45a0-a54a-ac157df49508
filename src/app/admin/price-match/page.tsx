'use client';

import { useState } from 'react';
import Link from 'next/link';

interface PriceData {
  id: string;
  product: string;
  currentPrice: number;
  suggestedPrice: number;
  competitors: Array<{
    store: string;
    price: number;
    lastUpdated: string;
  }>;
  priceHistory: Array<{
    date: string;
    price: number;
  }>;
  recommendation: 'increase' | 'decrease' | 'maintain';
  potentialImpact: string;
}

export default function PriceMatchPage() {
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Mock price data
  const priceData: PriceData[] = [
    {
      id: '1',
      product: 'iPhone 15 Pro Max',
      currentPrice: 1199.99,
      suggestedPrice: 1189.99,
      competitors: [
        { store: 'Amazon', price: 1189.99, lastUpdated: '2 hours ago' },
        { store: 'Best Buy', price: 1199.00, lastUpdated: '1 hour ago' },
        { store: 'B&H Photo', price: 1179.99, lastUpdated: '30 min ago' },
        { store: 'Walmart', price: 1199.99, lastUpdated: '45 min ago' }
      ],
      priceHistory: [
        { date: '2024-01-01', price: 1199.99 },
        { date: '2024-01-15', price: 1189.99 },
        { date: '2024-02-01', price: 1199.99 },
        { date: '2024-02-15', price: 1179.99 }
      ],
      recommendation: 'decrease',
      potentialImpact: '+15% sales volume, -0.8% margin'
    },
    {
      id: '2',
      product: 'MacBook Air M3',
      currentPrice: 1299.99,
      suggestedPrice: 1299.99,
      competitors: [
        { store: 'Apple Store', price: 1299.99, lastUpdated: '1 hour ago' },
        { store: 'Amazon', price: 1299.99, lastUpdated: '2 hours ago' },
        { store: 'Best Buy', price: 1299.99, lastUpdated: '1 hour ago' },
        { store: 'Costco', price: 1279.99, lastUpdated: '3 hours ago' }
      ],
      priceHistory: [
        { date: '2024-01-01', price: 1299.99 },
        { date: '2024-01-15', price: 1299.99 },
        { date: '2024-02-01', price: 1299.99 },
        { date: '2024-02-15', price: 1299.99 }
      ],
      recommendation: 'maintain',
      potentialImpact: 'Stable pricing, optimal margin'
    },
    {
      id: '3',
      product: 'Sony WH-1000XM5',
      currentPrice: 399.99,
      suggestedPrice: 379.99,
      competitors: [
        { store: 'Amazon', price: 379.99, lastUpdated: '1 hour ago' },
        { store: 'Best Buy', price: 399.99, lastUpdated: '2 hours ago' },
        { store: 'Target', price: 389.99, lastUpdated: '1 hour ago' },
        { store: 'Sony Direct', price: 399.99, lastUpdated: '30 min ago' }
      ],
      priceHistory: [
        { date: '2024-01-01', price: 399.99 },
        { date: '2024-01-15', price: 389.99 },
        { date: '2024-02-01', price: 399.99 },
        { date: '2024-02-15', price: 379.99 }
      ],
      recommendation: 'decrease',
      potentialImpact: '+22% sales volume, -2.1% margin'
    }
  ];

  const handlePriceUpdate = async (productId: string, newPrice: number) => {
    setIsUpdating(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsUpdating(false);
    alert(`Price updated successfully for product ${productId} to $${newPrice}`);
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'increase': return '#28a745';
      case 'decrease': return '#dc3545';
      case 'maintain': return '#007bff';
      default: return '#6c757d';
    }
  };

  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'increase': return '📈';
      case 'decrease': return '📉';
      case 'maintain': return '📊';
      default: return '❓';
    }
  };

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto', padding: '1rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <nav style={{ fontSize: '0.9rem', color: '#666', marginBottom: '1rem' }}>
          <Link href="/admin" style={{ color: '#007bff', textDecoration: 'none' }}>Admin</Link>
          {' > '}
          <span>Price Matching</span>
        </nav>
        <h1 style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>
          💰 AI Price Matching & Optimization
        </h1>
        <p style={{ color: '#666', fontSize: '1.1rem' }}>
          Real-time competitor analysis and intelligent pricing recommendations
        </p>
      </div>

      {/* Summary Cards */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', color: '#dc3545', marginBottom: '0.5rem' }}>📉</div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Price Decrease</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>2 products</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', color: '#007bff', marginBottom: '0.5rem' }}>📊</div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Maintain Price</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>1 product</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', color: '#28a745', marginBottom: '0.5rem' }}>💵</div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Potential Savings</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>$30.00</div>
        </div>

        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '1.5rem',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '2rem', color: '#ffc107', marginBottom: '0.5rem' }}>🔄</div>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Last Update</div>
          <div style={{ color: '#666', fontSize: '0.9rem' }}>5 min ago</div>
        </div>
      </div>

      {/* Price Analysis Table */}
      <div style={{ 
        backgroundColor: '#ffffff',
        border: '1px solid #e1e5e9',
        borderRadius: '12px',
        overflow: 'hidden'
      }}>
        <div style={{ 
          padding: '1.5rem',
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #e1e5e9',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <h2 style={{ margin: 0 }}>📊 Price Analysis Dashboard</h2>
          <button
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            🔄 Refresh Prices
          </button>
        </div>

        <div style={{ overflowX: 'auto' }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #e1e5e9' }}>Product</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Current Price</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Suggested Price</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Recommendation</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Impact</th>
                <th style={{ padding: '1rem', textAlign: 'center', borderBottom: '1px solid #e1e5e9' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {priceData.map((item) => (
                <tr key={item.id} style={{ borderBottom: '1px solid #e1e5e9' }}>
                  <td style={{ padding: '1rem' }}>
                    <div style={{ fontWeight: 'bold' }}>{item.product}</div>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>
                      Lowest competitor: ${Math.min(...item.competitors.map(c => c.price)).toFixed(2)}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
                      ${item.currentPrice.toFixed(2)}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ 
                      fontSize: '1.2rem', 
                      fontWeight: 'bold',
                      color: item.suggestedPrice < item.currentPrice ? '#dc3545' : 
                             item.suggestedPrice > item.currentPrice ? '#28a745' : '#007bff'
                    }}>
                      ${item.suggestedPrice.toFixed(2)}
                    </div>
                    {item.suggestedPrice !== item.currentPrice && (
                      <div style={{ fontSize: '0.8rem', color: '#666' }}>
                        {item.suggestedPrice < item.currentPrice ? '-' : '+'}
                        ${Math.abs(item.suggestedPrice - item.currentPrice).toFixed(2)}
                      </div>
                    )}
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ 
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      backgroundColor: getRecommendationColor(item.recommendation) + '20',
                      color: getRecommendationColor(item.recommendation),
                      borderRadius: '20px',
                      fontSize: '0.9rem',
                      fontWeight: 'bold'
                    }}>
                      {getRecommendationIcon(item.recommendation)}
                      {item.recommendation.toUpperCase()}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>
                      {item.potentialImpact}
                    </div>
                  </td>
                  <td style={{ padding: '1rem', textAlign: 'center' }}>
                    <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center' }}>
                      <button
                        onClick={() => handlePriceUpdate(item.id, item.suggestedPrice)}
                        disabled={isUpdating || item.suggestedPrice === item.currentPrice}
                        style={{
                          padding: '0.5rem 1rem',
                          backgroundColor: item.suggestedPrice === item.currentPrice ? '#6c757d' : '#28a745',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: item.suggestedPrice === item.currentPrice ? 'not-allowed' : 'pointer',
                          fontSize: '0.8rem'
                        }}
                      >
                        {isUpdating ? '⏳' : '✅'} Apply
                      </button>
                      <button
                        onClick={() => setSelectedProduct(selectedProduct === item.id ? null : item.id)}
                        style={{
                          padding: '0.5rem 1rem',
                          backgroundColor: '#007bff',
                          color: 'white',
                          border: 'none',
                          borderRadius: '6px',
                          cursor: 'pointer',
                          fontSize: '0.8rem'
                        }}
                      >
                        📊 Details
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Detailed View */}
      {selectedProduct && (
        <div style={{ 
          marginTop: '2rem',
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '2rem'
        }}>
          {(() => {
            const product = priceData.find(p => p.id === selectedProduct);
            if (!product) return null;

            return (
              <>
                <h3 style={{ marginBottom: '1.5rem' }}>
                  📊 Detailed Analysis: {product.product}
                </h3>
                
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem' }}>
                  {/* Competitor Prices */}
                  <div>
                    <h4 style={{ marginBottom: '1rem' }}>🏪 Competitor Prices</h4>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                      {product.competitors.map((competitor, index) => (
                        <div key={index} style={{ 
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '1rem',
                          backgroundColor: '#f8f9fa',
                          borderRadius: '8px'
                        }}>
                          <div>
                            <div style={{ fontWeight: 'bold' }}>{competitor.store}</div>
                            <div style={{ fontSize: '0.8rem', color: '#666' }}>
                              Updated {competitor.lastUpdated}
                            </div>
                          </div>
                          <div style={{ 
                            fontWeight: 'bold',
                            fontSize: '1.1rem',
                            color: competitor.price < product.currentPrice ? '#dc3545' : 
                                   competitor.price > product.currentPrice ? '#28a745' : '#007bff'
                          }}>
                            ${competitor.price.toFixed(2)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Price History */}
                  <div>
                    <h4 style={{ marginBottom: '1rem' }}>📈 Price History</h4>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                      {product.priceHistory.map((entry, index) => (
                        <div key={index} style={{ 
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '1rem',
                          backgroundColor: '#f8f9fa',
                          borderRadius: '8px'
                        }}>
                          <div style={{ fontWeight: 'bold' }}>
                            {new Date(entry.date).toLocaleDateString()}
                          </div>
                          <div style={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                            ${entry.price.toFixed(2)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </>
            );
          })()}
        </div>
      )}
    </div>
  );
}
