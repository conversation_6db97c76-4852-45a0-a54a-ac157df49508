'use client';

import { useState } from 'react';
import Link from 'next/link';

interface ImportItem {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'success' | 'error';
  price?: number;
  images?: number;
  error?: string;
}

export default function BulkImportPage() {
  const [importText, setImportText] = useState('');
  const [importItems, setImportItems] = useState<ImportItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStep, setCurrentStep] = useState<'input' | 'processing' | 'results'>('input');

  const sampleData = `iPhone 15 Pro Max 256GB
MacBook Air M3 13-inch
Sony WH-1000XM5 Headphones
Samsung Galaxy S24 Ultra
AirPods Pro 2nd Generation
Dell XPS 13 Plus
Nintendo Switch OLED
iPad Pro 12.9-inch M2
Google Pixel 8 Pro
Surface Laptop 5`;

  const processImport = async () => {
    const lines = importText.trim().split('\n').filter(line => line.trim());
    if (lines.length === 0) return;

    setCurrentStep('processing');
    setIsProcessing(true);

    const items: ImportItem[] = lines.map((line, index) => ({
      id: `item-${index}`,
      name: line.trim(),
      status: 'pending'
    }));

    setImportItems(items);

    // Simulate processing each item
    for (let i = 0; i < items.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setImportItems(prev => prev.map((item, index) => {
        if (index === i) {
          // Simulate success/error randomly
          const isSuccess = Math.random() > 0.1; // 90% success rate
          return {
            ...item,
            status: isSuccess ? 'success' : 'error',
            price: isSuccess ? Math.floor(Math.random() * 2000) + 99 : undefined,
            images: isSuccess ? Math.floor(Math.random() * 5) + 1 : undefined,
            error: isSuccess ? undefined : 'Product not found in database'
          };
        } else if (index === i + 1) {
          return { ...item, status: 'processing' };
        }
        return item;
      }));
    }

    setIsProcessing(false);
    setCurrentStep('results');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'processing': return '🔄';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#6c757d';
      case 'processing': return '#007bff';
      case 'success': return '#28a745';
      case 'error': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const successCount = importItems.filter(item => item.status === 'success').length;
  const errorCount = importItems.filter(item => item.status === 'error').length;

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '1rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <nav style={{ fontSize: '0.9rem', color: '#666', marginBottom: '1rem' }}>
          <Link href="/admin" style={{ color: '#007bff', textDecoration: 'none' }}>Admin</Link>
          {' > '}
          <span>Bulk Import</span>
        </nav>
        <h1 style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>
          📊 AI-Powered Bulk Product Import
        </h1>
        <p style={{ color: '#666', fontSize: '1.1rem' }}>
          Import multiple products at once with automatic pricing and image detection
        </p>
      </div>

      {currentStep === 'input' && (
        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '2rem'
        }}>
          <h2 style={{ marginBottom: '1.5rem' }}>📝 Product List Input</h2>
          
          <div style={{ marginBottom: '2rem' }}>
            <label style={{ 
              display: 'block', 
              fontWeight: 'bold', 
              marginBottom: '0.5rem',
              color: '#333'
            }}>
              Product Names (one per line)
            </label>
            <textarea
              value={importText}
              onChange={(e) => setImportText(e.target.value)}
              placeholder="Enter product names, one per line..."
              style={{
                width: '100%',
                minHeight: '200px',
                padding: '1rem',
                border: '2px solid #e1e5e9',
                borderRadius: '8px',
                fontSize: '1rem',
                fontFamily: 'monospace',
                resize: 'vertical'
              }}
            />
            <div style={{ fontSize: '0.9rem', color: '#666', marginTop: '0.5rem' }}>
              💡 Be as specific as possible for better AI recognition (include model numbers, storage, colors, etc.)
            </div>
          </div>

          <div style={{ marginBottom: '2rem' }}>
            <h3 style={{ marginBottom: '1rem' }}>📋 Sample Data</h3>
            <div style={{ 
              backgroundColor: '#f8f9fa',
              border: '1px solid #e1e5e9',
              borderRadius: '8px',
              padding: '1rem',
              fontFamily: 'monospace',
              fontSize: '0.9rem',
              whiteSpace: 'pre-line'
            }}>
              {sampleData}
            </div>
            <button
              onClick={() => setImportText(sampleData)}
              style={{
                marginTop: '0.5rem',
                padding: '0.5rem 1rem',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '0.9rem'
              }}
            >
              📋 Use Sample Data
            </button>
          </div>

          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <button
              onClick={processImport}
              disabled={!importText.trim()}
              style={{
                padding: '1rem 2rem',
                backgroundColor: importText.trim() ? '#007bff' : '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                cursor: importText.trim() ? 'pointer' : 'not-allowed'
              }}
            >
              🚀 Start AI Import Process
            </button>
            
            <div style={{ color: '#666', fontSize: '0.9rem' }}>
              {importText.trim().split('\n').filter(line => line.trim()).length} products ready to import
            </div>
          </div>
        </div>
      )}

      {currentStep === 'processing' && (
        <div style={{ 
          backgroundColor: '#ffffff',
          border: '1px solid #e1e5e9',
          borderRadius: '12px',
          padding: '2rem'
        }}>
          <h2 style={{ marginBottom: '1.5rem' }}>🤖 AI Processing in Progress</h2>
          
          <div style={{ 
            marginBottom: '2rem',
            padding: '1.5rem',
            backgroundColor: '#e3f2fd',
            borderRadius: '8px',
            border: '1px solid #2196f3'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1rem' }}>
              <div style={{ 
                width: '20px', 
                height: '20px', 
                border: '2px solid #2196f3',
                borderTop: '2px solid transparent',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
              <strong>Processing {importItems.length} products...</strong>
            </div>
            <div style={{ fontSize: '0.9rem', color: '#666' }}>
              Our AI is analyzing each product, finding competitive prices, and sourcing high-quality images.
            </div>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            {importItems.map((item, index) => (
              <div key={item.id} style={{ 
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                padding: '1rem',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                border: `2px solid ${getStatusColor(item.status)}20`
              }}>
                <div style={{ 
                  fontSize: '1.5rem',
                  minWidth: '30px',
                  textAlign: 'center'
                }}>
                  {getStatusIcon(item.status)}
                </div>
                
                <div style={{ flex: 1 }}>
                  <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                    {item.name}
                  </div>
                  {item.status === 'success' && (
                    <div style={{ fontSize: '0.9rem', color: '#666' }}>
                      Price: ${item.price} • Images: {item.images} found
                    </div>
                  )}
                  {item.status === 'error' && (
                    <div style={{ fontSize: '0.9rem', color: '#dc3545' }}>
                      Error: {item.error}
                    </div>
                  )}
                  {item.status === 'processing' && (
                    <div style={{ fontSize: '0.9rem', color: '#007bff' }}>
                      Analyzing product specifications and pricing...
                    </div>
                  )}
                </div>
                
                <div style={{ 
                  padding: '0.25rem 0.75rem',
                  backgroundColor: getStatusColor(item.status),
                  color: 'white',
                  borderRadius: '12px',
                  fontSize: '0.8rem',
                  fontWeight: 'bold',
                  textTransform: 'uppercase'
                }}>
                  {item.status}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {currentStep === 'results' && (
        <div>
          {/* Summary */}
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem',
            marginBottom: '2rem'
          }}>
            <div style={{ 
              backgroundColor: '#ffffff',
              border: '1px solid #e1e5e9',
              borderRadius: '12px',
              padding: '1.5rem',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2.5rem', color: '#28a745', marginBottom: '0.5rem' }}>
                {successCount}
              </div>
              <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Successful</div>
              <div style={{ color: '#666', fontSize: '0.9rem' }}>Products imported</div>
            </div>

            <div style={{ 
              backgroundColor: '#ffffff',
              border: '1px solid #e1e5e9',
              borderRadius: '12px',
              padding: '1.5rem',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2.5rem', color: '#dc3545', marginBottom: '0.5rem' }}>
                {errorCount}
              </div>
              <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Failed</div>
              <div style={{ color: '#666', fontSize: '0.9rem' }}>Products with errors</div>
            </div>

            <div style={{ 
              backgroundColor: '#ffffff',
              border: '1px solid #e1e5e9',
              borderRadius: '12px',
              padding: '1.5rem',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2.5rem', color: '#007bff', marginBottom: '0.5rem' }}>
                {Math.round((successCount / importItems.length) * 100)}%
              </div>
              <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Success Rate</div>
              <div style={{ color: '#666', fontSize: '0.9rem' }}>Overall performance</div>
            </div>

            <div style={{ 
              backgroundColor: '#ffffff',
              border: '1px solid #e1e5e9',
              borderRadius: '12px',
              padding: '1.5rem',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '2.5rem', color: '#ffc107', marginBottom: '0.5rem' }}>
                {importItems.reduce((sum, item) => sum + (item.images || 0), 0)}
              </div>
              <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>Images Found</div>
              <div style={{ color: '#666', fontSize: '0.9rem' }}>Total sourced</div>
            </div>
          </div>

          {/* Results Table */}
          <div style={{ 
            backgroundColor: '#ffffff',
            border: '1px solid #e1e5e9',
            borderRadius: '12px',
            overflow: 'hidden'
          }}>
            <div style={{ 
              padding: '1.5rem',
              backgroundColor: '#f8f9fa',
              borderBottom: '1px solid #e1e5e9',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h2 style={{ margin: 0 }}>📊 Import Results</h2>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button
                  onClick={() => {
                    setCurrentStep('input');
                    setImportText('');
                    setImportItems([]);
                  }}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '0.9rem'
                  }}
                >
                  🔄 Import More
                </button>
                <Link
                  href="/admin/products"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#28a745',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '6px',
                    fontSize: '0.9rem'
                  }}
                >
                  📦 View Products
                </Link>
              </div>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column' }}>
              {importItems.map((item) => (
                <div key={item.id} style={{ 
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  padding: '1rem 1.5rem',
                  borderBottom: '1px solid #e1e5e9'
                }}>
                  <div style={{ fontSize: '1.5rem', minWidth: '30px' }}>
                    {getStatusIcon(item.status)}
                  </div>
                  
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                      {item.name}
                    </div>
                    {item.status === 'success' && (
                      <div style={{ fontSize: '0.9rem', color: '#666' }}>
                        Imported with ${item.price} pricing and {item.images} high-quality images
                      </div>
                    )}
                    {item.status === 'error' && (
                      <div style={{ fontSize: '0.9rem', color: '#dc3545' }}>
                        {item.error}
                      </div>
                    )}
                  </div>
                  
                  <div style={{ 
                    padding: '0.25rem 0.75rem',
                    backgroundColor: getStatusColor(item.status),
                    color: 'white',
                    borderRadius: '12px',
                    fontSize: '0.8rem',
                    fontWeight: 'bold',
                    textTransform: 'uppercase'
                  }}>
                    {item.status}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
