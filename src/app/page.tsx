import Link from 'next/link';
import ProductCard from '@/components/ProductCard';

// Comprehensive Electronics Marketplace - Mock data for preview
const mockCategories = [
  { id: '1', name: 'Smartphones', slug: 'smartphones' },
  { id: '2', name: 'Laptops & Computers', slug: 'laptops-computers' },
  { id: '3', name: 'Audio & Headphones', slug: 'audio-headphones' },
  { id: '4', name: 'Gaming & Consoles', slug: 'gaming-consoles' },
  { id: '5', name: 'Tablets & E-readers', slug: 'tablets-ereaders' },
  { id: '6', name: 'Wearables & Fitness', slug: 'wearables-fitness' },
  { id: '7', name: 'Smart Home & IoT', slug: 'smart-home-iot' },
  { id: '8', name: 'Cameras & Photography', slug: 'cameras-photography' },
  { id: '9', name: 'TV & Home Theater', slug: 'tv-home-theater' },
  { id: '10', name: 'Computer Components', slug: 'computer-components' },
  { id: '11', name: 'Networking & WiFi', slug: 'networking-wifi' },
  { id: '12', name: 'Storage & Memory', slug: 'storage-memory' },
  { id: '13', name: 'Monitors & Displays', slug: 'monitors-displays' },
  { id: '14', name: 'Keyboards & Mice', slug: 'keyboards-mice' },
  { id: '15', name: 'Mobile Accessories', slug: 'mobile-accessories' },
  { id: '16', name: 'Power & Charging', slug: 'power-charging' },
  { id: '17', name: 'Drones & RC', slug: 'drones-rc' },
  { id: '18', name: 'VR & AR', slug: 'vr-ar' },
  { id: '19', name: 'Security & Surveillance', slug: 'security-surveillance' },
  { id: '20', name: 'Office Electronics', slug: 'office-electronics' },
];

const mockProducts = [
  // SMARTPHONES & MOBILE
  {
    id: '1',
    title: 'iPhone 15 Pro Max',
    slug: 'iphone-15-pro-max',
    brand: { name: 'Apple' },
    variants: [{ price: 1199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop' }],
  },
  {
    id: '2',
    title: 'iPhone 15 Pro',
    slug: 'iphone-15-pro',
    brand: { name: 'Apple' },
    variants: [{ price: 999.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop' }],
  },
  {
    id: '3',
    title: 'Samsung Galaxy S24 Ultra',
    slug: 'galaxy-s24-ultra',
    brand: { name: 'Samsung' },
    variants: [{ price: 1299.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }],
  },
  {
    id: '4',
    title: 'Samsung Galaxy S24',
    slug: 'galaxy-s24',
    brand: { name: 'Samsung' },
    variants: [{ price: 899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }],
  },
  {
    id: '5',
    title: 'Google Pixel 8 Pro',
    slug: 'google-pixel-8-pro',
    brand: { name: 'Google' },
    variants: [{ price: 999.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }],
  },
  {
    id: '6',
    title: 'OnePlus 12',
    slug: 'oneplus-12',
    brand: { name: 'OnePlus' },
    variants: [{ price: 799.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }],
  },
  {
    id: '7',
    title: 'Xiaomi 14 Ultra',
    slug: 'xiaomi-14-ultra',
    brand: { name: 'Xiaomi' },
    variants: [{ price: 699.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }],
  },

  // LAPTOPS & COMPUTERS
  {
    id: '8',
    title: 'MacBook Pro 16" M3 Max',
    slug: 'macbook-pro-16-m3-max',
    brand: { name: 'Apple' },
    variants: [{ price: 3499.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop' }],
  },
  {
    id: '9',
    title: 'MacBook Air M3',
    slug: 'macbook-air-m3',
    brand: { name: 'Apple' },
    variants: [{ price: 1299.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop' }],
  },
  {
    id: '10',
    title: 'Dell XPS 13 Plus',
    slug: 'dell-xps-13-plus',
    brand: { name: 'Dell' },
    variants: [{ price: 1399.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }],
  },
  {
    id: '11',
    title: 'ThinkPad X1 Carbon',
    slug: 'thinkpad-x1-carbon',
    brand: { name: 'Lenovo' },
    variants: [{ price: 1599.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }],
  },
  {
    id: '12',
    title: 'Surface Laptop 5',
    slug: 'surface-laptop-5',
    brand: { name: 'Microsoft' },
    variants: [{ price: 1299.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }],
  },
  {
    id: '13',
    title: 'ASUS ROG Zephyrus G14',
    slug: 'asus-rog-zephyrus-g14',
    brand: { name: 'ASUS' },
    variants: [{ price: 1899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }],
  },
  {
    id: '14',
    title: 'HP Spectre x360',
    slug: 'hp-spectre-x360',
    brand: { name: 'HP' },
    variants: [{ price: 1199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }],
  },

  // AUDIO & HEADPHONES
  {
    id: '15',
    title: 'AirPods Pro 2',
    slug: 'airpods-pro-2',
    brand: { name: 'Apple' },
    variants: [{ price: 249.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400&h=300&fit=crop' }],
  },
  {
    id: '16',
    title: 'Sony WH-1000XM5',
    slug: 'sony-wh-1000xm5',
    brand: { name: 'Sony' },
    variants: [{ price: 399.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }],
  },
  {
    id: '17',
    title: 'Bose QuietComfort Ultra',
    slug: 'bose-quietcomfort-ultra',
    brand: { name: 'Bose' },
    variants: [{ price: 429.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }],
  },
  {
    id: '18',
    title: 'Sennheiser HD 800S',
    slug: 'sennheiser-hd-800s',
    brand: { name: 'Sennheiser' },
    variants: [{ price: 1699.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }],
  },
  {
    id: '19',
    title: 'Audio-Technica ATH-M50x',
    slug: 'audio-technica-ath-m50x',
    brand: { name: 'Audio-Technica' },
    variants: [{ price: 149.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }],
  },
  {
    id: '20',
    title: 'Beats Studio Pro',
    slug: 'beats-studio-pro',
    brand: { name: 'Beats' },
    variants: [{ price: 349.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }],
  },

  // GAMING & CONSOLES
  {
    id: '21',
    title: 'PlayStation 5',
    slug: 'playstation-5',
    brand: { name: 'Sony' },
    variants: [{ price: 499.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }],
  },
  {
    id: '22',
    title: 'Xbox Series X',
    slug: 'xbox-series-x',
    brand: { name: 'Microsoft' },
    variants: [{ price: 499.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }],
  },
  {
    id: '23',
    title: 'Nintendo Switch OLED',
    slug: 'nintendo-switch-oled',
    brand: { name: 'Nintendo' },
    variants: [{ price: 349.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }],
  },
  {
    id: '24',
    title: 'Steam Deck OLED',
    slug: 'steam-deck-oled',
    brand: { name: 'Valve' },
    variants: [{ price: 549.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }],
  },

  // TABLETS & E-READERS
  {
    id: '25',
    title: 'iPad Pro 12.9" M2',
    slug: 'ipad-pro-12-9-m2',
    brand: { name: 'Apple' },
    variants: [{ price: 1099.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }],
  },
  {
    id: '26',
    title: 'iPad Air 5',
    slug: 'ipad-air-5',
    brand: { name: 'Apple' },
    variants: [{ price: 599.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }],
  },
  {
    id: '27',
    title: 'Samsung Galaxy Tab S9 Ultra',
    slug: 'galaxy-tab-s9-ultra',
    brand: { name: 'Samsung' },
    variants: [{ price: 1199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }],
  },
  {
    id: '28',
    title: 'Microsoft Surface Pro 9',
    slug: 'surface-pro-9',
    brand: { name: 'Microsoft' },
    variants: [{ price: 999.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }],
  },
  {
    id: '29',
    title: 'Kindle Oasis',
    slug: 'kindle-oasis',
    brand: { name: 'Amazon' },
    variants: [{ price: 249.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }],
  },

  // WEARABLES & FITNESS
  {
    id: '30',
    title: 'Apple Watch Ultra 2',
    slug: 'apple-watch-ultra-2',
    brand: { name: 'Apple' },
    variants: [{ price: 799.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }],
  },
  {
    id: '31',
    title: 'Apple Watch Series 9',
    slug: 'apple-watch-series-9',
    brand: { name: 'Apple' },
    variants: [{ price: 399.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }],
  },
  {
    id: '32',
    title: 'Samsung Galaxy Watch 6',
    slug: 'galaxy-watch-6',
    brand: { name: 'Samsung' },
    variants: [{ price: 329.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }],
  },
  {
    id: '33',
    title: 'Garmin Fenix 7X',
    slug: 'garmin-fenix-7x',
    brand: { name: 'Garmin' },
    variants: [{ price: 899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }],
  },
  {
    id: '34',
    title: 'Fitbit Sense 2',
    slug: 'fitbit-sense-2',
    brand: { name: 'Fitbit' },
    variants: [{ price: 299.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }],
  },

  // SMART HOME & IOT
  {
    id: '35',
    title: 'Amazon Echo Studio',
    slug: 'amazon-echo-studio',
    brand: { name: 'Amazon' },
    variants: [{ price: 199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop' }],
  },
  {
    id: '36',
    title: 'Google Nest Hub Max',
    slug: 'google-nest-hub-max',
    brand: { name: 'Google' },
    variants: [{ price: 229.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop' }],
  },
  {
    id: '37',
    title: 'Philips Hue Starter Kit',
    slug: 'philips-hue-starter-kit',
    brand: { name: 'Philips' },
    variants: [{ price: 199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop' }],
  },
  {
    id: '38',
    title: 'Ring Video Doorbell Pro 2',
    slug: 'ring-video-doorbell-pro-2',
    brand: { name: 'Ring' },
    variants: [{ price: 249.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop' }],
  },
  {
    id: '39',
    title: 'Nest Thermostat',
    slug: 'nest-thermostat',
    brand: { name: 'Google' },
    variants: [{ price: 129.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop' }],
  },

  // CAMERAS & PHOTOGRAPHY
  {
    id: '40',
    title: 'Canon EOS R5',
    slug: 'canon-eos-r5',
    brand: { name: 'Canon' },
    variants: [{ price: 3899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop' }],
  },
  {
    id: '41',
    title: 'Sony A7R V',
    slug: 'sony-a7r-v',
    brand: { name: 'Sony' },
    variants: [{ price: 3899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop' }],
  },
  {
    id: '42',
    title: 'Nikon Z9',
    slug: 'nikon-z9',
    brand: { name: 'Nikon' },
    variants: [{ price: 5499.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop' }],
  },
  {
    id: '43',
    title: 'Fujifilm X-T5',
    slug: 'fujifilm-x-t5',
    brand: { name: 'Fujifilm' },
    variants: [{ price: 1699.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop' }],
  },
  {
    id: '44',
    title: 'GoPro Hero 12',
    slug: 'gopro-hero-12',
    brand: { name: 'GoPro' },
    variants: [{ price: 399.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop' }],
  },

  // TV & HOME THEATER
  {
    id: '45',
    title: 'Samsung 65" Neo QLED 8K',
    slug: 'samsung-65-neo-qled-8k',
    brand: { name: 'Samsung' },
    variants: [{ price: 2999.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop' }],
  },
  {
    id: '46',
    title: 'LG 77" OLED C3',
    slug: 'lg-77-oled-c3',
    brand: { name: 'LG' },
    variants: [{ price: 3499.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop' }],
  },
  {
    id: '47',
    title: 'Sony 85" X95L',
    slug: 'sony-85-x95l',
    brand: { name: 'Sony' },
    variants: [{ price: 2799.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop' }],
  },
  {
    id: '48',
    title: 'Sonos Arc Soundbar',
    slug: 'sonos-arc-soundbar',
    brand: { name: 'Sonos' },
    variants: [{ price: 899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop' }],
  },

  // COMPUTER COMPONENTS
  {
    id: '49',
    title: 'NVIDIA RTX 4090',
    slug: 'nvidia-rtx-4090',
    brand: { name: 'NVIDIA' },
    variants: [{ price: 1599.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=400&h=300&fit=crop' }],
  },
  {
    id: '50',
    title: 'AMD Ryzen 9 7950X',
    slug: 'amd-ryzen-9-7950x',
    brand: { name: 'AMD' },
    variants: [{ price: 699.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=400&h=300&fit=crop' }],
  },
  {
    id: '51',
    title: 'Intel Core i9-14900K',
    slug: 'intel-core-i9-14900k',
    brand: { name: 'Intel' },
    variants: [{ price: 589.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=400&h=300&fit=crop' }],
  },
  {
    id: '52',
    title: 'ASUS ROG Strix Z790-E',
    slug: 'asus-rog-strix-z790-e',
    brand: { name: 'ASUS' },
    variants: [{ price: 499.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1591799264318-7e6ef8ddb7ea?w=400&h=300&fit=crop' }],
  },

  // MONITORS & DISPLAYS
  {
    id: '53',
    title: 'Apple Studio Display',
    slug: 'apple-studio-display',
    brand: { name: 'Apple' },
    variants: [{ price: 1599.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop' }],
  },
  {
    id: '54',
    title: 'Dell UltraSharp 32" 4K',
    slug: 'dell-ultrasharp-32-4k',
    brand: { name: 'Dell' },
    variants: [{ price: 899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop' }],
  },
  {
    id: '55',
    title: 'ASUS ProArt PA32UCG',
    slug: 'asus-proart-pa32ucg',
    brand: { name: 'ASUS' },
    variants: [{ price: 2999.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop' }],
  },
  {
    id: '56',
    title: 'Samsung Odyssey G9',
    slug: 'samsung-odyssey-g9',
    brand: { name: 'Samsung' },
    variants: [{ price: 1299.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop' }],
  },

  // KEYBOARDS & MICE
  {
    id: '57',
    title: 'Logitech MX Master 3S',
    slug: 'logitech-mx-master-3s',
    brand: { name: 'Logitech' },
    variants: [{ price: 99.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop' }],
  },
  {
    id: '58',
    title: 'Apple Magic Keyboard',
    slug: 'apple-magic-keyboard',
    brand: { name: 'Apple' },
    variants: [{ price: 199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop' }],
  },
  {
    id: '59',
    title: 'Keychron K8 Pro',
    slug: 'keychron-k8-pro',
    brand: { name: 'Keychron' },
    variants: [{ price: 109.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop' }],
  },
  {
    id: '60',
    title: 'Razer DeathAdder V3',
    slug: 'razer-deathadder-v3',
    brand: { name: 'Razer' },
    variants: [{ price: 89.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop' }],
  },

  // STORAGE & MEMORY
  {
    id: '61',
    title: 'Samsung 990 PRO 2TB SSD',
    slug: 'samsung-990-pro-2tb-ssd',
    brand: { name: 'Samsung' },
    variants: [{ price: 199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?w=400&h=300&fit=crop' }],
  },
  {
    id: '62',
    title: 'WD Black SN850X 4TB',
    slug: 'wd-black-sn850x-4tb',
    brand: { name: 'Western Digital' },
    variants: [{ price: 399.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?w=400&h=300&fit=crop' }],
  },
  {
    id: '63',
    title: 'Corsair Vengeance 32GB DDR5',
    slug: 'corsair-vengeance-32gb-ddr5',
    brand: { name: 'Corsair' },
    variants: [{ price: 149.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1597872200969-2b65d56bd16b?w=400&h=300&fit=crop' }],
  },

  // VR & AR
  {
    id: '64',
    title: 'Meta Quest 3',
    slug: 'meta-quest-3',
    brand: { name: 'Meta' },
    variants: [{ price: 499.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop' }],
  },
  {
    id: '65',
    title: 'Apple Vision Pro',
    slug: 'apple-vision-pro',
    brand: { name: 'Apple' },
    variants: [{ price: 3499.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop' }],
  },
  {
    id: '66',
    title: 'PICO 4 Enterprise',
    slug: 'pico-4-enterprise',
    brand: { name: 'ByteDance' },
    variants: [{ price: 899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop' }],
  },

  // DRONES & RC
  {
    id: '67',
    title: 'DJI Mavic 3 Pro',
    slug: 'dji-mavic-3-pro',
    brand: { name: 'DJI' },
    variants: [{ price: 2199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=400&h=300&fit=crop' }],
  },
  {
    id: '68',
    title: 'DJI Mini 4 Pro',
    slug: 'dji-mini-4-pro',
    brand: { name: 'DJI' },
    variants: [{ price: 759.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=400&h=300&fit=crop' }],
  },

  // POWER & CHARGING
  {
    id: '69',
    title: 'Anker PowerCore 26800',
    slug: 'anker-powercore-26800',
    brand: { name: 'Anker' },
    variants: [{ price: 65.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1609592806596-4d1b5e5e5e5e?w=400&h=300&fit=crop' }],
  },
  {
    id: '70',
    title: 'Apple MagSafe Charger',
    slug: 'apple-magsafe-charger',
    brand: { name: 'Apple' },
    variants: [{ price: 39.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1609592806596-4d1b5e5e5e5e?w=400&h=300&fit=crop' }],
  },
];

export default function Home() {
  // Show first 24 products on homepage for better performance
  const featuredProducts = mockProducts.slice(0, 24);

  return (
    <div style={{ padding: '1rem', maxWidth: '1400px', margin: '0 auto' }}>
      <header style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <h1 style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>ElectroHub - Premium Electronics Marketplace</h1>
        <p style={{ fontSize: '1.2rem', color: '#666' }}>
          🔥 70+ Premium Products | 📱 Smartphones | 💻 Laptops | 🎧 Audio | 🎮 Gaming | 📺 Smart Home | 📷 Cameras | ⌚ Wearables
        </p>
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '1rem',
          borderRadius: '12px',
          margin: '1rem 0',
          fontSize: '1.1rem'
        }}>
          ✨ <strong>Comprehensive Electronics Library</strong> - From $39.99 to $5,499.99 | Free Shipping on Orders $50+
        </div>
      </header>

      <section aria-label="Categories" style={{ marginBottom: '3rem' }}>
        <h2 style={{ fontSize: '1.8rem', marginBottom: '1.5rem', textAlign: 'center' }}>
          🛍️ Shop by Category ({mockCategories.length} Categories)
        </h2>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
          gap: '1rem'
        }}>
          {mockCategories.map((c) => (
            <Link
              key={c.id}
              href={`/category/${c.slug}`}
              aria-label={`Browse ${c.name}`}
              style={{
                padding: '1.2rem',
                border: '2px solid #e1e5e9',
                borderRadius: '12px',
                textAlign: 'center',
                textDecoration: 'none',
                color: '#333',
                backgroundColor: '#ffffff',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                transition: 'all 0.3s ease',
                fontWeight: '500'
              }}
            >
              {c.name}
            </Link>
          ))}
        </div>
      </section>

      <section aria-label="Products">
        <h2 style={{ fontSize: '1.8rem', marginBottom: '1.5rem', textAlign: 'center' }}>
          🌟 Featured Products ({mockProducts.length} Total Products Available)
        </h2>
        <p style={{ textAlign: 'center', color: '#666', marginBottom: '2rem' }}>
          Showing {featuredProducts.length} of {mockProducts.length} premium electronics from top brands
        </p>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '1.5rem'
        }}>
          {featuredProducts.map((p) => (
            <ProductCard key={p.id} product={p} />
          ))}
        </div>

        <div style={{
          textAlign: 'center',
          marginTop: '3rem',
          padding: '2rem',
          backgroundColor: '#f8f9fa',
          borderRadius: '12px'
        }}>
          <h3 style={{ marginBottom: '1rem' }}>Want to see all {mockProducts.length} products?</h3>
          <p style={{ color: '#666', marginBottom: '1.5rem' }}>
            Browse our complete catalog with advanced filtering by category, brand, and price range
          </p>
          <Link
            href="/products"
            style={{
              display: 'inline-block',
              padding: '12px 24px',
              backgroundColor: '#007bff',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '8px',
              fontWeight: 'bold'
            }}
          >
            View All Products →
          </Link>
        </div>
      </section>
    </div>
  );
}
