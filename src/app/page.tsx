import Link from 'next/link';
import ProductCard from '@/components/ProductCard';

// Mock data for preview
const mockCategories = [
  { id: '1', name: 'Smartphones', slug: 'smartphones' },
  { id: '2', name: 'Laptops', slug: 'laptops' },
  { id: '3', name: 'Audio', slug: 'audio' },
  { id: '4', name: 'Gaming', slug: 'gaming' },
  { id: '5', name: 'Accessories', slug: 'accessories' },
  { id: '6', name: 'Tablets', slug: 'tablets' },
  { id: '7', name: 'Wearables', slug: 'wearables' },
  { id: '8', name: 'Smart Home', slug: 'smart-home' },
];

const mockProducts = [
  {
    id: '1',
    title: 'iPhone 15 Pro',
    slug: 'iphone-15-pro',
    brand: { name: 'Apple' },
    variants: [{ price: 999.99 }],
    media: [{ url: 'https://via.placeholder.com/400x300?text=iPhone+15+Pro' }],
  },
  {
    id: '2',
    title: 'MacBook Air M3',
    slug: 'macbook-air-m3',
    brand: { name: 'Apple' },
    variants: [{ price: 1299.99 }],
    media: [{ url: 'https://via.placeholder.com/400x300?text=MacBook+Air' }],
  },
  {
    id: '3',
    title: 'AirPods Pro',
    slug: 'airpods-pro',
    brand: { name: 'Apple' },
    variants: [{ price: 249.99 }],
    media: [{ url: 'https://via.placeholder.com/400x300?text=AirPods+Pro' }],
  },
  {
    id: '4',
    title: 'Samsung Galaxy S24',
    slug: 'galaxy-s24',
    brand: { name: 'Samsung' },
    variants: [{ price: 899.99 }],
    media: [{ url: 'https://via.placeholder.com/400x300?text=Galaxy+S24' }],
  },
];

export default function Home() {
  return (
    <div style={{ padding: '1rem' }}>
      <h1>ElectroHub - Electronics Marketplace</h1>
      <p>Phones · Laptops · Audio · Gaming · Accessories</p>

      <section aria-label="Categories" style={{ marginBottom: '2rem' }}>
        <h2>Shop by Category</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem' }}>
          {mockCategories.map((c) => (
            <Link
              key={c.id}
              href={`/category/${c.slug}`}
              aria-label={`Browse ${c.name}`}
              style={{
                padding: '1rem',
                border: '1px solid #ddd',
                borderRadius: '8px',
                textAlign: 'center',
                textDecoration: 'none',
                color: 'inherit',
                backgroundColor: '#f9f9f9'
              }}
            >
              {c.name}
            </Link>
          ))}
        </div>
      </section>

      <section aria-label="Products">
        <h2>Featured Products</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
          {mockProducts.map((p) => (
            <ProductCard key={p.id} product={p} />
          ))}
        </div>
      </section>
    </div>
  );
}
