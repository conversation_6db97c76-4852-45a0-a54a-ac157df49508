import Link from 'next/link';
import { prisma } from '@/lib/prisma';
import ProductCard from '@/components/ProductCard';

export default async function Home() {
  const categories = await prisma.category.findMany({ orderBy: { name: 'asc' }, take: 8 });
  const products = await prisma.product.findMany({ where: { status: 'PUBLISHED' }, include: { media: true, variants: true, brand: true }, take: 12, orderBy: { createdAt: 'desc' } });
  return (
    <div style={{ padding: '1rem' }}>
      <h1>Electronics-first Marketplace</h1>
      <p>Phones · Laptops · Audio · Gaming · Accessories</p>
      <section aria-label="Categories" style={{ display: 'grid', gridTemplateColumns: 'repeat(4,1fr)', gap: '1rem' }}>
        {categories.map((c) => (
          <Link key={c.id} href={`/category/${c.slug}`} aria-label={`Browse ${c.name}`}>{c.name}</Link>
        ))}
      </section>
      <h2>New Arrivals</h2>
      <section aria-label="Products" style={{ display: 'grid', gridTemplateColumns: 'repeat(4,1fr)', gap: '1rem' }}>
        {products.map((p) => (
          <ProductCard key={p.id} product={p} />
        ))}
      </section>
    </div>
  );
}
