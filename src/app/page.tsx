import Link from 'next/link';
import ProductCard from '@/components/ProductCard';

// Mock data for preview
const mockCategories = [
  { id: '1', name: 'Smartphones', slug: 'smartphones' },
  { id: '2', name: 'Laptops', slug: 'laptops' },
  { id: '3', name: 'Audio', slug: 'audio' },
  { id: '4', name: 'Gaming', slug: 'gaming' },
  { id: '5', name: 'Accessories', slug: 'accessories' },
  { id: '6', name: 'Tablets', slug: 'tablets' },
  { id: '7', name: 'Wearables', slug: 'wearables' },
  { id: '8', name: 'Smart Home', slug: 'smart-home' },
];

const mockProducts = [
  {
    id: '1',
    title: 'iPhone 15 Pro',
    slug: 'iphone-15-pro',
    brand: { name: 'Apple' },
    variants: [{ price: 999.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop' }],
  },
  {
    id: '2',
    title: 'MacBook Air M3',
    slug: 'macbook-air-m3',
    brand: { name: 'Apple' },
    variants: [{ price: 1299.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop' }],
  },
  {
    id: '3',
    title: 'AirPods Pro',
    slug: 'airpods-pro',
    brand: { name: 'Apple' },
    variants: [{ price: 249.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400&h=300&fit=crop' }],
  },
  {
    id: '4',
    title: 'Samsung Galaxy S24',
    slug: 'galaxy-s24',
    brand: { name: 'Samsung' },
    variants: [{ price: 899.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }],
  },
  {
    id: '5',
    title: 'Sony WH-1000XM5',
    slug: 'sony-wh-1000xm5',
    brand: { name: 'Sony' },
    variants: [{ price: 399.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }],
  },
  {
    id: '6',
    title: 'Dell XPS 13',
    slug: 'dell-xps-13',
    brand: { name: 'Dell' },
    variants: [{ price: 1199.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }],
  },
  {
    id: '7',
    title: 'iPad Pro 12.9"',
    slug: 'ipad-pro-12-9',
    brand: { name: 'Apple' },
    variants: [{ price: 1099.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }],
  },
  {
    id: '8',
    title: 'Nintendo Switch OLED',
    slug: 'nintendo-switch-oled',
    brand: { name: 'Nintendo' },
    variants: [{ price: 349.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }],
  },
  {
    id: '9',
    title: 'Apple Watch Series 9',
    slug: 'apple-watch-series-9',
    brand: { name: 'Apple' },
    variants: [{ price: 399.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }],
  },
  {
    id: '10',
    title: 'Samsung 4K Monitor',
    slug: 'samsung-4k-monitor',
    brand: { name: 'Samsung' },
    variants: [{ price: 599.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop' }],
  },
  {
    id: '11',
    title: 'Logitech MX Master 3',
    slug: 'logitech-mx-master-3',
    brand: { name: 'Logitech' },
    variants: [{ price: 99.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop' }],
  },
  {
    id: '12',
    title: 'Google Pixel 8 Pro',
    slug: 'google-pixel-8-pro',
    brand: { name: 'Google' },
    variants: [{ price: 999.99 }],
    media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }],
  },
];

export default function Home() {
  return (
    <div style={{ padding: '1rem' }}>
      <h1>ElectroHub - Electronics Marketplace</h1>
      <p>Phones · Laptops · Audio · Gaming · Accessories</p>

      <section aria-label="Categories" style={{ marginBottom: '2rem' }}>
        <h2>Shop by Category</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem' }}>
          {mockCategories.map((c) => (
            <Link
              key={c.id}
              href={`/category/${c.slug}`}
              aria-label={`Browse ${c.name}`}
              style={{
                padding: '1rem',
                border: '1px solid #ddd',
                borderRadius: '8px',
                textAlign: 'center',
                textDecoration: 'none',
                color: 'inherit',
                backgroundColor: '#f9f9f9'
              }}
            >
              {c.name}
            </Link>
          ))}
        </div>
      </section>

      <section aria-label="Products">
        <h2>Featured Products</h2>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
          {mockProducts.map((p) => (
            <ProductCard key={p.id} product={p} />
          ))}
        </div>
      </section>
    </div>
  );
}
