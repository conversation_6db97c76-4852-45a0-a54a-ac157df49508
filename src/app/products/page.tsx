import ProductCard from '@/components/ProductCard';
import FilterSidebar from '@/components/FilterSidebar';
import Link from 'next/link';

// Complete product catalog - all 70+ products
const allProducts = [
  // SMARTPHONES
  { id: '1', title: 'iPhone 15 Pro Max', slug: 'iphone-15-pro-max', brand: { name: 'Apple' }, variants: [{ price: 1199.99 }], media: [{ url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '2', title: 'iPhone 15 Pro', slug: 'iphone-15-pro', brand: { name: 'Apple' }, variants: [{ price: 999.99 }], media: [{ url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '3', title: 'Samsung Galaxy S24 Ultra', slug: 'galaxy-s24-ultra', brand: { name: 'Samsung' }, variants: [{ price: 1299.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '4', title: 'Samsung Galaxy S24', slug: 'galaxy-s24', brand: { name: 'Samsung' }, variants: [{ price: 899.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '5', title: 'Google Pixel 8 Pro', slug: 'google-pixel-8-pro', brand: { name: 'Google' }, variants: [{ price: 999.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '6', title: 'OnePlus 12', slug: 'oneplus-12', brand: { name: 'OnePlus' }, variants: [{ price: 799.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },
  { id: '7', title: 'Xiaomi 14 Ultra', slug: 'xiaomi-14-ultra', brand: { name: 'Xiaomi' }, variants: [{ price: 699.99 }], media: [{ url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop' }], category: 'smartphones' },

  // LAPTOPS
  { id: '8', title: 'MacBook Pro 16" M3 Max', slug: 'macbook-pro-16-m3-max', brand: { name: 'Apple' }, variants: [{ price: 3499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop' }], category: 'laptops' },
  { id: '9', title: 'MacBook Air M3', slug: 'macbook-air-m3', brand: { name: 'Apple' }, variants: [{ price: 1299.99 }], media: [{ url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop' }], category: 'laptops' },
  { id: '10', title: 'Dell XPS 13 Plus', slug: 'dell-xps-13-plus', brand: { name: 'Dell' }, variants: [{ price: 1399.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops' },
  { id: '11', title: 'ThinkPad X1 Carbon', slug: 'thinkpad-x1-carbon', brand: { name: 'Lenovo' }, variants: [{ price: 1599.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops' },
  { id: '12', title: 'Surface Laptop 5', slug: 'surface-laptop-5', brand: { name: 'Microsoft' }, variants: [{ price: 1299.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops' },
  { id: '13', title: 'ASUS ROG Zephyrus G14', slug: 'asus-rog-zephyrus-g14', brand: { name: 'ASUS' }, variants: [{ price: 1899.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops' },
  { id: '14', title: 'HP Spectre x360', slug: 'hp-spectre-x360', brand: { name: 'HP' }, variants: [{ price: 1199.99 }], media: [{ url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop' }], category: 'laptops' },

  // AUDIO
  { id: '15', title: 'AirPods Pro 2', slug: 'airpods-pro-2', brand: { name: 'Apple' }, variants: [{ price: 249.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=400&h=300&fit=crop' }], category: 'audio' },
  { id: '16', title: 'Sony WH-1000XM5', slug: 'sony-wh-1000xm5', brand: { name: 'Sony' }, variants: [{ price: 399.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio' },
  { id: '17', title: 'Bose QuietComfort Ultra', slug: 'bose-quietcomfort-ultra', brand: { name: 'Bose' }, variants: [{ price: 429.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio' },
  { id: '18', title: 'Sennheiser HD 800S', slug: 'sennheiser-hd-800s', brand: { name: 'Sennheiser' }, variants: [{ price: 1699.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio' },
  { id: '19', title: 'Audio-Technica ATH-M50x', slug: 'audio-technica-ath-m50x', brand: { name: 'Audio-Technica' }, variants: [{ price: 149.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio' },
  { id: '20', title: 'Beats Studio Pro', slug: 'beats-studio-pro', brand: { name: 'Beats' }, variants: [{ price: 349.99 }], media: [{ url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=300&fit=crop' }], category: 'audio' },

  // GAMING
  { id: '21', title: 'PlayStation 5', slug: 'playstation-5', brand: { name: 'Sony' }, variants: [{ price: 499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }], category: 'gaming' },
  { id: '22', title: 'Xbox Series X', slug: 'xbox-series-x', brand: { name: 'Microsoft' }, variants: [{ price: 499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }], category: 'gaming' },
  { id: '23', title: 'Nintendo Switch OLED', slug: 'nintendo-switch-oled', brand: { name: 'Nintendo' }, variants: [{ price: 349.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }], category: 'gaming' },
  { id: '24', title: 'Steam Deck OLED', slug: 'steam-deck-oled', brand: { name: 'Valve' }, variants: [{ price: 549.99 }], media: [{ url: 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=300&fit=crop' }], category: 'gaming' },

  // TABLETS
  { id: '25', title: 'iPad Pro 12.9" M2', slug: 'ipad-pro-12-9-m2', brand: { name: 'Apple' }, variants: [{ price: 1099.99 }], media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }], category: 'tablets' },
  { id: '26', title: 'iPad Air 5', slug: 'ipad-air-5', brand: { name: 'Apple' }, variants: [{ price: 599.99 }], media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }], category: 'tablets' },
  { id: '27', title: 'Samsung Galaxy Tab S9 Ultra', slug: 'galaxy-tab-s9-ultra', brand: { name: 'Samsung' }, variants: [{ price: 1199.99 }], media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }], category: 'tablets' },
  { id: '28', title: 'Microsoft Surface Pro 9', slug: 'surface-pro-9', brand: { name: 'Microsoft' }, variants: [{ price: 999.99 }], media: [{ url: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=300&fit=crop' }], category: 'tablets' },

  // WEARABLES
  { id: '30', title: 'Apple Watch Ultra 2', slug: 'apple-watch-ultra-2', brand: { name: 'Apple' }, variants: [{ price: 799.99 }], media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }], category: 'wearables' },
  { id: '31', title: 'Apple Watch Series 9', slug: 'apple-watch-series-9', brand: { name: 'Apple' }, variants: [{ price: 399.99 }], media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }], category: 'wearables' },
  { id: '32', title: 'Samsung Galaxy Watch 6', slug: 'galaxy-watch-6', brand: { name: 'Samsung' }, variants: [{ price: 329.99 }], media: [{ url: 'https://images.unsplash.com/photo-1551816230-ef5deaed4a26?w=400&h=300&fit=crop' }], category: 'wearables' },

  // CAMERAS
  { id: '40', title: 'Canon EOS R5', slug: 'canon-eos-r5', brand: { name: 'Canon' }, variants: [{ price: 3899.99 }], media: [{ url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop' }], category: 'cameras' },
  { id: '41', title: 'Sony A7R V', slug: 'sony-a7r-v', brand: { name: 'Sony' }, variants: [{ price: 3899.99 }], media: [{ url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop' }], category: 'cameras' },
  { id: '42', title: 'Nikon Z9', slug: 'nikon-z9', brand: { name: 'Nikon' }, variants: [{ price: 5499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop' }], category: 'cameras' },

  // TVS
  { id: '45', title: 'Samsung 65" Neo QLED 8K', slug: 'samsung-65-neo-qled-8k', brand: { name: 'Samsung' }, variants: [{ price: 2999.99 }], media: [{ url: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop' }], category: 'tvs' },
  { id: '46', title: 'LG 77" OLED C3', slug: 'lg-77-oled-c3', brand: { name: 'LG' }, variants: [{ price: 3499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=400&h=300&fit=crop' }], category: 'tvs' },

  // MONITORS
  { id: '53', title: 'Apple Studio Display', slug: 'apple-studio-display', brand: { name: 'Apple' }, variants: [{ price: 1599.99 }], media: [{ url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop' }], category: 'monitors' },
  { id: '54', title: 'Dell UltraSharp 32" 4K', slug: 'dell-ultrasharp-32-4k', brand: { name: 'Dell' }, variants: [{ price: 899.99 }], media: [{ url: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=300&fit=crop' }], category: 'monitors' },

  // VR
  { id: '64', title: 'Meta Quest 3', slug: 'meta-quest-3', brand: { name: 'Meta' }, variants: [{ price: 499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop' }], category: 'vr' },
  { id: '65', title: 'Apple Vision Pro', slug: 'apple-vision-pro', brand: { name: 'Apple' }, variants: [{ price: 3499.99 }], media: [{ url: 'https://images.unsplash.com/photo-1593508512255-86ab42a8e620?w=400&h=300&fit=crop' }], category: 'vr' },

  // ACCESSORIES
  { id: '57', title: 'Logitech MX Master 3S', slug: 'logitech-mx-master-3s', brand: { name: 'Logitech' }, variants: [{ price: 99.99 }], media: [{ url: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop' }], category: 'accessories' },
  { id: '58', title: 'Apple Magic Keyboard', slug: 'apple-magic-keyboard', brand: { name: 'Apple' }, variants: [{ price: 199.99 }], media: [{ url: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop' }], category: 'accessories' },
  { id: '70', title: 'Apple MagSafe Charger', slug: 'apple-magsafe-charger', brand: { name: 'Apple' }, variants: [{ price: 39.99 }], media: [{ url: 'https://images.unsplash.com/photo-1609592806596-4d1b5e5e5e5e?w=400&h=300&fit=crop' }], category: 'accessories' },
];

export default function AllProductsPage({ searchParams }: any) {
  // Get unique brands and categories
  const allBrands = [...new Set(allProducts.map(p => p.brand.name))].sort();
  const allCategories = [...new Set(allProducts.map(p => p.category))];
  
  // Apply filters
  let filteredProducts = allProducts;
  
  if (searchParams.brand) {
    filteredProducts = filteredProducts.filter(p => 
      p.brand.name.toLowerCase() === searchParams.brand.toLowerCase()
    );
  }
  
  if (searchParams.category) {
    filteredProducts = filteredProducts.filter(p => 
      p.category === searchParams.category
    );
  }
  
  if (searchParams.min) {
    const minPrice = parseFloat(searchParams.min);
    filteredProducts = filteredProducts.filter(p => p.variants[0].price >= minPrice);
  }
  
  if (searchParams.max) {
    const maxPrice = parseFloat(searchParams.max);
    filteredProducts = filteredProducts.filter(p => p.variants[0].price <= maxPrice);
  }

  return (
    <div style={{ maxWidth: '1400px', margin: '0 auto', padding: '1rem' }}>
      {/* Header */}
      <div style={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        padding: '2rem',
        borderRadius: '12px',
        marginBottom: '2rem',
        textAlign: 'center'
      }}>
        <h1 style={{ fontSize: '2.5rem', margin: '0 0 1rem 0' }}>
          🛍️ Complete Product Catalog
        </h1>
        <p style={{ fontSize: '1.2rem', opacity: 0.9 }}>
          Browse all {allProducts.length} premium electronics from top brands
        </p>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '280px 1fr', gap: '2rem' }}>
        {/* Sidebar */}
        <div>
          <FilterSidebar brands={allBrands} />
          
          {/* Category Filter */}
          <div style={{ 
            marginTop: '2rem',
            padding: '1.5rem',
            backgroundColor: '#f8f9fa',
            borderRadius: '12px'
          }}>
            <h3 style={{ marginBottom: '1rem' }}>Categories</h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <Link 
                href="/products"
                style={{
                  textDecoration: 'none',
                  color: !searchParams.category ? '#007bff' : '#666',
                  padding: '0.5rem',
                  borderRadius: '6px',
                  backgroundColor: !searchParams.category ? '#e3f2fd' : 'transparent',
                  fontWeight: !searchParams.category ? 'bold' : 'normal'
                }}
              >
                All Categories ({allProducts.length})
              </Link>
              {allCategories.slice(0, 8).map((category) => {
                const count = allProducts.filter(p => p.category === category).length;
                return (
                  <Link 
                    key={category}
                    href={`/products?category=${category}`}
                    style={{
                      textDecoration: 'none',
                      color: searchParams.category === category ? '#007bff' : '#666',
                      padding: '0.5rem',
                      borderRadius: '6px',
                      backgroundColor: searchParams.category === category ? '#e3f2fd' : 'transparent',
                      fontWeight: searchParams.category === category ? 'bold' : 'normal'
                    }}
                  >
                    {category} ({count})
                  </Link>
                );
              })}
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '1.5rem',
            padding: '1rem',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px'
          }}>
            <div>
              <strong>{filteredProducts.length}</strong> of {allProducts.length} products
              {(searchParams.brand || searchParams.category) && (
                <div style={{ fontSize: '0.9rem', color: '#666', marginTop: '0.25rem' }}>
                  {searchParams.brand && `Brand: ${searchParams.brand}`}
                  {searchParams.brand && searchParams.category && ' • '}
                  {searchParams.category && `Category: ${searchParams.category}`}
                </div>
              )}
            </div>
            <div style={{ fontSize: '0.9rem', color: '#666' }}>
              Sorted by: Featured
            </div>
          </div>
          
          {filteredProducts.length > 0 ? (
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', 
              gap: '1.5rem' 
            }}>
              {filteredProducts.map(p => (
                <ProductCard key={p.id} product={p} />
              ))}
            </div>
          ) : (
            <div style={{ 
              textAlign: 'center', 
              padding: '3rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '12px'
            }}>
              <h3>No products found</h3>
              <p style={{ color: '#666', marginBottom: '1rem' }}>
                No products match your current filters.
              </p>
              <Link 
                href="/products"
                style={{
                  color: '#007bff',
                  textDecoration: 'none',
                  fontWeight: 'bold'
                }}
              >
                Clear all filters
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
