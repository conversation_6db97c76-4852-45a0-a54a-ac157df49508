import './globals.css';
import Link from 'next/link';
import { ReactNode } from 'react';
import { SkipLink } from '@/components/SkipLink';

export const metadata = {
  title: 'ElectroHub — Electronics Marketplace',
  description: 'Multi-vendor electronics marketplace: phones, laptops, audio, gaming, accessories.',
  metadataBase: new URL(process.env.NEXTAUTH_URL || 'http://localhost:3000'),
  icons: { icon: '/favicon.ico' }
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body>
        <SkipLink />
        <header aria-label="Site header" style={{ padding: '1rem', borderBottom: '1px solid #eee' }}>
          <nav aria-label="Main">
            <Link href="/">ElectroHub</Link> | <Link href="/cart">Cart</Link>
          </nav>
        </header>
        <main id="main" style={{ minHeight: '60vh' }}>{children}</main>
        <footer style={{ padding: '2rem', borderTop: '1px solid #eee' }}>
          <small>© {new Date().getFullYear()} ElectroHub · <Link href="/">Home</Link></small>
        </footer>
      </body>
    </html>
  );
}
